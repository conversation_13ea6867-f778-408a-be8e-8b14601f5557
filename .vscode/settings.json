{
  "editor.formatOnSave": false,
  "[json]": {
    "editor.formatOnSave": false
  },
  "[javascript]": {
    "editor.formatOnSave": true
  },
  "[next]": {
    // "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[typescript]": {
    "editor.formatOnSave": true
  },
  "[graphql]": {
    "editor.formatOnSave": true
  },
  "[typescriptreact]": {
    "editor.formatOnSave": true
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  }
}
