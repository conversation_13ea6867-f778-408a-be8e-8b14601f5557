module.exports = [
  {
    ignores: ['node_modules', '.next', 'public', '.yarn'],
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    languageOptions: {
      parser: require('@typescript-eslint/parser'),
      parserOptions: {
        ecmaVersion: 2015, // ES6
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    plugins: {
      '@typescript-eslint': require('@typescript-eslint/eslint-plugin'),
      react: require('eslint-plugin-react'),
      next: require('@next/eslint-plugin-next'),
      import: require('eslint-plugin-import'),
      jsxA11y: require('eslint-plugin-jsx-a11y'),
      tailwindcss: require('eslint-plugin-tailwindcss/lib'),
      tanstackQuery: require('@tanstack/eslint-plugin-query'),
      'react-hooks': require('eslint-plugin-react-hooks'),
    },
    rules: {
      '@typescript-eslint/camelcase': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-member-accessibility': 'off',
      '@typescript-eslint/indent': 'off',
      '@typescript-eslint/member-delimiter-style': 'off',
      '@typescript-eslint/no-inferrable-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/no-use-before-define': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
        },
      ],
      'react/react-in-jsx-scope': 'off',
      'react/display-name': 'off',
      'react/prop-types': 'off',
      'no-console': [
        'error',
        {
          allow: ['warn', 'error', 'log', 'time', 'timeEnd', 'timeLog'],
        },
      ],
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
]
