{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "downlevelIteration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext", "ES2015"], "module": "esnext", "moduleResolution": "bundler", "noEmit": true, "noUnusedLocals": true, "paths": {"~/*": ["./*"], "$cells/*": ["cells/*"], "$components/*": ["components/*"], "$containers/*": ["containers/*"], "$generated/*": ["generated/*"], "$lib/*": ["lib/*"], "$stores/*": ["stores/*"], "$styles/*": ["styles/*"], "$types/*": ["types/*"], "$utils/*": ["utils/*"]}, "resolveJsonModule": true, "skipLibCheck": true, "strict": false, "target": "es5", "typeRoots": ["./@types", "./node_modules/@types"], "plugins": [{"name": "next"}, {"name": "@0no-co/graphqlsp", "schema": "./src/generated/index.ts", "template": "gql", "shouldCheckForColocatedFragments": true, "trackFieldUsage": true}], "strictNullChecks": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "public", "node_modules/video.js/dist/video.cjs.js"]}