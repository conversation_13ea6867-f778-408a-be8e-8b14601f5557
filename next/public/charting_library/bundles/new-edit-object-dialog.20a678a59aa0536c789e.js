(window.webpackJsonp = window.webpackJsonp || []).push([["new-edit-object-dialog"], {
  "+ByK": function(e, t, n) {
    e.exports = {
      itemWrap: "itemWrap-3qF9ynvx",
      item: "item-112BZuXZ",
      icon: "icon-2y6cSg4c",
      selected: "selected-3tUrY97Z",
      label: "label-1uw3rZaL",
    };
  },
  "/YRR": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M5.5 16.5l5-5a1.414 1.414 0 0 1 2 0m11-1l-5 5a1.414 1.414 0 0 1-2 0\"/><path fill=\"currentColor\" d=\"M14 5h1v2h-1zM14 10h1v2h-1zM14 15h1v2h-1zM14 20h1v2h-1z\"/></svg>";
  },
  "01Ho": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M14.354 6.646L14 6.293l-.354.353-7 7-.353.354.353.354 7 7 .354.353.354-.353 7-7 .353-.354-.353-.354-7-7z\"/></svg>";
  },
  "4Njr": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M14 21l7.424-6.114a.5.5 0 0 0-.318-.886H18.5V7h-9v7H6.894a.5.5 0 0 0-.318.886L14 21z\"/></svg>";
  },
  "4ZyK": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M8.5 22v-5.5m0 0v-8L12 7l4 2.5 3.5-1v8l-3.5 1-4-2.5-3.5 1.5z\"/></svg>";
  },
  "4pMH": function(e, t, n) {},
  "5ijr": function(e) {
    e.exports = JSON.parse(
      "{\"switcherWrapper\":\"switcherWrapper-1wFH-_jm\",\"size-small\":\"size-small-1gT-kZYO\",\"size-large\":\"size-large-MOSirnj_\",\"intent-select\":\"intent-select-2kut8F29\",\"switcherThumbWrapper\":\"switcherThumbWrapper-2u191lDO\",\"input\":\"input-J7QIcTTo\",\"switcherTrack\":\"switcherTrack-2XruDVTa\",\"intent-default\":\"intent-default-3soo5rvS\",\"switcherThumb\":\"switcherThumb-2yuEucci\",\"focus\":\"focus-uZMRkCO0\"}",
    );
  },
  "9FXF": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M6.5 12.5v8h3v-8h-3zM12.5 7.5v13h3v-13h-3zM18.5 15.5v5h3v-5h-3z\"/></svg>";
  },
  CHgb: function(e, t, n) {
    "use strict";
    n.d(t, "c", function() {
      return u;
    }),
      n.d(t, "a", function() {
        return d;
      }),
      n.d(t, "b", function() {
        return h;
      });
    var r = n("mrSG"),
      a = n("q1tI"),
      o = n.n(a),
      l = n("TSYQ"),
      i = n.n(l),
      s = n("H172"),
      c = n("Iivm"),
      p = n("+ByK");
    function u(e) {
      var t = e.menuItemClassName, n = Object(r.__rest)(e, ["menuItemClassName"]);
      return o.a.createElement(s.a, Object(r.__assign)({}, n, { menuItemClassName: i()(t, p.itemWrap) }));
    }
    function d(e) {
      return o.a.createElement(
        "div",
        { className: i()(p.item, p.selected) },
        o.a.createElement(c.Icon, { className: p.icon, icon: e.icon }),
      );
    }
    function h(e) {
      return o.a.createElement(
        "div",
        { className: p.item },
        o.a.createElement(c.Icon, { className: i()(p.icon, e.iconClassName), icon: e.icon }),
        o.a.createElement("div", { className: p.label }, e.label),
      );
    }
  },
  D2im: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path stroke=\"currentColor\" d=\"M17 8.5h7M20.5 12V5M10 19.5h7M13.5 23v-7M3 12.5h7M6.5 16V9\"/></svg>";
  },
  Dj0x: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M11 18.5h-.5V8.793l.146-.147 3-3L14 5.293l.354.353 3 3 .146.147V18.5H11z\"/></svg>";
  },
  FzLb: function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", { value: !0 }), n("mrSG").__exportStar(n("j3s+"), t);
  },
  HWhk: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path fill=\"currentColor\" fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z\"/></svg>";
  },
  J4oI: function(e, t, n) {
    e.exports = { lineStyleSelect: "lineStyleSelect-1s1ap44b" };
  },
  KacW: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return v;
    });
    var r = n("mrSG"),
      a = (n("YFKU"), n("q1tI")),
      o = n.n(a),
      l = n("TSYQ"),
      i = n.n(l),
      s = n("8Uy/"),
      c = n("CHgb"),
      p = n("bQEj"),
      u = n("UXdH"),
      d = n("ZSM+"),
      h = n("J4oI"),
      m = [{ type: s.LINESTYLE_SOLID, icon: p, label: window.t("Line") }, {
        type: s.LINESTYLE_DASHED,
        icon: u,
        label: window.t("Dashed Line"),
      }, { type: s.LINESTYLE_DOTTED, icon: d, label: window.t("Dotted Line") }];
    var v = function(e) {
      function t() {
        return null !== e && e.apply(this, arguments) || this;
      }
      return Object(r.__extends)(t, e),
        t.prototype.render = function() {
          var e,
            t,
            n = this.props,
            a = n.lineStyle,
            l = n.className,
            s = n.lineStyleChange,
            p = n.disabled,
            u = n.additionalItems,
            d = n.allowedLineStyles,
            v = (e = d,
              t = Object(r.__spreadArrays)(m),
              void 0 !== e && (t = t.filter(function(t) {
                return e.includes(t.type);
              })),
              t.map(function(e) {
                return {
                  value: e.type,
                  selectedContent: o.a.createElement(c.a, { icon: e.icon }),
                  content: o.a.createElement(c.b, { icon: e.icon, label: e.label }),
                };
              }));
          return u && (v = Object(r.__spreadArrays)([{ readonly: !0, content: u }], v)),
            o.a.createElement(c.c, {
              disabled: p,
              className: i()(h.lineStyleSelect, l),
              hideArrowButton: !0,
              items: v,
              value: a,
              onChange: s,
              "data-name": "line-style-select",
            });
        },
        t;
    }(o.a.PureComponent);
  },
  Ly1u: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M7.5 7.5h13v13h-13z\"/></svg>";
  },
  MB0Y: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return u;
    }),
      n.d(t, "b", function() {
        return d;
      });
    var r = n("mrSG"),
      a = n("q1tI"),
      o = n.n(a),
      l = n("TSYQ"),
      i = n.n(l),
      s = n("FzLb"),
      c = n("QpNh"),
      p = n("OP2o"),
      u = p;
    function d(e) {
      var t = e.className,
        n = e.checked,
        a = e.id,
        l = e.label,
        u = e.labelDescription,
        d = e.value,
        h = e.preventLabelHighlight,
        m = e.reference,
        v = e.switchReference,
        y = e.theme,
        f = void 0 === y ? p : y,
        b = i()(f.label, n && !h && f.labelOn),
        g = i()(t, f.wrapper, n && f.wrapperWithOnLabel);
      return o.a.createElement(
        "label",
        { className: g, htmlFor: a, ref: m },
        o.a.createElement(
          "div",
          { className: f.labelRow },
          o.a.createElement("div", { className: b }, l),
          u && o.a.createElement("div", { className: f.labelHint }, u),
        ),
        o.a.createElement(
          s.Switch,
          Object(r.__assign)({
            className: f.switch,
            reference: v,
            checked: n,
            onChange: function(t) {
              var n = t.target.checked;
              void 0 !== e.onChange && e.onChange(n);
            },
            value: d,
            tabIndex: -1,
            id: a,
          }, Object(c.a)(e)),
        ),
      );
    }
  },
  OP2o: function(e, t, n) {
    e.exports = {
      wrapper: "wrapper-3Sj-FzgR",
      hovered: "hovered-1G0yygIe",
      labelRow: "labelRow-3h7cSJ_L",
      label: "label-3iLxp29M",
      labelHint: "labelHint-3qxeiVfa",
      labelOn: "labelOn-10QGwv2n",
    };
  },
  UXdH: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z\"/></svg>";
  },
  UXjO: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return u;
    });
    var r = n("mrSG"),
      a = n("q1tI"),
      o = n.n(a),
      l = n("TSYQ"),
      i = n.n(l),
      s = n("H172"),
      c = n("QpNh"),
      p = n("z1Uu");
    function u(e) {
      var t,
        n = e.fontSize,
        a = e.fontSizes,
        l = void 0 === a ? [] : a,
        u = e.className,
        d = e.disabled,
        h = e.fontSizeChange;
      return o.a.createElement(
        s.a,
        Object(r.__assign)({
          disabled: d,
          className: i()(u, p.defaultSelect),
          menuClassName: p.defaultSelect,
          items: (t = l,
            t.map(function(e) {
              return { value: e.value, content: e.title };
            })),
          value: n,
          onChange: h,
        }, Object(c.a)(e)),
      );
    }
  },
  V1YL: function(e, t, n) {
    e.exports = { recalculateCheckbox: "recalculateCheckbox-1Xa1TR7D", descriptionCell: "descriptionCell-3oIbGAm4" };
  },
  W7Dn: function(e, t, n) {
    e.exports = { scrollable: "scrollable-mKj9lAM_" };
  },
  Y5hB: function(e, t, n) {
    "use strict";
    n.r(t);
    var r = n("mrSG"),
      a = (n("YFKU"), n("i8i4")),
      o = n("q1tI"),
      l = n.n(o),
      i = n("Eyy1"),
      s = (n("bSeV"), n("CLNU")),
      c = n("Vdly"),
      p = n("Kxc7"),
      u = n("FQhm"),
      d = n("JWMC"),
      h = n("aDg1"),
      m = n("vHME"),
      v = n("ycFu"),
      y = n("tWVy"),
      f = n("tmL0"),
      b = n("3ClC"),
      g = n("W7Dn"),
      w = function(e) {
        function t(t) {
          var n = e.call(this, t) || this;
          n._renderFooterLeft = function(e) {
            var t = n.props, r = t.source, a = t.model;
            if (Object(b.isStudy)(r)) {
              return o.createElement(m.a, { model: a, source: r, mode: e ? "compact" : "normal" });
            }
            throw new TypeError("Unsupported source type.");
          },
            n._handleSelect = function(e) {
              n.setState({ activeTabId: e }, function() {
                n._requestResize && n._requestResize();
              }), n.props.onActiveTabChanged && n.props.onActiveTabChanged(e);
            },
            n._handleScroll = function() {
              y.a.fire();
            },
            n._handleSubmit = function() {
              n.props.onSubmit(), n.props.onClose();
            };
          var r = n.props, a = r.pages, l = r.initialActiveTab;
          return n.state = { activeTabId: a.allIds.includes(l) ? l : a.allIds[0] }, n;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props, t = e.title, n = e.onCancel, r = e.onClose, a = this.state.activeTabId;
            return o.createElement(v.a, {
              dataName: "indicator-properties-dialog",
              title: t,
              isOpened: !0,
              onSubmit: this._handleSubmit,
              onCancel: n,
              onClickOutside: r,
              onClose: r,
              footerLeftRenderer: this._renderFooterLeft,
              render: this._renderChildren(a),
              submitOnEnterKey: !1,
            });
          },
          t.prototype._renderChildren = function(e) {
            var t = this;
            return function(n) {
              var r = n.requestResize;
              t._requestResize = r;
              var a = t.props, l = a.pages, i = a.source, s = a.model, c = l.byId[e];
              "Component" in c || c.page;
              return o.createElement(
                o.Fragment,
                null,
                o.createElement(h.a, { activeTabId: e, onSelect: t._handleSelect, tabs: l }),
                o.createElement(
                  f.a,
                  { className: g.scrollable, onScroll: t._handleScroll },
                  "Component" in c && o.createElement(c.Component, { source: i, model: s }),
                ),
              );
            };
          },
          t;
      }(o.PureComponent),
      _ = n("PjdP"),
      C = n("HfwS"),
      S = n("HGyE"),
      E = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.input,
              n = e.value,
              a = e.onChange,
              l = e.onBlur,
              i = e.onKeyDown,
              s = t.options.reduce(function(e, t) {
                return e[t] = "NONE" === t ? window.t("Default") : t, e;
              }, {}),
              c = Object(r.__assign)(Object(r.__assign)({}, t), { optionsTitles: s });
            return o.createElement(S.b, { input: c, value: n, onChange: a, onBlur: l, onKeyDown: i });
          },
          t;
      }(o.PureComponent),
      x = Object(C.a)(E),
      P = n("h5Dg"),
      O = n("rJEJ"),
      j = n("XDrA"),
      k = n("+8gn"),
      L = n("Q+1u"),
      T = (n("HbRj"), o.createContext(null)),
      M = window.t("{currency} per order"),
      N = window.t("{currency} per contract"),
      I = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e,
              t = this.props.input,
              n = Object(i.ensureNotNull)(this.context),
              a =
                ((e = {}).percent = "%",
                  e.cash_per_order = M.format({ currency: n }),
                  e.cash_per_contract = N.format({ currency: n }),
                  e),
              l = Object(r.__assign)(Object(r.__assign)({}, t), { optionsTitles: a });
            return o.createElement(S.a, { input: l });
          },
          t.contextType = T,
          t;
      }(o.PureComponent),
      z = window.t("Contracts"),
      D = window.t("% of equity"),
      V = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e,
              t = this.props.input,
              n = Object(i.ensureNotNull)(this.context),
              a = ((e = {}).fixed = z, e.cash_per_order = n, e.percent_of_equity = D, e),
              l = Object(r.__assign)(Object(r.__assign)({}, t), { optionsTitles: a });
            return o.createElement(S.a, { input: l });
          },
          t.contextType = T,
          t;
      }(o.PureComponent),
      R = n("V1YL"),
      B = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props.inputs;
            return o.createElement(
              L.a,
              null,
              o.createElement(
                O.a,
                { label: window.t("Initial capital") },
                o.createElement(_.a, { input: e.initial_capital }),
              ),
              o.createElement(O.a, { label: window.t("Base currency") }, o.createElement(x, { input: e.currency })),
              o.createElement(
                O.a,
                { label: window.t("Order size"), labelAlign: "adaptive" },
                o.createElement(
                  j.a,
                  null,
                  o.createElement(_.a, { input: e.default_qty_value }),
                  o.createElement(V, { input: e.default_qty_type }),
                ),
              ),
              o.createElement(
                O.a,
                { label: window.t("Pyramiding") },
                o.createElement("span", null, o.createElement(_.a, { input: e.pyramiding })),
                o.createElement(
                  "span",
                  { className: R.descriptionCell },
                  window.t("orders", { context: "Pyramiding: count orders" }),
                ),
              ),
              o.createElement(L.a.Separator, null),
              o.createElement(
                O.a,
                { label: window.t("Commission"), labelAlign: "adaptive" },
                o.createElement(
                  j.a,
                  null,
                  o.createElement(_.a, { input: e.commission_value }),
                  o.createElement(I, { input: e.commission_type }),
                ),
              ),
              o.createElement(
                O.a,
                { label: window.t("Verify Price for Limit Orders") },
                o.createElement("span", null, o.createElement(_.a, { input: e.backtest_fill_limits_assumption })),
                o.createElement(
                  "span",
                  { className: R.descriptionCell },
                  window.t("ticks", { context: "slippage ... ticks" }),
                ),
              ),
              o.createElement(
                O.a,
                { label: window.t("Slippage") },
                o.createElement("span", null, o.createElement(_.a, { input: e.slippage })),
                o.createElement(
                  "span",
                  { className: R.descriptionCell },
                  window.t("ticks", { context: "slippage ... ticks" }),
                ),
              ),
              o.createElement(L.a.Separator, null),
              o.createElement(
                O.a,
                { label: window.t("Recalculate"), labelAlign: "top" },
                o.createElement(
                  "div",
                  null,
                  o.createElement(
                    "div",
                    { className: R.recalculateCheckbox },
                    o.createElement(P.a, { label: window.t("After Order is Filled"), input: e.calc_on_order_fills }),
                  ),
                  o.createElement(
                    "div",
                    { className: R.recalculateCheckbox },
                    o.createElement(P.a, {
                      label: window.t("On Every Tick"),
                      input: e.calc_on_every_tick,
                    }),
                  ),
                ),
              ),
            );
          },
          t.contextType = k.b,
          t;
      }(o.PureComponent);
    function H(e) {
      var t = e.property, n = e.model, r = e.inputs, a = e.study;
      return o.createElement(k.a, { property: t.inputs, model: n, study: a }, o.createElement(B, { inputs: r }));
    }
    var A,
      F = n("z61+"),
      W = n("txPx"),
      G = Object(W.getLogger)("Platform.GUI.PropertyDialog.Indicators.StrategyPage"),
      U = function(e) {
        function t(t) {
          var n = e.call(this, t) || this;
          n._handleWatchedDataChange = function() {
            n.setState({ currency: n._getCurrency() });
          };
          var r = n.props.source;
          if (n._source = r, !Object(b.isStudy)(n._source)) throw new TypeError("Strategy page works only for study.");
          n._properties = r.properties();
          var a = r.metaInfo(), o = new F.a(a);
          return n._inputs = o.getStrategyProperties(), n.state = { currency: n._getCurrency() }, n;
        }
        return Object(r.__extends)(t, e),
          t.prototype.componentDidMount = function() {
            this._source.watchedData.subscribe(this._handleWatchedDataChange);
          },
          t.prototype.componentWillUnmount = function() {
            this._source.watchedData.unsubscribe(this._handleWatchedDataChange);
          },
          t.prototype.render = function() {
            return o.createElement(
              T.Provider,
              { value: this.state.currency },
              o.createElement(H, {
                inputs: this._inputs,
                property: this._properties,
                model: this.props.model,
                study: this.props.source,
              }),
            );
          },
          t.prototype._getCurrency = function() {
            var e = this._source.reportData();
            return null === e || void 0 === e.currency
              ? (void 0 !== this.state && null === this.state.currency
                || G.logWarn("Can't obtain currency from strategy report"),
                null)
              : e.currency;
          },
          t;
      }(o.PureComponent),
      Y = n("5Ssy"),
      q = function(e) {
        function t(t) {
          var n = e.call(this, t) || this;
          return n._properties = n.props.source.properties(),
            n._inputs = new F.a(n.props.source.metaInfo()).getUserEditableInputs(),
            n;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            return o.createElement(Y.a, {
              property: this._properties,
              model: this.props.model,
              study: this.props.source,
              inputs: this._inputs,
            });
          },
          t;
      }(o.PureComponent),
      Q = n("23IT"),
      K = n("0YCj"),
      J = n.n(K),
      X = n("Z1Tk"),
      Z = n("S0KV"),
      $ = window.t("Change Visibility"),
      ee = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onChange = function(e) {
            var n = t.context.setValue, r = t.props.visible;
            r && Object(Z.b)(r, function(t) {
              return n(t, e, $);
            });
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.id,
              n = e.title,
              r = e.visible,
              a = e.disabled,
              l = Object(s.clean)(window.t(n, { context: "input" }), !0);
            return o.createElement(P.b, {
              label: l,
              disabled: a,
              input: { id: t, type: "bool", defval: !0, name: "visible" },
              value: !r || Object(Z.a)(r),
              onChange: this._onChange,
            });
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      te = n("KKsp"),
      ne = n("MB0Y"),
      re = n("CHgb"),
      ae = n("xHjM"),
      oe = n("/YRR"),
      le = n("rlj/"),
      ie = n("ZtdB"),
      se = n("D2im"),
      ce = n("tH7p"),
      pe = n("tQCG"),
      ue = n("9FXF"),
      de = n("sPU+"),
      he = ((A = {})[Q.LineStudyPlotStyle.Line] = {
        type: Q.LineStudyPlotStyle.Line,
        order: 0,
        icon: ae,
        label: window.t("Line"),
      },
        A[Q.LineStudyPlotStyle.LineWithBreaks] = {
          type: Q.LineStudyPlotStyle.LineWithBreaks,
          order: 1,
          icon: oe,
          label: window.t("Line With Breaks"),
        },
        A[Q.LineStudyPlotStyle.StepLine] = {
          type: Q.LineStudyPlotStyle.StepLine,
          order: 2,
          icon: le,
          label: window.t("Step Line"),
        },
        A[Q.LineStudyPlotStyle.Histogram] = {
          type: Q.LineStudyPlotStyle.Histogram,
          order: 3,
          icon: ie,
          label: window.t("Histogram"),
        },
        A[Q.LineStudyPlotStyle.Cross] = {
          type: Q.LineStudyPlotStyle.Cross,
          order: 4,
          icon: se,
          label: window.t("Cross", { context: "chart_type" }),
        },
        A[Q.LineStudyPlotStyle.Area] = {
          type: Q.LineStudyPlotStyle.Area,
          order: 5,
          icon: ce,
          label: window.t("Area"),
        },
        A[Q.LineStudyPlotStyle.AreaWithBreaks] = {
          type: Q.LineStudyPlotStyle.AreaWithBreaks,
          order: 6,
          icon: pe,
          label: window.t("Area With Breaks"),
        },
        A[Q.LineStudyPlotStyle.Columns] = {
          type: Q.LineStudyPlotStyle.Columns,
          order: 7,
          icon: ue,
          label: window.t("Columns"),
        },
        A[Q.LineStudyPlotStyle.Circles] = {
          type: Q.LineStudyPlotStyle.Circles,
          order: 8,
          icon: de,
          label: window.t("Circles"),
        },
        A),
      me = Object.values(he).sort(function(e, t) {
        return e.order - t.order;
      }).map(function(e) {
        return {
          value: e.type,
          selectedContent: l.a.createElement(re.a, { icon: e.icon }),
          content: l.a.createElement(re.b, { icon: e.icon, label: e.label }),
        };
      }),
      ve = window.t("Price Line"),
      ye = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.plotType,
              n = e.className,
              a = e.priceLine,
              o = e.plotTypeChange,
              i = e.priceLineChange,
              s = e.disabled,
              c = {
                readonly: !0,
                content: l.a.createElement(
                  l.a.Fragment,
                  null,
                  l.a.createElement(ne.b, {
                    id: "PlotTypePriceLineSwitch",
                    checked: a,
                    label: ve,
                    preventLabelHighlight: !0,
                    value: "priceLineSwitcher",
                    onChange: i,
                  }),
                  l.a.createElement(te.a, null),
                ),
              };
            return l.a.createElement(re.c, {
              disabled: s,
              className: n,
              hideArrowButton: !0,
              items: Object(r.__spreadArrays)([c], me),
              value: t,
              onChange: o,
            });
          },
          t;
      }(l.a.PureComponent),
      fe = n("lkVX"),
      be = n("wwEg"),
      ge = window.t("Change Plot Type"),
      we = window.t("Change Price Line"),
      _e = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onPlotTypeChange = function(e) {
            var n = t.context.setValue, r = t.props.styleProp.plottype;
            r && n(r, e, ge);
          },
            t._onPriceLineChange = function(e) {
              var n = t.context.setValue, r = t.props.styleProp.trackPrice;
              r && n(r, e, we);
            },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.paletteColor,
              n = e.paletteColorProps,
              r = e.styleProp,
              a = e.isLine,
              l = e.hasPlotTypeSelect,
              i = e.grouped,
              s = n.childs();
            return o.createElement(
              O.a,
              {
                grouped: i,
                label: o.createElement(
                  "div",
                  { className: be.childRowContainer },
                  window.t(t.name, { context: "input" }),
                ),
              },
              o.createElement(fe.a, {
                disabled: !r.visible.value(),
                color: s.color,
                transparency: r.transparency,
                thickness: a ? s.width : void 0,
                isPaletteColor: !0,
              }),
              a && l && r.plottype && r.trackPrice
                ? o.createElement(ye, {
                  disabled: !r.visible.value(),
                  className: be.smallStyleControl,
                  plotType: r.plottype.value(),
                  priceLine: r.trackPrice.value(),
                  plotTypeChange: this._onPlotTypeChange,
                  priceLineChange: this._onPriceLineChange,
                })
                : null,
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent);
    var Ce = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.plot,
              n = e.area,
              r = e.palette,
              a = e.paletteProps,
              l = e.hideVisibilitySwitch,
              s = e.styleProp,
              c = t ? t.id : Object(i.ensureDefined)(n).id,
              p = !c.startsWith("fill") && t && Object(Q.isLinePlot)(t);
            return o.createElement(
              o.Fragment,
              null,
              !l
                && o.createElement(
                  L.a.Row,
                  null,
                  o.createElement(
                    L.a.Cell,
                    { placement: "first", colSpan: 2, grouped: !0 },
                    o.createElement(ee, { id: c, title: n ? n.title : s.title.value(), visible: s.visible }),
                  ),
                ),
              function(e, t, n, r) {
                var a = e.colors, l = t.colors;
                return Object.keys(a).map(function(e, t) {
                  return o.createElement(_e, {
                    key: e,
                    grouped: !0,
                    paletteColor: Object(i.ensureDefined)(a[e]),
                    paletteColorProps: Object(i.ensureDefined)(l[e]),
                    styleProp: n,
                    isLine: r,
                    hasPlotTypeSelect: 0 === t,
                  });
                });
              }(r, a, s, p),
              o.createElement(L.a.GroupSeparator, null),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      Se = window.t("Change Plot Type"),
      Ee = window.t("Change Price Line"),
      xe = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onPlotTypeChange = function(e) {
            var n = t.context.setValue, r = t.props.property.plottype;
            r && n(r, e, Se);
          },
            t._onPriceLineChange = function(e) {
              var n = t.context.setValue, r = t.props.property.trackPrice;
              r && n(r, e, Ee);
            },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.id,
              n = e.property,
              r = n.title,
              a = n.color,
              l = n.plottype,
              i = n.linewidth,
              s = n.transparency,
              c = n.trackPrice,
              p = n.visible;
            return o.createElement(
              O.a,
              { label: o.createElement(ee, { id: t, title: r.value(), visible: p }) },
              o.createElement(fe.a, { disabled: !p.value(), color: a, transparency: s, thickness: i }),
              o.createElement(ye, {
                disabled: !p.value(),
                className: be.smallStyleControl,
                plotType: l.value(),
                priceLine: c.value(),
                plotTypeChange: this._onPlotTypeChange,
                priceLineChange: this._onPriceLineChange,
              }),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      Pe = o.createContext(null),
      Oe = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.id,
              n = e.property,
              r = n.colorup,
              a = n.colordown,
              l = n.transparency,
              s = n.visible;
            return o.createElement(Pe.Consumer, null, function(e) {
              return o.createElement(
                O.a,
                { label: o.createElement(ee, { id: t, title: je(Object(i.ensureNotNull)(e), t), visible: s }) },
                o.createElement(fe.a, { disabled: !s.value(), color: r, transparency: l }),
                o.createElement(
                  "span",
                  { className: be.additionalSelect },
                  o.createElement(fe.a, { disabled: !s.value(), color: a, transparency: l }),
                ),
              );
            });
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent);
    function je(e, t) {
      var n = Object(i.ensureDefined)(e.metaInfo().styles), r = Object(i.ensureDefined)(n[t]).title;
      return Object(i.ensureDefined)(r);
    }
    var ke,
      Le,
      Te = n("/SnT"),
      Me = n.n(Te),
      Ne = n("TSYQ"),
      Ie = n.n(Ne),
      ze = n("3G1X"),
      De = n("H172"),
      Ve = n("972a"),
      Re = ((ke = {})[Ve.MarkLocation.AboveBar] = {
        value: Ve.MarkLocation.AboveBar,
        content: window.t("Above Bar"),
        order: 0,
      },
        ke[Ve.MarkLocation.BelowBar] = { value: Ve.MarkLocation.BelowBar, content: window.t("Below Bar"), order: 1 },
        ke[Ve.MarkLocation.Top] = { value: Ve.MarkLocation.Top, content: window.t("Top"), order: 2 },
        ke[Ve.MarkLocation.Bottom] = { value: Ve.MarkLocation.Bottom, content: window.t("Bottom"), order: 3 },
        ke[Ve.MarkLocation.Absolute] = { value: Ve.MarkLocation.Absolute, content: window.t("Absolute"), order: 4 },
        ke),
      Be = Object.values(Re).sort(function(e, t) {
        return e.order - t.order;
      }),
      He = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.shapeLocation,
              n = e.className,
              r = e.menuItemClassName,
              a = e.shapeLocationChange,
              l = e.disabled;
            return o.createElement(De.a, {
              disabled: l,
              className: n,
              menuItemClassName: r,
              items: Be,
              value: t,
              onChange: a,
            });
          },
          t;
      }(o.PureComponent),
      Ae = window.t("Change Char"),
      Fe = window.t("Change Location"),
      We = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onCharChange = function(e) {
            var n = t.context.setValue,
              r = e.currentTarget.value.trim(),
              a = Me()(r),
              o = 0 === a.length ? "" : a[a.length - 1];
            n(t.props.property.char, o, Ae);
          },
            t._onLocationChange = function(e) {
              (0, t.context.setValue)(t.props.property.location, e, Fe);
            },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.id,
              n = e.property,
              r = n.title,
              a = n.color,
              l = n.transparency,
              i = n.char,
              s = n.location,
              c = n.visible,
              p = e.hasPalette;
            return o.createElement(
              O.a,
              { grouped: p, label: o.createElement(ee, { id: t, title: r.value(), visible: c }) },
              !p && o.createElement(fe.a, { disabled: !c.value(), color: a, transparency: l }),
              o.createElement(ze.a, {
                disabled: !c.value(),
                className: be.smallStyleControl,
                value: i.value(),
                onChange: this._onCharChange,
              }),
              o.createElement(He, {
                disabled: !c.value(),
                className: Ne(be.defaultSelect, be.additionalSelect),
                menuItemClassName: be.defaultSelectItem,
                shapeLocation: s.value(),
                shapeLocationChange: this._onLocationChange,
              }),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      Ge = n("Nu4p"),
      Ue = n("4Njr"),
      Ye = n("lOpG"),
      qe = n("br6c"),
      Qe = n("m+Gx"),
      Ke = n("01Ho"),
      Je = n("4ZyK"),
      Xe = n("kMtk"),
      Ze = n("Dj0x"),
      $e = n("Ly1u"),
      et = n("leq5"),
      tt = n("flzi"),
      nt = n("iB0j"),
      rt =
        ((Le = {}).arrow_down = Ue,
          Le.arrow_up = Ye,
          Le.circle = qe,
          Le.cross = Qe,
          Le.diamond = Ke,
          Le.flag = Je,
          Le.label_down = Xe,
          Le.label_up = Ze,
          Le.square = $e,
          Le.triangle_down = et,
          Le.triangle_up = tt,
          Le.x_cross = nt,
          Le);
    function at(e) {
      return rt[e];
    }
    var ot = [];
    Object.keys(Ge.plotShapesData).forEach(function(e) {
      var t = Ge.plotShapesData[e];
      ot.push({
        value: t.id,
        selectedContent: l.a.createElement(re.a, { icon: at(t.icon) }),
        content: l.a.createElement(re.b, { icon: at(t.icon), label: t.guiName }),
      });
    });
    var lt = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props, t = e.shapeStyleId, n = e.className, r = e.shapeStyleChange, a = e.disabled;
            return l.a.createElement(re.c, {
              disabled: a,
              className: n,
              hideArrowButton: !0,
              items: ot,
              value: t,
              onChange: r,
            });
          },
          t;
      }(l.a.PureComponent),
      it = window.t("Change Shape"),
      st = window.t("Change Location"),
      ct = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onPlotTypeChange = function(e) {
            (0, t.context.setValue)(t.props.property.plottype, e, it);
          },
            t._onLocationChange = function(e) {
              (0, t.context.setValue)(t.props.property.location, e, st);
            },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.id,
              n = e.hasPalette,
              r = e.property,
              a = r.title,
              l = r.color,
              i = r.transparency,
              s = r.plottype,
              c = r.location,
              p = r.visible;
            return o.createElement(
              O.a,
              { grouped: n, label: o.createElement(ee, { id: t, title: a.value(), visible: p }) },
              !n && o.createElement(fe.a, { disabled: !p.value(), color: l, transparency: i }),
              o.createElement(lt, {
                disabled: !p.value(),
                className: be.smallStyleControl,
                shapeStyleId: s.value(),
                shapeStyleChange: this._onPlotTypeChange,
              }),
              o.createElement(He, {
                disabled: !p.value(),
                className: Ne(be.defaultSelect, be.additionalSelect),
                menuItemClassName: be.defaultSelectItem,
                shapeLocation: c.value(),
                shapeLocationChange: this._onLocationChange,
              }),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      pt = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.id,
              n = e.title,
              r = e.visible,
              a = e.color,
              l = e.transparency,
              i = e.thickness,
              s = e.children,
              c = e.switchable,
              p = void 0 === c || c;
            return o.createElement(
              O.a,
              { label: p ? o.createElement(ee, { id: t, title: n, visible: r }) : n },
              o.createElement(fe.a, {
                disabled: r && !(Array.isArray(r) ? r[0].value() : r.value()),
                color: a,
                transparency: l,
                thickness: i,
              }),
              s,
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      ut = Object(W.getLogger)("Chart.Study.PropertyPage"),
      dt = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.plot,
              n = e.palette,
              r = e.paletteProps,
              a = e.study,
              l = t.id,
              s = a.properties().styles[l],
              c = t.type;
            if ("line" === c || "bar_colorer" === c || "bg_colorer" === c) {
              return n && r
                ? o.createElement(Ce, { plot: t, palette: n, paletteProps: r, styleProp: s })
                : o.createElement(xe, { id: l, property: s });
            }
            if ("arrows" === c) return o.createElement(Oe, { id: l, property: s });
            if ("chars" === c || "shapes" === c) {
              return o.createElement(
                o.Fragment,
                null,
                "chars" === c
                  ? o.createElement(We, { id: l, property: s, hasPalette: Boolean(n) })
                  : o.createElement(ct, { id: l, property: s, hasPalette: Boolean(n) }),
                n && r
                  && o.createElement(Ce, {
                    plot: t,
                    palette: n,
                    paletteProps: r,
                    hideVisibilitySwitch: !0,
                    styleProp: s,
                  }),
              );
            }
            if (Object(Q.isOhlcPlot)(t)) {
              var p = t.target,
                u = Object(i.ensureDefined)(a.metaInfo().defaults.ohlcPlots)[p],
                d = a.properties().ohlcPlots[p],
                h = void 0;
              h = n && r
                ? o.createElement(Ce, { plot: t, palette: n, paletteProps: r, styleProp: d })
                : o.createElement(pt, {
                  id: p,
                  title: d.title.value(),
                  color: d.color,
                  visible: d.visible,
                  transparency: d.transparency,
                });
              var m = void 0;
              return void 0 !== u && Object(Q.isOhlcPlotStyleCandles)(u)
                && (m = o.createElement(
                  o.Fragment,
                  null,
                  o.createElement(pt, {
                    id: p,
                    title: window.t("Wick"),
                    visible: d.drawWick,
                    color: d.wickColor,
                    transparency: d.transparency,
                  }),
                  o.createElement(pt, {
                    id: p,
                    title: window.t("Border"),
                    visible: d.drawBorder,
                    color: d.borderColor,
                    transparency: d.transparency,
                  }),
                )),
                o.createElement(o.Fragment, null, h, m);
            }
            return ut.logError("Unknown plot type: " + c), null;
          },
          t;
      }(o.PureComponent),
      ht = n("YS4w"),
      mt = n("KacW"),
      vt = window.t("Change Line Style"),
      yt = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onLineStyleChange = function(e) {
            (0, t.context.setValue)(t.props.lineStyle, e, vt);
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props, t = e.lineStyle, n = Object(r.__rest)(e, ["lineStyle"]);
            return l.a.createElement(
              mt.a,
              Object(r.__assign)({}, n, { lineStyle: t.value(), lineStyleChange: this._onLineStyleChange }),
            );
          },
          t.contextType = X.b,
          t;
      }(l.a.PureComponent),
      ft = window.t("Change Value"),
      bt = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onValueChange = function(e) {
            (0, t.context.setValue)(t.props.property.value, e, ft);
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.id,
              n = e.property,
              r = n.name,
              a = n.color,
              l = n.linestyle,
              i = n.linewidth,
              s = n.transparency,
              c = n.value,
              p = n.visible;
            return o.createElement(
              O.a,
              { labelAlign: "adaptive", label: o.createElement(ee, { id: t, title: r.value(), visible: p }) },
              o.createElement(
                "div",
                { className: be.block },
                o.createElement(
                  "div",
                  { className: be.group },
                  o.createElement(fe.a, { disabled: !p.value(), color: a, transparency: s, thickness: i }),
                  o.createElement(yt, { disabled: !p.value(), className: be.smallStyleControl, lineStyle: l }),
                ),
                o.createElement(
                  "div",
                  { className: Ne(be.wrapGroup, be.defaultSelect, be.additionalSelect) },
                  o.createElement(ht.b, {
                    input: { id: "", name: "", type: "float", defval: 0 },
                    value: c.value(),
                    disabled: !p.value(),
                    onChange: this._onValueChange,
                  }),
                ),
              ),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      gt = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props.orders, t = e.visible, n = e.showLabels, r = e.showQty;
            return o.createElement(
              o.Fragment,
              null,
              o.createElement(
                L.a.Row,
                null,
                o.createElement(
                  L.a.Cell,
                  { placement: "first", colSpan: 2 },
                  o.createElement(ee, { id: "chart-orders-switch", title: window.t("Trades on Chart"), visible: t }),
                ),
              ),
              o.createElement(
                L.a.Row,
                null,
                o.createElement(
                  L.a.Cell,
                  { placement: "first", colSpan: 2 },
                  o.createElement(ee, {
                    id: "chart-orders-labels-switch",
                    title: window.t("Signal Labels"),
                    visible: n,
                  }),
                ),
              ),
              o.createElement(
                L.a.Row,
                null,
                o.createElement(
                  L.a.Cell,
                  { placement: "first", colSpan: 2 },
                  o.createElement(ee, { id: "chart-orders-qty-switch", title: window.t("Quantity"), visible: r }),
                ),
              ),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      wt = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props.title,
              t = this.props.property.childs(),
              n = t.color,
              r = t.transparency,
              a = t.width,
              l = t.style,
              i = t.visible;
            return o.createElement(
              O.a,
              { label: o.createElement(ee, { id: e.value(), title: e.value(), visible: i }) },
              o.createElement(fe.a, { disabled: !i.value(), color: n, transparency: r, thickness: a }),
              o.createElement(yt, { disabled: !i.value(), className: be.smallStyleControl, lineStyle: l }),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      _t = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props,
              t = e.graphicType,
              n = e.study,
              r = n.metaInfo().graphics,
              a = n.properties().graphics,
              l = Object(i.ensureDefined)(r[t]);
            return Object.keys(l).map(function(e, n) {
              var r = a[t][e];
              return "horizlines" === t || "vertlines" === t || "lines" === t
                ? o.createElement(wt, { key: e, title: "lines" === t ? r.title : r.name, property: r })
                : null;
            });
          },
          t;
      }(o.PureComponent),
      Ct = window.t("Change Font"),
      St = ["Verdana", "Courier New", "Times New Roman", "Arial"].map(function(e) {
        return { value: e, content: e };
      }),
      Et = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onFontFamilyChange = function(e) {
            (0, t.context.setValue)(t.props.fontFamily, e, Ct);
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props, t = e.fontFamily, n = e.className, r = e.disabled;
            return o.createElement(De.a, {
              disabled: r,
              className: Ie()(n, be.defaultSelect),
              menuItemClassName: be.defaultSelectItem,
              items: St,
              value: t.value(),
              onChange: this._onFontFamilyChange,
            });
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      xt = n("UXjO"),
      Pt = window.t("Change Font Size"),
      Ot = [10, 11, 12, 14, 16, 20, 24, 28, 32, 40].map(function(e) {
        return { value: e, title: e.toString() };
      }),
      jt = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onFontSizeChange = function(e) {
            (0, t.context.setValue)(t.props.fontSize, e, Pt);
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props, t = e.fontSize, n = Object(r.__rest)(e, ["fontSize"]);
            return o.createElement(
              xt.a,
              Object(r.__assign)({}, n, { fontSizes: Ot, fontSize: t.value(), fontSizeChange: this._onFontSizeChange }),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      kt = window.t("Change Visibility"),
      Lt = window.t("Labels Font"),
      Tt = window.t("Show Labels"),
      Mt = {
        Traditional: new Set(["S5/R5", "S4/R4", "S3/R3", "S2/R2", "S1/R1", "P"]),
        Fibonacci: new Set(["S3/R3", "S2/R2", "S1/R1", "P"]),
        Woodie: new Set(["S4/R4", "S3/R3", "S2/R2", "S1/R1", "P"]),
        Classic: new Set(["S4/R4", "S3/R3", "S2/R2", "S1/R1", "P"]),
        DM: new Set(["S1/R1", "P"]),
        DeMark: new Set(["S1/R1", "P"]),
        Camarilla: new Set(["S4/R4", "S3/R3", "S2/R2", "S1/R1", "P"]),
      },
      Nt = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onChange = function(e) {
            (0, t.context.setValue)(t.props.property.childs().levelsStyle.childs().showLabels, e, kt);
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props.property.childs(), t = e.font, n = e.fontsize, r = e.levelsStyle;
            return l.a.createElement(
              l.a.Fragment,
              null,
              l.a.createElement(
                O.a,
                { labelAlign: "adaptive", label: l.a.createElement("span", null, Lt) },
                l.a.createElement(
                  "div",
                  { className: be.block },
                  l.a.createElement("div", { className: be.group }, l.a.createElement(Et, { fontFamily: t })),
                  l.a.createElement(
                    "div",
                    { className: Ne(be.wrapGroup, be.additionalSelect) },
                    l.a.createElement(jt, { fontSize: n }),
                  ),
                ),
              ),
              l.a.createElement(
                L.a.Row,
                null,
                l.a.createElement(
                  L.a.Cell,
                  { placement: "first", colSpan: 2 },
                  l.a.createElement(P.b, {
                    label: Tt,
                    input: { id: "ShowLabels", type: "bool", defval: !0, name: "visible" },
                    value: r.childs().showLabels.value(),
                    onChange: this._onChange,
                  }),
                ),
              ),
              this._renderColors(),
            );
          },
          t.prototype._renderColors = function() {
            var e = this.props.property.childs(),
              t = e.levelsStyle,
              n = e.inputs,
              r = t.childs(),
              a = r.colors,
              o = r.widths,
              s = r.visibility,
              c = n.childs().kind,
              p = Object(i.ensureDefined)(Mt[c.value()]);
            return a.childNames().filter(function(e) {
              return p.has(e);
            }).map(function(e) {
              return l.a.createElement(pt, {
                key: e,
                id: e,
                title: e,
                color: a.childs()[e],
                visible: s.childs()[e],
                thickness: o.childs()[e],
              });
            });
          },
          t.contextType = X.b,
          t;
      }(l.a.PureComponent);
    for (
      var It = n("KJt4"),
        zt = {
          PivotPointsStandard: function() {
            var e = Object(i.ensureNotNull)(Object(o.useContext)(Pe)).properties();
            return l.a.createElement(Nt, { property: e });
          },
        },
        Dt = function(e) {
          function t() {
            return null !== e && e.apply(this, arguments) || this;
          }
          return Object(r.__extends)(t, e),
            t.prototype.render = function() {
              var e = this, t = Object(i.ensureNotNull)(this.context);
              return o.createElement(Pe.Consumer, null, function(n) {
                return o.createElement(
                  X.a,
                  { property: Object(i.ensureNotNull)(n).properties(), model: t },
                  o.createElement(L.a, null, e._renderCustomContent(Object(i.ensureNotNull)(n).metaInfo().shortId)),
                );
              });
            },
            t.prototype._renderCustomContent = function(e) {
              if (e in zt) {
                var t = zt[e];
                return o.createElement(t, null);
              }
              return null;
            },
            t.contextType = It.a,
            t;
        }(o.PureComponent),
        Vt = n("Ecpn"),
        Rt = window.t("Default"),
        Bt = window.t("Precision"),
        Ht = window.t("Change Precision"),
        At = [{ value: "default", content: Rt }],
        Ft = 0;
      Ft <= 8;
      Ft++
    ) At.push({ value: Ft, content: Ft.toString() });
    for (
      var Wt = function(e) {
          function t() {
            var t = null !== e && e.apply(this, arguments) || this;
            return t._onChange = function(e) {
              (0, t.context.setValue)(t.props.precision, e, Ht);
            },
              t;
          }
          return Object(r.__extends)(t, e),
            t.prototype.render = function() {
              var e = this.props.precision;
              return o.createElement(
                O.a,
                { label: Bt },
                o.createElement(De.a, {
                  className: be.defaultSelect,
                  menuItemClassName: be.defaultSelectItem,
                  items: At,
                  value: e.value(),
                  onChange: this._onChange,
                }),
              );
            },
            t.contextType = X.b,
            t;
        }(o.PureComponent),
        Gt = window.t("Default"),
        Ut = window.t("Override Min Tick"),
        Yt = window.t("Change Min Tick"),
        qt = [
          { priceScale: 1, minMove: 1, frac: !1 },
          { priceScale: 10, minMove: 1, frac: !1 },
          { priceScale: 100, minMove: 1, frac: !1 },
          { priceScale: 1e3, minMove: 1, frac: !1 },
          { priceScale: 1e4, minMove: 1, frac: !1 },
          { priceScale: 1e5, minMove: 1, frac: !1 },
          { priceScale: 1e6, minMove: 1, frac: !1 },
          { priceScale: 1e7, minMove: 1, frac: !1 },
          { priceScale: 1e8, minMove: 1, frac: !1 },
          { priceScale: 2, minMove: 1, frac: !0 },
          { priceScale: 4, minMove: 1, frac: !0 },
          { priceScale: 8, minMove: 1, frac: !0 },
          { priceScale: 16, minMove: 1, frac: !0 },
          { priceScale: 32, minMove: 1, frac: !0 },
          { priceScale: 64, minMove: 1, frac: !0 },
          { priceScale: 128, minMove: 1, frac: !0 },
          { priceScale: 320, minMove: 1, frac: !0 },
        ],
        Qt = [{ value: "default", content: Gt }],
        Kt = 0;
      Kt < qt.length;
      Kt++
    ) {
      var Jt = qt[Kt];
      Qt.push({ value: Jt.priceScale + "," + Jt.minMove + "," + Jt.frac, content: Jt.minMove + "/" + Jt.priceScale });
    }
    var Xt = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._onChange = function(e) {
            (0, t.context.setValue)(t.props.minTick, e, Yt);
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props.minTick;
            return o.createElement(
              O.a,
              { label: Ut },
              o.createElement(De.a, {
                className: be.defaultSelect,
                menuItemClassName: be.defaultSelectItem,
                items: Qt,
                value: e.value(),
                onChange: this._onChange,
              }),
            );
          },
          t.contextType = X.b,
          t;
      }(o.PureComponent),
      Zt = n("5YG5"),
      $t = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._findPlotPalette = function(e) {
            var n = t.props.study, r = n.metaInfo(), a = Object(i.ensureDefined)(r.palettes);
            return Object(Q.isBarColorerPlot)(e) || Object(Q.isBgColorerPlot)(e)
              ? { palette: a[e.palette], paletteProps: n.properties().palettes[e.palette] }
              : t._findPaletteByTargetId(e.id);
          },
            t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            var e = this, t = this.props.study, n = t.metaInfo();
            if (Object(Vt.a)(n.shortId)) return o.createElement(Dt, null);
            var a = new F.a(n).getUserEditablePlots(),
              l = t.properties(),
              s = l.bands,
              c = l.bandsBackground,
              p = l.areaBackground,
              u = l.precision,
              d = l.strategy,
              h = l.minTick,
              m = n.filledAreas,
              v = n.graphics,
              y = a.length > 0,
              f = Object(Zt.a)(t).canOverrideMinTick();
            return o.createElement(
              L.a,
              null,
              a.map(function(n) {
                var a = Object(Q.isOhlcPlot)(n) ? Object(r.__assign)(Object(r.__assign)({}, n), { id: n.target }) : n,
                  l = e._findPlotPalette(a),
                  i = l.palette,
                  s = l.paletteProps;
                return o.createElement(dt, { key: n.id, plot: n, palette: i, paletteProps: s, study: t });
              }),
              s && s.childNames().map(function(e, t) {
                var n = s.child(e);
                if (!n.isHidden || !n.isHidden.value()) {
                  return o.createElement(bt, { key: t, id: n.name.value(), property: n });
                }
              }),
              c
                && o.createElement(pt, {
                  id: "bandsBackground",
                  title: "Background",
                  visible: c.fillBackground,
                  color: c.backgroundColor,
                  transparency: c.transparency,
                }),
              p
                && o.createElement(pt, {
                  id: "areaBackground",
                  title: "Background",
                  visible: p.fillBackground,
                  color: p.backgroundColor,
                  transparency: p.transparency,
                }),
              m && m.map(function(n) {
                if (!n.isHidden) {
                  var r = t.properties().filledAreasStyle[n.id], a = n.title || "Background";
                  if (n.palette) {
                    var l = e._findPaletteByTargetId(n.id);
                    return o.createElement(Ce, {
                      key: n.id,
                      area: n,
                      palette: Object(i.ensureDefined)(l.palette),
                      paletteProps: Object(i.ensureDefined)(l.paletteProps),
                      styleProp: r,
                    });
                  }
                  return o.createElement(pt, {
                    key: n.id,
                    id: n.id,
                    title: a,
                    color: r.color,
                    visible: r.visible,
                    transparency: r.transparency,
                  });
                }
              }),
              v && Object.keys(v).map(function(e, n) {
                return o.createElement(_t, { key: e, graphicType: e, study: t });
              }),
              y && o.createElement(Wt, { precision: u }),
              f && o.createElement(Xt, { minTick: h }),
              J.a.isScriptStrategy(n) && o.createElement(gt, { orders: d.orders }),
            );
          },
          t.prototype._findPaletteByTargetId = function(e) {
            for (
              var t = this.props.study,
                n = t.metaInfo(),
                r = n.plots,
                a = Object(i.ensureDefined)(n.palettes),
                o = 0,
                l = r;
              o < l.length;
              o++
            ) {
              var s = l[o];
              if ((Object(Q.isColorerPlot)(s) || Object(Q.isOhlcColorerPlot)(s)) && s.target === e) {
                return { palette: a[s.palette], paletteProps: t.properties().palettes[s.palette] };
              }
            }
            return {};
          },
          t;
      }(o.PureComponent);
    function en(e) {
      return Object(X.c)($t, Object(r.__assign)(Object(r.__assign)({}, e), { property: e.study.properties() }));
    }
    var tn = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.render = function() {
            return o.createElement(
              It.a.Provider,
              { value: this.props.model },
              o.createElement(
                Pe.Provider,
                { value: this.props.source },
                o.createElement(en, { study: this.props.source }),
              ),
            );
          },
          t;
      }(o.PureComponent),
      nn = n("CW80");
    n.d(t, "EditObjectDialogRenderer", function() {
      return rn;
    });
    var rn = function() {
      function e(e, t, n, r) {
        var o = this;
        this._container = document.createElement("div"),
          this._isVisible = !1,
          this._timeout = null,
          this._handleClose = function() {
            a.unmountComponentAtNode(o._container),
              o._isVisible = !1,
              o._subscription.unsubscribe(o, o._handleCollectionChanged);
          },
          this._handleCancel = function() {
            o._model.undoToCheckpoint(o._checkpoint);
          },
          this._handleSubmit = function() {},
          this._handleActiveTabChanged = function(e) {
            c.setValue(o._activeTabSettingsName(), e);
          },
          this._source = e,
          this._model = t,
          this._propertyPages = r,
          this._checkpoint = this._ensureCheckpoint(n),
          this._subscription = this._model.model().dataSourceCollectionChanged(),
          this._subscription.subscribe(this, this._handleCollectionChanged);
      }
      return e.prototype.hide = function(e) {
        e ? this._handleCancel() : this._handleSubmit(), this._handleClose();
      },
        e.prototype.isVisible = function() {
          return this._isVisible;
        },
        e.prototype.focusOnText = function() {},
        e.prototype.show = function(e) {
          if (void 0 === e && (e = {}), p.enabled("property_pages")) {
            var t = this._source.metaInfo();
            if (
              Object(nn.isLineTool)(this._source)
              && Object(d.trackEvent)("GUI", "Drawing Properties", this._source.name()), Object(b.isStudy)(this._source)
            ) {
              var n = !this._source.isPine() || this._source.isStandardPine() ? t.description : "Custom Pine";
              Object(d.trackEvent)("GUI", "Study Properties", n);
            }
            var r = {
                byId: {
                  inputs: { title: window.t("Inputs"), Component: q },
                  style: { title: window.t("Style"), Component: tn },
                  properties: { title: window.t("Properties"), Component: U },
                },
                allIds: [],
              },
              l = new F.a(t);
            l.hasUserEditableInputs() && r.allIds.push("inputs"),
              l.hasUserEditableProperties() && r.allIds.push("properties"),
              l.hasUserEditableStyles() && r.allIds.push("style"),
              r = this._getPagesForStudyLineTool(r);
            var i = e.initialTab || c.getValue(this._activeTabSettingsName()) || "inputs",
              h = Object(s.clean)(t.shortDescription, !0);
            0,
              a.render(
                o.createElement(w, {
                  title: h,
                  model: this._model,
                  source: this._source,
                  initialActiveTab: r.allIds.includes(i) ? i : r.allIds[0],
                  pages: r,
                  onSubmit: this._handleSubmit,
                  onCancel: this._handleCancel,
                  onClose: this._handleClose,
                  onActiveTabChanged: this._handleActiveTabChanged,
                }),
                this._container,
              ),
              this._isVisible = !0,
              u.emit("edit_object_dialog", { objectType: "study", scriptTitle: this._source.title() });
          }
        },
        e.prototype._activeTabSettingsName = function() {
          return "properties_dialog.active_tab.study";
        },
        e.prototype._ensureCheckpoint = function(e) {
          return void 0 === e && (e = this._model.createUndoCheckpoint()), e;
        },
        e.prototype._getPagesForStudyLineTool = function(e) {
          if (this._propertyPages) {
            var t = this._propertyPages.filter(function(e) {
                return "coordinates" === e.id || "visibility" === e.id;
              }),
              n = {
                allIds: t.map(function(e) {
                  return e.id;
                }),
                byId: t.reduce(function(e, t) {
                  var n;
                  return Object(r.__assign)(
                    Object(r.__assign)({}, e),
                    ((n = {})[t.id] = { title: t.title, page: t }, n),
                  );
                }, {}),
              };
            return {
              allIds: Object(r.__spreadArrays)(e.allIds, n.allIds),
              byId: Object(r.__assign)(Object(r.__assign)({}, e.byId), n.byId),
            };
          }
          return e;
        },
        e.prototype._handleCollectionChanged = function() {
          var e = this;
          null === this._timeout && (this._timeout = setTimeout(function() {
            e._closeDialogIfSourceIsDeleted(), e._timeout = null;
          }));
        },
        e.prototype._closeDialogIfSourceIsDeleted = function() {
          null === this._model.model().dataSourceForId(this._source.id()) && this._handleClose();
        },
        e;
    }();
  },
  "ZSM+": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"currentColor\"><circle cx=\"9\" cy=\"14\" r=\"1\"/><circle cx=\"4\" cy=\"14\" r=\"1\"/><circle cx=\"14\" cy=\"14\" r=\"1\"/><circle cx=\"19\" cy=\"14\" r=\"1\"/><circle cx=\"24\" cy=\"14\" r=\"1\"/></svg>";
  },
  ZtdB: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path stroke=\"currentColor\" d=\"M4.5 20v-7m3 7V10m3 10V8m3 12V10m3 10v-8m3 8V10m3 10V8\"/></svg>";
  },
  bQEj: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path stroke=\"currentColor\" d=\"M4 13.5h20\"/></svg>";
  },
  br6c: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><circle stroke=\"currentColor\" cx=\"14\" cy=\"14\" r=\"6.5\"/></svg>";
  },
  flzi: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M19.424 16.735l.478.765H8.098l.478-.765 5-8L14 8.057l.424.678 5 8z\"/></svg>";
  },
  iB0j: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path stroke=\"currentColor\" d=\"M9 9l11 11M9 20L20 9\"/></svg>";
  },
  "j3s+": function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", { value: !0 }), t.Switch = void 0;
    var r = n("mrSG"), a = n("q1tI"), o = n("TSYQ"), l = n("5ijr");
    function i(e) {
      var t = e.className,
        n = void 0 === t ? "" : t,
        r = e.intent,
        a = void 0 === r ? "default" : r,
        i = e.size,
        s = void 0 === i ? "small" : i,
        c = e.disabled;
      return o(n, l.switcherWrapper, l["size-" + s], !c && l["intent-" + a]);
    }
    n("4pMH");
    var s = function(e) {
      function t() {
        return null !== e && e.apply(this, arguments) || this;
      }
      return r.__extends(t, e),
        t.prototype.render = function() {
          var e = this.props,
            t = e.reference,
            n = (e.size, e.intent, r.__rest(e, ["reference", "size", "intent"])),
            s = o(l.input, -1 !== this.props.tabIndex && l.focus);
          return a.createElement(
            "div",
            { className: i(this.props) },
            a.createElement("input", r.__assign({}, n, { type: "checkbox", className: s, ref: t })),
            a.createElement(
              "div",
              { className: l.switcherThumbWrapper },
              a.createElement("div", { className: l.switcherTrack }),
              a.createElement("div", { className: l.switcherThumb }),
            ),
          );
        },
        t;
    }(a.PureComponent);
    t.Switch = s;
  },
  kMtk: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M11 8.5h-.5v9.707l.146.147 3 3 .354.353.354-.353 3-3 .146-.147V8.5H11z\"/></svg>";
  },
  lOpG: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M14 7l7.424 6.114a.5.5 0 0 1-.318.886H18.5v7h-9v-7H6.894a.5.5 0 0 1-.318-.886L14 7z\"/></svg>";
  },
  leq5: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M19.424 11.265l.478-.765H8.098l.478.765 5 8 .424.678.424-.678 5-8z\"/></svg>";
  },
  "m+Gx": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path stroke=\"currentColor\" d=\"M9 14.5h11M14.5 20V9\"/></svg>";
  },
  "rlj/": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M5.5 17v5.5h4v-18h4v12h4v-9h4V21\"/></svg>";
  },
  "sPU+": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M10.5 13a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM16.5 19a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM22.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z\"/></svg>";
  },
  tH7p: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M5.5 13.52v4.98a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1V8.914c0-.89-1.077-1.337-1.707-.707l-4.66 4.66a1 1 0 0 1-1.332.074l-3.716-2.973a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82z\"/></svg>";
  },
  tQCG: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M13 11.5l-1.915-1.532a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82V18.5a1 1 0 0 0 1 1H13m3.5-7l4.293-4.293c.63-.63 1.707-.184 1.707.707V18.5a1 1 0 0 1-1 1H16\"/><path fill=\"currentColor\" d=\"M14 6h1v2h-1zM14 11h1v2h-1zM14 16h1v2h-1zM14 21h1v2h-1z\"/></svg>";
  },
  wwEg: function(e, t, n) {
    e.exports = {
      smallStyleControl: "smallStyleControl-1XGqoHgA",
      additionalSelect: "additionalSelect-1RoWzlTA",
      childRowContainer: "childRowContainer-_iCnmDPI",
      defaultSelect: "defaultSelect-DeTJWnAh",
      defaultSelectItem: "defaultSelectItem-1jN74NCa",
      block: "block-3Tp_jRog",
      group: "group-2HQIdqE5",
      wrapGroup: "wrapGroup-3gHGJIrr",
      textMarkGraphicBlock: "textMarkGraphicBlock-1nDopgxR",
      textMarkGraphicWrapGroup: "textMarkGraphicWrapGroup-3QaIoY03",
    };
  },
  xHjM: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5\"/></svg>";
  },
  z1Uu: function(e, t, n) {
    e.exports = { defaultSelect: "defaultSelect-2RDyqwu4" };
  },
}]);
