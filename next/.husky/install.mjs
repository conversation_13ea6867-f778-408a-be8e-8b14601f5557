import { resolve } from 'path'

// <PERSON><PERSON> installation in production or CI environments
if (
  process.env.NODE_ENV === 'production' ||
  process.env.CI === 'true' ||
  process.env.CIRCLECI === 'true' ||
  process.env.NETLIFY === 'true' ||
  process.env.SKIP_HUSKY_INSTALL === 'true' ||
  process.env.HUSKY === '0'
) {
  console.log('Skipping Husky installation in production or CI environments')
  process.exit(0)
}

// Install Husky
const husky = (await import('husky')).default
const dir = resolve(process.cwd(), '..', 'next', '.husky')
console.log('Installing <PERSON>sky in', dir)

console.log(husky(dir))
