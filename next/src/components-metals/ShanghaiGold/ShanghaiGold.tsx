import dayjs from 'dayjs'
import Link from 'next/link'
import { Suspense, useEffect, useMemo, useState } from 'react'
import ShangHaiContent from '~/src/components-metals/ShanghaiGold/ShangHaiContent'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import { CurrencySelectCNY } from '~/src/components/CurrencySelect'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { Query } from '~/src/components/Query/Query'
import { DynamicWeightSelect } from '~/src/components/WeightSelect'
import type { CurrenciesQuery } from '~/src/generated'
import { useShanghaiGoldData } from '~/src/hooks/ShangHai/useShanghaiGoldData'
import WeightType from '~/src/types/WeightSelect/WeightType'
import conversionRateFromCNY from '~/src/utils/Conversion/conversionRateFromCNY'
import { transformData } from '~/src/utils/SangHai/transformData'
import styles from './ShanghaiGold.module.scss'

const ShanghaiGold = () => {
  const [data, setData] = useState(null)

  // Get data from useShanghaiGoldData hook
  const { currency, fetcher, fetcherCurrencies, quoteDate, setQuoteDate } =
    useShanghaiGoldData()

  return (
    <BlockShell
      title="Shanghai Gold Benchmark Price"
      href="/price/fixes/shanghai-benchmark"
    >
      <div className={styles.wrapper}>
        <div className={styles.contents}>
          <div className="flex flex-col items-stretch justify-between pb-2">
            <div className="flex items-center justify-between gap-1">
              <CurrencySelectCNY classNamesListbox="!w-1/2" hideFlags />
              <DynamicWeightSelect
                type={WeightType.PreciousMetals}
                defaultWeight="GRAM"
                classNamesListbox="!w-1/2"
                id="shanghai"
              />
            </div>
            <p className="flex h-7 items-center text-xs">
              {quoteDate ? dayjs.unix(quoteDate).format('MMM DD, YYYY') : ''}
            </p>
          </div>
          <ErrBoundary>
            <Suspense fallback={<div>Loading...</div>}>
              <Query fetcher={fetcher}>
                {(res) => {
                  if (res.error) {
                    return (
                      <p className="my-8 text-center">
                        Oh no, something went wrong
                      </p>
                    )
                  }

                  /**
                   * Extract data from response
                   */
                  useEffect(() => {
                    setData(res?.data?.GetShanghaiFixV3?.results?.[0])
                  }, [res])

                  /**
                   * Parse timestamp from data
                   */
                  useEffect(() => {
                    if (data?.timestamp) {
                      setQuoteDate(data.timestamp)
                    }
                  }, [data])

                  // Transform data
                  const transformedData = useMemo(
                    () => transformData(data),
                    [data],
                  )

                  // Fetch currencies data
                  const fetcherCurrenciesData = fetcherCurrencies(
                    transformedData.latestTimestamp,
                  )

                  return (
                    <Query<CurrenciesQuery> fetcher={fetcherCurrenciesData}>
                      {(resCurrencies) => {
                        return (
                          <ShangHaiContent
                            data={data}
                            isLoading={res.isFetching}
                            symbol={currency.symbol}
                            timeStamp={transformedData.latestTimestamp}
                            conversionRate={conversionRateFromCNY(
                              currency.symbol,
                              resCurrencies.data,
                            )}
                          />
                        )
                      }}
                    </Query>
                  )
                }}
              </Query>
            </Suspense>
          </ErrBoundary>
          <div className={styles.historicalButtonContainer}>
            <Link href="/price/fixes/shanghai-benchmark">
              Historical SGE Fix
            </Link>
          </div>
        </div>
      </div>
    </BlockShell>
  )
}

export default ShanghaiGold
