import { type FC, memo } from 'react'
import CommodityPrice from '~/src/components/CommodityPrice/CommodityPrice'
import useMetalQuoteCell from '~/src/hooks/MetalQuotes/useMetalQuoteCell'

interface MetalQuoteCellProps {
  symbol: string
  isBaseMetal?: boolean
}

/**
 * MetalQuoteCell component.
 *
 * @param symbol
 * @param isBaseMetal
 * @constructor
 */
const MetalQuoteCell: FC<MetalQuoteCellProps> = ({
  symbol,
  isBaseMetal = false,
}) => {
  // Get the currency, data, isLoading, and error from the useMetalQuoteCell hook
  const { currency, data, isLoading, error } = useMetalQuoteCell(symbol)

  // If the data is loading, return a loading message
  if (isLoading) return <div>Loading...</div>

  // If there is an error loading the data, return an error message
  if (error) return <div>Error loading data</div>

  return (
    <div>
      <CommodityPrice
        symbol={symbol}
        currency={currency}
        data={data}
        isBaseMetal={isBaseMetal}
      />
    </div>
  )
}

export default memo(MetalQuoteCell)
