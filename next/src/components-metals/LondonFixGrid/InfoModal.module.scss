/* Modal overlay - covers entire screen */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

/* Modal content container */
.modalContent {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

  /* Mobile responsive */
  @media (max-width: 768px) {
    max-width: 90vw;
    margin: 1rem;
    border-radius: 4px;
  }
}

/* Close button */
.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #333;
  color: white;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;

  &:hover {
    background: #555;
  }
}

/* Modal title */
.modalTitle {
  color: #0a87d2;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  padding: 1.5rem 1.5rem 0 1.5rem;

  /* Mobile responsive */
  @media (max-width: 768px) {
    font-size: 1.25rem;
    padding: 1rem 1rem 0 1rem;
    padding-right: 3rem; /* Make room for close button */
  }
}

/* Modal body */
.modalBody {
  padding: 0 1.5rem 1.5rem 1.5rem;
  line-height: 1.6;
  color: #333;

  /* Mobile responsive */
  @media (max-width: 768px) {
    padding: 0 1rem 1rem 1rem;
    font-size: 0.9rem;
  }

  p {
    margin: 0 0 1rem 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* Email link styling */
.emailLink {
  color: #0a87d2;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
