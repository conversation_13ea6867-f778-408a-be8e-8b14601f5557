import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import cs from '~/src/utils/cs'
import priceFormatter from '~/src/utils/price-formatter'
import { useLondonFixCurrencyData } from './use-london-fix-currency-data.hook'

const b = 'border border-ktc-date-gray border-l-0 border-b-0'

export function LondonFixSidebarGoldSilverPlatinumPalladium() {
  const { data } = useLondonFixCurrencyData()

  const [d, setD] = useState(null)

  useEffect(() => {
    setD(data?.londonFixUSD?.results?.[0])
  }, [data])

  return (
    <BlockShell title="London Fix">
      <table className={cs(['w-full border-collapse', b])}>
        <thead>
          <tr>
            <th className={cs(['pl-2 text-left', b])}>
              {!d?.timestamp
                ? '-'
                : dayjs.unix(d?.timestamp).format('MMM DD, YYYY')}
            </th>
            <th className={cs(['pr-2 text-right', b])}>AM</th>
            <th className={cs(['pr-3 text-right', b])}>PM</th>
          </tr>
        </thead>
        <tbody>
          <Cell label="Gold" am={d?.goldAM} pm={d?.goldPM} />
          <Cell label="Silver" am={d?.silver} pm={d?.silver} />
          <Cell label="Platinum" am={d?.platinumAM} pm={d?.platinumPM} />
          <Cell label="Palladium" am={d?.palladiumAM} pm={d?.palladiumPM} />
        </tbody>
      </table>
    </BlockShell>
  )
}

function Cell(p: {
  label: string
  am: number | undefined
  pm: number | undefined
}) {
  return (
    <tr>
      <th className={cs(['pl-2 text-left', b])}>{p.label}</th>
      <td className={cs(['py-1 pr-1 text-right', b])}>
        {priceFormatter(p.am) || '-'}
      </td>
      <td className={cs(['py-1 pr-2 text-right', b])}>
        {priceFormatter(p.pm) || '-'}
      </td>
    </tr>
  )
}
