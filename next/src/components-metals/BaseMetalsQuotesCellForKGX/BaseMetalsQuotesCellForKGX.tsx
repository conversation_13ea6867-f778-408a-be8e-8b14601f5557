import type { FC } from 'react'
import KitcoTable from '~/src/components/KitcoTable/KitcoTable'
import { metals } from '~/src/lib/metals-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

const BaseMetalsQuoteCellForKGX: FC = () => {
  const { data } = kitcoQuery(
    metals.baseMetals({
      variables: {
        currency: 'USD',
        timestamp: timestamps.current(),
      },
    }),
  )

  const dataAsArray = !data
    ? []
    : [
        data?.AluminumPrice,
        data?.CopperPrice,
        data?.NickelPrice,
        data?.ZincPrice,
        data?.LeadPrice,
        data?.Uranium,
      ]

  return <KitcoTable data={dataAsArray} title="Kitco Base Metals" />
}

export default BaseMetalsQuoteCellForKGX
