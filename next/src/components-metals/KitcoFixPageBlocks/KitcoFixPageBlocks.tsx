import { Suspense } from 'react'
import { CurrencySelect } from '~/src/components/CurrencySelect'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { Query } from '~/src/components/Query/Query'
import { DynamicWeightSelect } from '~/src/components/WeightSelect'
import { Socials } from '~/src/components/socials/Socials'
import type { Metal } from '~/src/generated'
import { useCurrency } from '~/src/hooks/Currency/useCurrency'
import useWeight from '~/src/hooks/Weight/useWeight'
import { metals } from '~/src/lib/metals-factory.lib'
import st from '~/src/styles/pages/kitco-fix.module.scss'
import WeightType from '~/src/types/WeightSelect/WeightType'
import { renderFn } from '~/src/utils/SangHai/priceConversion'
import cs from '~/src/utils/cs'
import dates, { Timezones } from '~/src/utils/dates'

export function TitleBar() {
  return (
    <div className={st.titlebarWrap}>
      <div className={st.iconContainer}>
        <img
          src="/icons/kitco-circle-logo.svg"
          height="35px"
          width="35px"
          alt="Kitco Circle Logo"
        />
      </div>
      <h1 className={st.pageTitle}>
        Kitco Gold and Precious Metals Fix{' '}
        <span className={st.newSpan}>NEW</span>
      </h1>
      <div className={st.socialsContainer}>
        <Socials
          facebook="https://frontend.dev.kitco.com"
          email="https://frontend.dev.kitco.com"
          twitter="https://frontend.dev.kitco.com"
          linkedIn="https://frontend.dev.kitco.com"
        />
      </div>
    </div>
  )
}

export function ZoneSwitcher(props: {
  zone: Timezones
  zoneHandler: (z: Timezones) => void
}) {
  const zones: Array<{ short: Timezones; long: string }> = [
    { short: Timezones.NY, long: 'New York' },
    { short: Timezones.UK, long: 'London' },
    { short: Timezones.IS, long: 'Mumbai' },
    { short: Timezones.HK, long: 'Hong Kong' },
  ]
  return (
    <div className={st.btnGrid}>
      {zones.map(({ short, long }, idx) => (
        <button
          key={idx}
          className={cs([
            st.defs,
            props.zone !== short ? st.inactive : st.active,
          ])}
          type="button"
          onClick={() => props.zoneHandler(short)}
        >
          {long}
        </button>
      ))}
    </div>
  )
}

export function MainPriceTitle() {
  return (
    <h1 className={st.dateTitle} suppressHydrationWarning>
      {dates.day()}
    </h1>
  )
}

export function MainPriceBorder({ children }: { children: React.ReactNode }) {
  return <div className={st.mainPricesBorder}>{children}</div>
}

export function MainPriceBlock(props: { zone: Timezones }) {
  const currency = useCurrency()
  const weight = useWeight(WeightType.PreciousMetals)

  const fetcher = metals.goldSilverPlatinumPalladium({
    variables: {
      timestamp: dates.tenAMunix(props.zone),
      currency: currency.symbol,
    },
  })
  const loaders = ['gold', 'silver', 'platinum', 'palladium']

  function getZoneTimeLabel(zone: Timezones): string {
    switch (zone) {
      case Timezones.NY:
        return 'NY Time'
      case Timezones.UK:
        return 'London Time'
      case Timezones.IS:
        return 'Mumbai Time'
      case Timezones.HK:
        return 'HK Time'
      default:
        return ''
    }
  }

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data }) => {
            return (
              <div className={st.priceBodyWrap}>
                <div className={st.priceGrid}>
                  {!data
                    ? loaders.map((x) => (
                        <div key={x} className={st.item}>
                          <h4>{x}</h4>
                          <h2>-</h2>
                          <p>{dates.day()}</p>
                        </div>
                      ))
                    : Object.entries(data)?.map(
                        ([name, metal]: [string, Metal], idx) => (
                          <div
                            key={`${name}-${metal.ID}-${idx}`}
                            className={st.item}
                          >
                            <h4>{name}</h4>
                            <h2>{renderFn(weight, metal?.results[0]?.bid)}</h2>
                            <span>{`${dates.clock(
                              props.zone ?? Timezones.NY,
                              metal.ID,
                            )} ${getZoneTimeLabel(props.zone)}`}</span>
                          </div>
                        ),
                      )}
                </div>
              </div>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export function ControlBar(props: { zone: Timezones }) {
  function getZoneTimeLabel(zone: Timezones): string {
    switch (zone) {
      case Timezones.NY:
        return 'EST (GMT-5)'
      case Timezones.UK:
        return 'BST (GMT+1)'
      case Timezones.IS:
        return 'IST (GMT+5:30)'
      case Timezones.HK:
        return 'HKT (GMT+8)'
      default:
        return ''
    }
  }

  return (
    <div className={st.controlWrap}>
      <h2>Time value in {getZoneTimeLabel(props.zone)}</h2>
      <div className="flex items-center gap-4">
        <CurrencySelect />
        <DynamicWeightSelect type={WeightType.PreciousMetals} />
      </div>
    </div>
  )
}
