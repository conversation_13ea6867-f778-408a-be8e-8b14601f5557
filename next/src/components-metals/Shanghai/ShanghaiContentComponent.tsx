import { useEffect, useMemo } from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { Query } from '~/src/components/Query/Query'
import ShangHaiContent from '~/src/components/ShangHai/ShangHaiContent'
import { CanvasChart } from '~/src/features/home-page/home-chart'
import { useCurrencyCNY } from '~/src/hooks/Currency/useCurrency'
import useWeight from '~/src/hooks/Weight/useWeight'
import { metals } from '~/src/lib/metals-factory.lib'
import styles from '~/src/styles/shanghaiFixPage.module.scss'
import WeightType from '~/src/types/WeightSelect/WeightType'
import conversionRateFromCNY from '~/src/utils/Conversion/conversionRateFromCNY'
import { renderFn } from '~/src/utils/SangHai/priceConversion'
import { transformData } from '~/src/utils/SangHai/transformData'
import { Timescales } from '~/src/utils/ctxTimestamp'

const ShanghaiContentComponent = ({ data, refetch, isFetching, read }) => {
  const currency = useCurrencyCNY()
  const weight = useWeight(WeightType.PreciousMetals, 'GRAM', 'shanghai')

  const transformedData = useMemo(() => transformData(data), [data])

  const fetcherCurrencies = metals.currencies({
    variables: { timestamp: transformedData?.latestTimestamp },
  })

  useEffect(() => {
    refetch()
  }, [read, refetch])

  return (
    <Query fetcher={fetcherCurrencies}>
      {(resCurrencies) => {
        const formedData = useMemo(
          () => formatData(data, currency, weight, resCurrencies.data),
          [data, currency.symbol, weight, JSON.stringify(resCurrencies.data)],
        )

        return (
          <>
            <div style={{ height: '400px' }}>
              <ErrBoundary errorTitle="Shanghai Fix Chart">
                <CanvasChart data={formedData} scale={Timescales.ONE_DAY} />
              </ErrBoundary>
            </div>
            <div className={styles.block}>
              <ErrBoundary errorTitle="Shanghai Fix Content">
                <ShangHaiContent
                  data={data}
                  transformedData={transformedData}
                  currency={currency}
                  currencies={resCurrencies.data}
                  isFetchingData={isFetching}
                />
              </ErrBoundary>
            </div>
          </>
        )
      }}
    </Query>
  )
}

const formatData = (data, currency, weight, currenciesData) => {
  const { results } = data?.GetShanghaiFixByYearV3 || {}
  if (!results || !results.length) {
    return { labels: [], values: [] }
  }
  const { labels, values } = results.reduce(
    (acc, { pm, timestamp }) => {
      acc.labels.unshift(timestamp)
      acc.values.unshift(
        currency.symbol === 'CNY'
          ? renderFn(weight, pm, { defaultWeight: 'GRAM' }).replace(',', '')
          : renderFn(
              weight,
              pm * conversionRateFromCNY(currency.symbol, currenciesData),
              { defaultWeight: 'GRAM' },
            ).replace(',', ''),
      )
      return acc
    },
    { labels: [], values: [] },
  )
  return { labels, values }
}

export default ShanghaiContentComponent
