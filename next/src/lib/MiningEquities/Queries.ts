import type { UseQueryOptions } from '@tanstack/react-query'
// @ts-ignore
import { gql } from 'graphql-request'
import type {
  MiningEquitiesTableQuery,
  MiningEquitiesTableQueryVariables,
} from '~/src/generated'
import { graphs } from '~/src/services/database/fetcher'
import type QueryArgs from '~/src/types/QueryArgs'
import { refetchInterval } from '../../utils/timestamps'

export const MiningEquitiesQueries = {
  miningEquitiesTable: (
    args?: QueryArgs<
      MiningEquitiesTableQueryVariables,
      MiningEquitiesTableQuery
    >,
  ): UseQueryOptions<MiningEquitiesTableQuery> => {
    const query = gql`
      query MiningEquitiesTable {
        GetEquities {
          Category
          Change
          ChangePercentage
          Currency
          Exchange
          High
          ID
          Low
          Name
          Price
          Symbol
          SymbolURL
          TVExchange
          TVSymbol
          Timestamp
          Volume
        }
      }
    `

    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['MiningEquitiesTable'],
      queryFn: async () => await graphs.pricesFetch(query, args?.variables),
    }
  },
}
