import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  GoldIndexTableQuery,
  GoldIndexTableQueryVariables,
  GoldIndexWidgetQuery,
  GoldIndexWidgetQueryVariables,
} from '~/src/generated'
import type QueryArgs from '~/src/types/QueryArgs'
import { excludeTimestampFromCacheKey } from '~/src/utils/exclude-timestamp-from-cache-key.util'
import { graphs } from '../../services/database/fetcher'
import { refetchInterval } from '../../utils/timestamps'
import { goldIndexFragment } from './Fragments'

export const GoldIndex = {
  goldIndexTable: (
    args?: QueryArgs<GoldIndexTableQueryVariables, GoldIndexTableQuery>,
  ): UseQueryOptions<GoldIndexTableQuery> => {
    const query = gql`
      ${goldIndexFragment}
      query GoldIndexTable($currency: String!, $timestamp: Int) {
        Gold: GetMetalQuoteV3(
          symbol: "AU"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Silver: GetMetalQuoteV3(
          symbol: "AG"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Platinum: GetMetalQuoteV3(
          symbol: "PT"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Palladium: GetMetalQuoteV3(
          symbol: "PD"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Copper: GetMetalQuoteV3(
          symbol: "CU"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Nickel: GetMetalQuoteV3(
          symbol: "NI"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Aluminum: GetMetalQuoteV3(
          symbol: "AL"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Zinc: GetMetalQuoteV3(
          symbol: "ZN"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Lead: GetMetalQuoteV3(
          symbol: "PB"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
      }
    `

    const cacheKey = excludeTimestampFromCacheKey(args.variables)

    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['goldIndexTable', cacheKey],
      queryFn: async () => await graphs.pricesFetch(query, args.variables),
    }
  },

  goldIndexWidget: (
    args?: QueryArgs<GoldIndexWidgetQueryVariables, GoldIndexWidgetQuery>,
  ): UseQueryOptions<GoldIndexWidgetQuery> => {
    const query = gql`
      ${goldIndexFragment}
      query goldIndexWidget($currency: String!, $timestamp: Int) {
        Gold: GetMetalQuoteV3(
          symbol: "AU"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Silver: GetMetalQuoteV3(
          symbol: "AG"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Platinum: GetMetalQuoteV3(
          symbol: "PT"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
        Palladium: GetMetalQuoteV3(
          symbol: "PD"
          currency: $currency
          timestamp: $timestamp
        ) {
          ...GoldIndexFragment
        }
      }
    `
    const cacheKey = excludeTimestampFromCacheKey(args.variables)

    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['goldIndexWidget', cacheKey],
      queryFn: async () => await graphs.pricesFetch(query, args.variables),
    }
  },
}
