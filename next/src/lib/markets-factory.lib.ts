import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  BarchartsFuturesByExchangeQuery,
  BarchartsFuturesByExchangeQueryVariables,
  BarchartsGoldIndicatorsQuery,
  BarchartsGoldIndicatorsQueryVariables,
  BarchartsLeadersQuery,
  BarchartsLeadersQueryVariables,
  BarchartsQuotesQuery,
  BarchartsQuotesQueryVariables,
  MarketStatusQuery,
  MarketStatusQueryVariables,
  RegionIndicesQuery,
  RegionIndicesQueryVariables,
} from '~/src/generated'
import type QueryArgs from '~/src/types/QueryArgs'
import { graphs } from '../services/database/fetcher'
import { excludeTimestampFromCacheKey } from '../utils/exclude-timestamp-from-cache-key.util'
import { refetchInterval } from '../utils/timestamps'
import { barchartFragment } from './metals-fragments.graphql'

export const markets = {
  marketStatus: (
    args?: QueryArgs<MarketStatusQueryVariables, MarketStatusQuery>,
  ): UseQueryOptions<MarketStatusQuery> => {
    return {
      ...args?.options,
      queryKey: ['marketStatus'],
      refetchInterval,
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query MarketStatus {
              GetMarketStatus {
                status
                next
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  barchartsGoldIndicators: (
    args?: QueryArgs<
      BarchartsGoldIndicatorsQueryVariables,
      BarchartsGoldIndicatorsQuery
    >,
  ): UseQueryOptions<BarchartsGoldIndicatorsQuery> => {
    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['barchartsGoldIndicators', args?.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${barchartFragment}
            query BarchartsGoldIndicators($symbols: String!, $timestamp: Int!) {
              GetBarchartQuotes(symbols: $symbols, timestamp: $timestamp) {
                results {
                  ...BarchartFragment
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  barchartsQuotes: (
    args?: QueryArgs<BarchartsQuotesQueryVariables, BarchartsQuotesQuery>,
  ): UseQueryOptions<BarchartsQuotesQuery> => {
    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['barchartsQuotes', args?.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query BarchartsQuotes($timestamp: Int!, $symbols: String!) {
              GetBarchartQuotes(symbols: $symbols, timestamp: $timestamp) {
                timestamp
                symbols
                results {
                  high
                  lastPrice
                  low
                  name
                  netChange
                  open
                  percentChange
                  serverTimestamp
                  symbol
                  volume
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  barchartsLeaders: (
    args?: QueryArgs<BarchartsLeadersQueryVariables, BarchartsLeadersQuery>,
  ): UseQueryOptions<BarchartsLeadersQuery> => {
    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['barchartsLeaders', args?.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query BarchartsLeaders($leaderType: String!, $limit: Int!) {
              leaders: GetBarchartLeaders(
                maxRecords: $limit
                leaderboardType: $leaderType
                exchanges: "NYSE,AMEX,NASDAQ"
                assetType: "STK"
              ) {
                exchanges
                timestamp
                results {
                  symbol
                  symbolName
                  priceNetChange
                  pricePercentChange
                  lastPrice
                  timestamp
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  regionIndices: (
    args?: QueryArgs<RegionIndicesQueryVariables, RegionIndicesQuery>,
  ): UseQueryOptions<RegionIndicesQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['barchartsLeaders', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query RegionIndices($timestamp: Int!) {
              USquotes: GetBarchartQuotes(
                symbols: "$NASX,$DOWI,$SPX"
                timestamp: $timestamp
              ) {
                timestamp
                symbols
                results {
                  lastPrice
                  name
                  netChange
                  percentChange
                  serverTimestamp
                  symbol
                }
              }

              EUquotes: GetBarchartQuotes(
                symbols: "$CAC,$SSMI,$DAX,"
                timestamp: $timestamp
              ) {
                timestamp
                symbols
                results {
                  lastPrice
                  name
                  netChange
                  percentChange
                  serverTimestamp
                  symbol
                }
              }

              ASIAquotes: GetBarchartQuotes(
                symbols: "$NKY,$HSI,$AXJO,"
                timestamp: $timestamp
              ) {
                timestamp
                symbols
                results {
                  lastPrice
                  name
                  netChange
                  percentChange
                  serverTimestamp
                  symbol
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  futuresByExchange: (
    args?: QueryArgs<
      BarchartsFuturesByExchangeQueryVariables,
      BarchartsFuturesByExchangeQuery
    >,
  ): UseQueryOptions<BarchartsFuturesByExchangeQuery> => {
    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['futuresByExchange', args?.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query BarchartsFuturesByExchange(
              $exchange: String!
              $category: String!
            ) {
              GetBarchartFuturesByExchange(
                exchange: $exchange
                category: $category
              ) {
                timestamp
                exchange
                results {
                  name
                  lastPrice
                  percentChange
                  netChange
                  symbol
                  close
                  low
                  high
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
