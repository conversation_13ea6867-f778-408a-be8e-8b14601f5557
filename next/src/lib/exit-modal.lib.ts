import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type { ExitModalQuery, ExitModalQueryVariables } from '~/src/generated'
import { graphs } from '../services/database/fetcher'
import type QueryArgs from '../types/QueryArgs'

export const exitModal = {
  query: (
    args?: QueryArgs<ExitModalQueryVariables, ExitModalQuery>,
  ): UseQueryOptions<ExitModalQuery> => {
    return {
      ...args?.options,
      queryKey: ['exitModal', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query ExitModal {
              exitModal {
                __typename
                active
                backgroundImage
                buttonColor
                subTitle
                subTitleColor
                title
                titleColor
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
