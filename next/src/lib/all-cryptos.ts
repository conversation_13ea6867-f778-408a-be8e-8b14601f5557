// CryptoCompare
// BTC,ETH,LTC,BNB,XRP,USDT,ADA,DOT,DOGE,UNI,LINK,EOS,BCH,XLM,TRX,ZEC,NEO,XMR,OMG,XEM,MANA,NANO,ZRX,ONT,DASH,MIOTA,SUPER,BAL,SOL,USDC,LUNA,AVAX,SHIB,MATIC,BUSD,CRO,WAX,WAXP,ALGO,DAI,NEAR,ATOM,AXS,FTM,VET,SAND,THETA,BONDLY,XTZ,AAVE,GALA,CAKE,LRC,KSM,RLC,ENJ,BAT,CHZ,CELO,ROSE,COMP,TUSD,SUSHI,YFI,1INCH,FLOW,DCR,CEL,STEEM,IMX,ANKR,BNT,SNX,PHA,UMA,DYDX,REN,KAVA,PYR,TRAC,CHR,SWAP,REQ,ALICE,INJ,PAXG,XVG,BAND,REP,UTK,AMPL,UBT,BMI,KNC,ILV,RSR,POLS,AKT,EGLD,CHSB,GRT,BAKE,HIVE,OCEAN,LUNC
// CoinGecko
// BTC,ETH,LTC,BNB,XRP,USDT,ADA,DOT,DOGE,UNI,LINK,EOS,BCH,XLM,TRX,ZEC,NEO,XMR,OMG,XEM,MANA,XNO,ZRX,ONT,DASH,IOTA,SUPER,BAL,SOL,USDC,LUNA,AVAX,SHIB,MATIC,BUSD,CRO,WAX,WAXP,ALGO,DAI,NEAR,ATOM,AXS,FTM,VET,SAND,THETA,BONDLY,XTZ,AAVE,GALA,CAKE,LRC,KSM,RLC,ENJ,BAT,CHZ,CELO,ROSE,COMP,TUSD,SUSHI,YFI,1INCH,FLOW,DCR,CEL,STEEM,IMX,ANKR,BNT,SNX,PHA,UMA,DYDX,REN,KAVA,PYR,TRAC,CHR,SWAP,REQ,ALICE,INJ,PAXG,XVG,BAND,REP,UTK,AMPL,UBT,BMI,KNC,ILV,RSR,POLS,AKT,EGLD,BORG,GRT,BAKE,HIVE,OCEAN,LUNC,SUI
import { urlSafePath } from '../utils/url-safe-path'

// create another array of objects like above but from the following list
// Bitcoin
// Ethereum
// Litecoin
// Binance Coin
// Ripple
// Tether
// Cardano
// Polkadot
// Dogecoin
// Uniswap
// Chainlink
// EOS
// Bitcoin Cash
// Stellar
// TRON
// Zcash
// NEO
// Monero
// OmiseGo
// NEM
// Decentraland
// Nano
// Ox
// Ontology
// Dash
// IOTA
// Solana
// USD Coin
// Terra
// Avalanche
// Shibu Inu
// Polygon
// Binance USD
// Crypto.com  Coin
// Worldwide Asset Exchange
// Algorand
// Dai
// Near Protocol
// Cosmos
// Axie Infinity
// Fantom
// VeChain
// The Sandbox
// Theta Network
// Bondly
// Tezos
// Aave
// Gala Games
// PancakeSwap
// Loopring
// Kusama
// iExec RLC
// Enjin Coin
// Basic Attention Token
// Chilliz
// Celo
// Oasis Network
// Compound
// TrueUSD
// SushiSwap
// Yearn.Finance
// 1inch Network
// Flow
// Decred
// Celsius
// Steem
// Immutable X
// Ankr
// Bancor
// Synthetix
// Phala Network
// Universal Market Access
// dYdX
// Ren
// Kava
// Vulcan Forged
// Origin Trail
// Chromia
// Trust Swap
// Request Network
// MyNeighborAlice
// Injective Protocol
// PAX Gold
// Verge
// Band Protocol
// Augur
// UTrust
// Ampleforth
// Unibright
// Bridge Mutual
// Kyber Network
// Illuvium
// Reserve Rights
// Polkastarter
// Akash Network
// Elrond
// Swissborg
// SuperFarm
// Sui
// Balancer

export const allCryptosTwo = [
  { id: 0, name: 'Bitcoin', symbol: 'BTC' },
  { id: 1, name: 'Ethereum', symbol: 'ETH' },
  { id: 2, name: 'Litecoin', symbol: 'LTC' },
  { id: 3, name: 'BNB', symbol: 'BNB' },
  { id: 4, name: 'XRP', symbol: 'XRP' },
  { id: 5, name: 'Tether', symbol: 'USDT' },
  { id: 6, name: 'Cardano', symbol: 'ADA' },
  { id: 7, name: 'Polkadot', symbol: 'DOT' },
  { id: 8, name: 'Dogecoin', symbol: 'DOGE' },
  { id: 9, name: 'Uniswap', symbol: 'UNI' },
  { id: 10, name: 'Chainlink', symbol: 'LINK' },
  { id: 11, name: 'EOS', symbol: 'EOS' },
  { id: 12, name: 'Bitcoin Cash', symbol: 'BCH' },
  { id: 13, name: 'Stellar', symbol: 'XLM' },
  { id: 14, name: 'TRON', symbol: 'TRX' },
  { id: 15, name: 'Zcash', symbol: 'ZEC' },
  { id: 16, name: 'NEO', symbol: 'NEO' },
  { id: 17, name: 'Monero', symbol: 'XMR' },
  { id: 18, name: 'OMG Network', symbol: 'OMG' },
  { id: 19, name: 'NEM', symbol: 'XEM' },
  { id: 20, name: 'Decentraland', symbol: 'MANA' },
  { id: 21, name: 'Nano', symbol: 'XNO' },
  { id: 22, name: '0x Protocol', symbol: 'ZRX' },
  { id: 23, name: 'Ontology', symbol: 'ONT' },
  { id: 24, name: 'Dash', symbol: 'DASH' },
  { id: 25, name: 'IOTA', symbol: 'IOTA' },
  { id: 28, name: 'Solana', symbol: 'SOL' },
  { id: 29, name: 'USDC', symbol: 'USDC' },
  { id: 30, name: 'Terra', symbol: 'LUNA' },
  { id: 31, name: 'Avalanche', symbol: 'AVAX' },
  { id: 32, name: 'Shiba Inu', symbol: 'SHIB' },
  { id: 33, name: 'Polygon', symbol: 'MATIC' },
  { id: 34, name: 'BUSD', symbol: 'BUSD' },
  { id: 35, name: 'Cronos', symbol: 'CRO' },
  { id: 37, name: 'WAX', symbol: 'WAXP' },
  { id: 38, name: 'Algorand', symbol: 'ALGO' },
  { id: 39, name: 'Dai', symbol: 'DAI' },
  { id: 40, name: 'Near Protocol', symbol: 'NEAR' },
  { id: 41, name: 'Cosmos Hub', symbol: 'ATOM' },
  { id: 42, name: 'Axie Infinity', symbol: 'AXS' },
  { id: 43, name: 'Fantom', symbol: 'FTM' },
  { id: 44, name: 'VeChain', symbol: 'VET' },
  { id: 45, name: 'The Sandbox', symbol: 'SAND' },
  { id: 46, name: 'Theta Network', symbol: 'THETA' },
  { id: 47, name: 'Forj', symbol: 'BONDLY' },
  { id: 48, name: 'Tezos', symbol: 'XTZ' },
  { id: 49, name: 'Aave', symbol: 'AAVE' },
  { id: 50, name: 'GALA', symbol: 'GALA' },
  { id: 51, name: 'PancakeSwap', symbol: 'CAKE' },
  { id: 52, name: 'Loopring', symbol: 'LRC' },
  { id: 53, name: 'Kusama', symbol: 'KSM' },
  { id: 54, name: 'iExec RLC', symbol: 'RLC' },
  { id: 55, name: 'Enjin Coin', symbol: 'ENJ' },
  { id: 56, name: 'Basic Attention', symbol: 'BAT' },
  { id: 57, name: 'Chiliz', symbol: 'CHZ' },
  { id: 58, name: 'Celo', symbol: 'CELO' },
  { id: 59, name: 'Oasis', symbol: 'ROSE' },
  { id: 60, name: 'Compound', symbol: 'COMP' },
  { id: 61, name: 'TrueUSD', symbol: 'TUSD' },
  { id: 62, name: 'Sushi', symbol: 'SUSHI' },
  { id: 63, name: 'Yearn.Finance', symbol: 'YFI' },
  { id: 64, name: '1inch', symbol: '1INCH' },
  { id: 65, name: 'Flow', symbol: 'FLOW' },
  { id: 66, name: 'Decred', symbol: 'DCR' },
  { id: 67, name: 'Celsius Network', symbol: 'CEL' },
  { id: 68, name: 'Steem', symbol: 'STEEM' },
  { id: 69, name: 'Immutable', symbol: 'IMX' },
  { id: 70, name: 'Ankr Network', symbol: 'ANKR' },
  { id: 71, name: 'Bancor Network', symbol: 'BNT' },
  { id: 72, name: 'Synthetix Network', symbol: 'SNX' },
  { id: 73, name: 'PHALA', symbol: 'PHA' },
  { id: 74, name: 'UMA', symbol: 'UMA' },
  { id: 75, name: 'dYdX', symbol: 'DYDX' },
  { id: 76, name: 'Ren', symbol: 'REN' },
  { id: 77, name: 'Kava', symbol: 'KAVA' },
  { id: 78, name: 'Vulcan Forged', symbol: 'PYR' },
  { id: 79, name: 'OriginTrail', symbol: 'TRAC' },
  { id: 80, name: 'Chromia', symbol: 'CHR' },
  { id: 81, name: 'TrustSwap', symbol: 'SWAP' },
  { id: 82, name: 'Request', symbol: 'REQ' },
  { id: 83, name: 'My Neighbor Alice', symbol: 'ALICE' },
  { id: 84, name: 'Injective', symbol: 'INJ' },
  // { id: 85, name: 'Dvision Network', symbol: 'DVI' },
  { id: 86, name: 'PAX Gold', symbol: 'PAXG' },
  { id: 87, name: 'Verge', symbol: 'XVG' },
  { id: 88, name: 'Band Protocol', symbol: 'BAND' },
  { id: 89, name: 'Augur', symbol: 'REP' },
  { id: 90, name: 'xMoney', symbol: 'UTK' },
  { id: 91, name: 'Ampleforth', symbol: 'AMPL' },
  { id: 92, name: 'Unibright', symbol: 'UBT' },
  { id: 93, name: 'Bridge Mutual', symbol: 'BMI' },
  { id: 94, name: 'Kyber Network Crystal', symbol: 'KNC' },
  { id: 95, name: 'Illuvium', symbol: 'ILV' },
  { id: 96, name: 'Reserve Rights', symbol: 'RSR' },
  { id: 97, name: 'Polkastarter', symbol: 'POLS' },
  { id: 98, name: 'Akash Network', symbol: 'AKT' },
  { id: 99, name: 'MultiversX', symbol: 'EGLD' },
  { id: 100, name: 'Swissborg', symbol: 'BORG' },
  { id: 101, name: 'SuperVerse', symbol: 'SUPER' },
  { id: 102, name: 'SUI', symbol: 'SUI' },
  { id: 103, name: 'Balancer', symbol: 'BAL' },
]

export const allCryptos = allCryptosTwo.map((x) => ({
  ...x,
  href: `/price/crypto/${urlSafePath(x.name)}`,
}))
