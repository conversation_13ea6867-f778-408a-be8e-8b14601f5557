import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  DigestLatestNewsQuery,
  DigestLatestNewsQueryVariables,
  DigestStreetTalkQuery,
  DigestStreetTalkQueryVariables,
} from '~/src/generated'
import { graphs } from '../services/database/fetcher'
import type QueryArgs from '../types/QueryArgs'

export const digest = {
  streetTalk: (
    args: QueryArgs<DigestStreetTalkQueryVariables, DigestStreetTalkQuery>,
  ): UseQueryOptions<DigestStreetTalkQuery> => {
    return {
      ...args?.options,
      queryKey: ['digestStreetTalk', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query DigestStreetTalk($limit: Int, $offset: Int) {
              nodeList(limit: $limit, offset: $offset, bundles: [StreetTalk]) {
                total
                items {
                  ... on StreetTalk {
                    id
                    title
                    source
                    createdAt
                    updatedAt
                    url
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },

  latestNews: (
    args: QueryArgs<DigestLatestNewsQueryVariables, DigestLatestNewsQuery>,
  ): UseQueryOptions<DigestLatestNewsQuery> => {
    return {
      ...args?.options,
      queryKey: ['digestLatestNews', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query DigestLatestNews($limit: Int, $offset: Int) {
              nodeListQueue(
                limit: $limit
                offset: $offset
                queueId: "latest_news"
                bundles: [NewsArticle, StreetTalk, OffTheWire]
              ) {
                total
                items {
                  ... on NewsArticle {
                    id
                    source {
                      id
                      name
                    }
                    title
                    urlAlias
                    createdAt
                    updatedAt
                    legacyThumbnailImageUrl
                  }

                  ... on OffTheWire {
                    id
                    source {
                      id
                      name
                    }
                    title
                    urlAlias
                    createdAt
                    updatedAt
                    legacyThumbnailImageUrl
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
