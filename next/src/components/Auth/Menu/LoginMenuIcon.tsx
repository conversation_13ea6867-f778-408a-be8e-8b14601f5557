import { onAuthStateChanged } from 'firebase/auth'
import type React from 'react'
import { useEffect, useState } from 'react'
import { FaUser } from 'react-icons/fa6'
import LoginForm from '~/src/components/Auth/Form/LoginForm'
import LoggedInMenu from '~/src/components/Auth/Menu/LoggedInMenu'
import LoginModal from '~/src/components/Auth/Modal/LoginModal'
import { auth } from '~/src/services/firebase/config'

/**
 * The LoginMenuIcon component
 * Is a button that shows the LoginModal
 * when the user is not logged in
 * and the LoggedInMenu when the user is logged in
 *
 * @returns {React.ReactElement} - The component
 */
function LoginMenuIcon(): React.ReactElement {
  // The state to show or hide the Modal
  const [showModal, setShowModal] = useState(false)

  // The state to check if the user is logged in
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  /**
   * Effect to check if the user is logged in
   */
  useEffect(() => {
    // Check if the user is logged in
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setIsLoggedIn(true)
      } else {
        setIsLoggedIn(false)
      }
    })

    // Clean up the observer
    return () => unsubscribe()
  }, [])

  /**
   * Handles the modal close
   * and reloads the page if needed
   *
   * @param shouldReload
   */
  const handleModalClose = (shouldReload = false) => {
    // Check if we are in a page with comments, for reloading the page
    const commentsDiv = document.getElementById('comments-loaded')

    // Check if we should reload the page
    if (shouldReload && commentsDiv) {
      if (window) window.location.reload()
    }

    // Close the modal
    setShowModal(false)
  }

  return (
    <>
      <div className="group relative">
        {!isLoggedIn && (
          <FaUser
            size="24px"
            color="white"
            className="hover:stroke-primary-500 cursor-pointer"
            onClick={() => setShowModal(true)}
          />
        )}

        {isLoggedIn && <LoggedInMenu />}
      </div>
      {showModal && (
        <LoginModal
          opened={showModal}
          onClose={handleModalClose}
          className="bg-gray-50"
        >
          <LoginForm onSuccess={handleModalClose} modal={true} />
        </LoginModal>
      )}
    </>
  )
}

export default LoginMenuIcon
