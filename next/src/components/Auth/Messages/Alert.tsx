import clsx from 'clsx'
import type React from 'react'
import type { FC } from 'react'

/**
 * Alert types.
 */
enum AlertType {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  SUCCESS = 'success',
}

/**
 * Auth error component properties.
 */
interface LoginErrorProps {
  title?: string
  message?: string
  type?: AlertType
  children?: React.ReactNode
}

/**
 * Alert component.
 *
 * @param {LoginErrorProps} props - Component properties.
 * @returns {React.Element} Alert component.
 */
const Alert: FC<LoginErrorProps> = ({
  title,
  message = '',
  type,
  children,
}: LoginErrorProps): React.ReactElement => {
  // Alert class.
  let alertClass = ''

  // Set the alert class based on the type.
  switch (type) {
    case AlertType.ERROR:
      alertClass = 'bg-red-50 border-red-400 text-red-800'
      title = title || 'Error'
      break
    case AlertType.WARNING:
      alertClass = 'bg-yellow-50 border-yellow-400 text-yellow-800'
      title = title || 'Warning'
      break
    case AlertType.INFO:
      alertClass = 'bg-blue-50 border-blue-400 text-blue-800'
      title = title || 'Info'
      break
    case AlertType.SUCCESS:
      alertClass = 'bg-green-50 border-green-400 text-green-800'
      title = title || 'Success'
      break
    default:
      alertClass = 'bg-red-50 border-red-400 text-red-800'
      title = title || 'Error'
  }

  return (
    <div className={clsx(alertClass, 'border-l-4 p-2')} role="alert">
      {!children && (
        <>
          <h3 className="mb-1 font-bold">{title}</h3>
          <p> {message}</p>
        </>
      )}
      {children}
    </div>
  )
}

export { Alert, AlertType }
