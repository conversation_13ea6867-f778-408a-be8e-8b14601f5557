import Link from 'next/link'
import type React from 'react'

/**
 * Auth Legal Footer component
 *
 * @returns {React.ReactElement} - Legal footer component
 */
function LoginLegalFooter(): React.ReactElement {
  return (
    <>
      <div className="text-center text-xs text-gray-400">
        By creating or using an account, you agree to our {''}
        <Link
          href="/terms-of-use"
          target="_blank"
          className="font-semibold text-gray-500 hover:text-gray-900"
        >
          Terms of Use
        </Link>
        , {''}
        <a
          href="/legal/terms-and-conditions"
          target="_blank"
          className="font-semibold text-gray-500 hover:text-gray-900"
        >
          Terms and Conditions
        </a>
        {''} and acknowledge our {''}
        <a
          href="/legal/privacy-policy"
          target="_blank"
          className="font-semibold text-gray-500 hover:text-gray-900"
        >
          Privacy Policy
        </a>
        .
      </div>
    </>
  )
}

export default LoginLegalFooter
