import { clsx } from 'clsx'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { HiExternalLink } from 'react-icons/hi'
import dataJeweler from '~/src/lib/dataJeweler'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import st from './FavoritesBar.module.scss'

// custom Hook
const getPosition = (containerRef: { current: any }) => {
  const [position, setPosition] = useState<number>()

  useEffect(() => {
    const container = containerRef.current
    const activeElement = container.querySelector('.border-b-ktc-blue')

    if (activeElement) {
      const containerRect = container.getBoundingClientRect()
      const activeRect = activeElement.getBoundingClientRect()

      if (container && container.scrollWidth > 0) {
        setPosition(
          activeRect.left +
            container.scrollLeft -
            containerRect.left -
            containerRect.width / 2 +
            activeRect.width / 2,
        )
      }
    }
  })

  return { position }
}

export const FavoritesBar = () => {
  const router = useRouter()

  const containerRef = useRef(null)

  const navBar = () => {
    if (
      router.pathname.includes('/news') ||
      router.pathname.includes('/author') ||
      router.pathname.includes('/opinion') ||
      router.pathname === '/podcasts' ||
      router.pathname === '/mining/press-release'
    ) {
      return <NewsNav router={router} containerRef={containerRef} />
    }

    if (
      router.pathname === '/jeweler-resources' ||
      router.pathname.startsWith('/jeweler-table')
    ) {
      return <JewelerNav router={router} containerRef={containerRef} />
    }

    return <DefaultFavorites />
  }

  return (
    <div className={st.background} ref={containerRef}>
      {navBar()}
    </div>
  )
}

const externalLinks = [
  { id: 1, label: 'Video News', url: '/news/video' },
  { id: 2, label: 'Podcasts', url: '/news/tag/podcasts' },
  { id: 3, label: 'Opinion', url: '/opinion' },
]

const DefaultFavorites = () => {
  return (
    <div className="mx-auto hidden h-full w-full max-w-[1240px] gap-2 px-2 md:flex">
      <ul className="mx-auto flex items-center text-xs text-black align-middle">
        {externalLinks.map((link) => (
          <li
            key={link.id}
            className="font-mulish h-full py-2.5 text-xs leading-4 flex items-center align-middle"
          >
            <span className="border-r-[1px] border-r-black px-2">
              <Link
                href={link.url}
                className="font-mulish text-sm leading-5 text-[#373737] hover:underline"
              >
                {link.label}
              </Link>
            </span>
          </li>
        ))}
        <li className="font-mulish h-full py-2.5 text-xs leading-4 flex items-center align-middle">
          <span className="border-r-[1px] border-r-black px-2">
            <a
              className="group font-mulish text-sm leading-5 text-[#373737] hover:underline"
              href="https://forum.kitco.com/categories?utm_source=kitco_website&utm_medium=navbar&utm_campaign=forum_navigation"
            >
              Kitco Forum
              <span className="ml-2 inline-flex items-center rounded-md bg-ktc-blue px-1 py-0.5 text-xs font-medium text-white group-hover:bg-kitco-black">
                NEW
              </span>
            </a>
          </span>
        </li>
        <li className="font-mulish h-full py-2.5 pl-2 text-xs leading-4 flex items-center align-middle">
          <Link
            href="/jeweler-resources"
            className="font-mulish text-sm leading-5 text-[#373737] hover:underline"
          >
            Jeweler Resources
          </Link>
        </li>
      </ul>
    </div>
  )
}

const NAV_ITEMS_DISABLED = ['Conferences']
const NewsNav = ({ router, containerRef }) => {
  const baseLiStyle =
    'py-2.5 border-b-[2px] h-full font-mulish text-xs leading-4'

  const { data } = kitcoQuery(news.newsCategoriesTree())

  const { position } = getPosition(containerRef)

  useEffect(() => {
    if (position !== undefined) {
      // TODO: this breaks refresh position
      // containerRef?.current?.scrollTo(position, 0);
    }
  }, [position])

  const navItem = (x) => {
    if (NAV_ITEMS_DISABLED.includes(x.name))
      return (
        <span className="font-mulish cursor-not-allowed text-sm leading-5 text-[#2323234d]">
          {x.name}
        </span>
      )

    return (
      <Link
        className="font-mulish text-sm leading-5 text-[#373737] hover:underline"
        href={x.urlAlias}
      >
        {x.name}
      </Link>
    )
  }

  return (
    <div className="mx-auto hidden h-full w-full max-w-[1240px] gap-2 px-2 md:flex">
      <ul className="mx-auto flex items-center text-xs text-black [&>*:last-child>span]:!border-r-0">
        {data?.categoriesTree?.map(
          (x) =>
            x?.status && (
              <li
                key={x.id}
                className={clsx(
                  baseLiStyle,
                  router.asPath.includes(x.urlAlias)
                    ? 'border-b-ktc-blue'
                    : 'border-b-transparent',
                )}
              >
                <span className="border-r-[1px] border-r-black px-2">
                  {navItem(x)}
                </span>
              </li>
            ),
        )}
      </ul>
    </div>
  )
}

const JewelerNav = ({ router, containerRef }) => {
  const baseLiStyle =
    'py-2.5 border-b-[2px] h-full font-mulish text-xs leading-4'

  const { position } = getPosition(containerRef)

  useEffect(() => {
    if (position !== undefined) {
      containerRef?.current?.scrollTo(position, 0)
    }
  }, [position])

  return (
    <div className="mx-auto hidden h-full w-full max-w-[1240px] gap-2 px-2 md:flex">
      <ul className="mx-auto flex items-center whitespace-nowrap text-xs text-black">
        {dataJeweler?.map((x) => (
          <li
            key={x.id}
            className={clsx(
              baseLiStyle,
              router.asPath.includes(x.urlAlias)
                ? 'border-b-ktc-blue'
                : 'border-b-transparent',
            )}
          >
            <span className="border-r-[1px] border-r-black px-2">
              <Link
                className="font-mulish text-sm leading-5 text-[#373737] hover:underline"
                href={x.urlAlias}
              >
                {x.name}
              </Link>
            </span>
          </li>
        ))}
        <li className={clsx(baseLiStyle, 'border-b-transparent pl-2')}>
          <a
            className="font-mulish flex items-center gap-1 text-sm leading-5 text-[#373737] hover:underline"
            href={'https://online.kitco.com/refining/refining-services.html'}
            target="_blank"
            rel="noreferrer"
          >
            Refining Services
            <HiExternalLink />
          </a>
        </li>
      </ul>
    </div>
  )
}
