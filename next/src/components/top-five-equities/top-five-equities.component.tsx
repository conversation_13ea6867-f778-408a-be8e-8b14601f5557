import clsx from 'clsx'
import Link from 'next/link'
import type { FC } from 'react'
import { BsPlusSquareFill } from 'react-icons/bs'
import QuotesTableOG from '~/src/components/QuotesTableOG/QuotesTableOG'
import Table from '~/src/components/Table/Table'
import { markets } from '~/src/lib/markets-factory.lib'
import { allSymbols } from '~/src/lib/miningIndices'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type { QuoteObj } from '~/src/types'
import * as timestamps from '~/src/utils/timestamps'

const useData = () => {
  const { data } = kitcoQuery(
    markets.barchartsQuotes({
      variables: {
        timestamp: timestamps.current(),
        symbols: allSymbols,
      },
    }),
  )

  let topFive: QuoteObj[] = []

  if (data) {
    topFive =
      data?.GetBarchartQuotes?.results?.length >= 1 &&
      data?.GetBarchartQuotes?.results
        .map((val: any) => val)
        .sort((a: { percentChange: number }, b: { percentChange: number }) =>
          a?.percentChange < b?.percentChange
            ? 1
            : a?.percentChange > b?.percentChange
              ? -1
              : 0,
        )
        .slice(0, 5)
  }

  return { topFive }
}

export const TopFiveEquities: FC = () => {
  const { topFive } = useData()

  return (
    <div className="scrollbar-hide scrolling-table-size overflow-x-auto">
      <div className="">
        <Table title={'Top 5 Performing Gold Equities'}>
          <QuotesTableOG section="stocks" data={topFive} />
        </Table>
        <Link
          href="/markets/stocks"
          as="/markets/stocks"
          className={clsx(
            'flex items-center gap-2',
            'group hover:bg-[#1D61AE] active:bg-[#144985]',
          )}
        >
          <div
            className={clsx(
              'flex items-center justify-center gap-2',
              'w-full border border-t-0 border-[#E2E8F0] py-2',
            )}
          >
            <BsPlusSquareFill className="mt-[.10rem] text-[#1D61AE] group-hover:bg-[#1D61AE] group-hover:text-[#ffffff] group-active:bg-[#1D61AE] group-active:text-[#ffffff]" />
            <span className="font-bold text-[#1D61AE] underline group-hover:!text-[#ffffff] group-active:!text-[#ffffff]">
              More Gold Stocks and Equities
            </span>
          </div>
        </Link>
      </div>
    </div>
  )
}

export const TopFiveEquitiesComingSoon: FC = () => {
  return (
    <div className="scrollbar-hide scrolling-table-size overflow-x-auto">
      <div className="">
        <Table title={'Top 5 Performing Gold Equities'}>
          <div className="h-56 bg-[#f5f5f5] text-center text-base font-bold leading-[14rem]">
            Coming back soon
          </div>
        </Table>
      </div>
    </div>
  )
}
