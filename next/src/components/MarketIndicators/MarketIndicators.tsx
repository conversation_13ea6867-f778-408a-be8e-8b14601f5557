import clsx from 'clsx'
import type React from 'react'
import type { FC } from 'react'
import { GrCircleInformation } from 'react-icons/gr'
import { Tooltip } from 'react-tippy'
import 'react-tippy/dist/tippy.css'
import useSWR from 'swr'
import { isUp } from '~/src/utils/Prices/isUp'
import { styleUpOrDown } from '~/src/utils/Prices/styleUpOrDown'
import cs from '~/src/utils/cs'
import priceFormatter from '~/src/utils/priceFormatter'
import BlockShell from '../BlockShell/BlockShell'
import styles from './MarketIndicators.module.scss'

const fetcher = (url) => fetch(url).then((r) => r.json())

const MarketIndicators: FC<{
  title: string
}> = ({ title }) => {
  const arrowUpOrDown = (v: number) => {
    const convertedValue = Number.parseFloat(v?.toString())
    if (convertedValue === 0) {
      return styles.arrowUnchanged
    }

    return !isUp(v) ? cs([styles.arrowDownTsp]) : cs([styles.arrowUpTsp])
  }

  // const { data } = kitcoQuery(
  //   markets.barchartsQuotes({
  //     variables: {
  //       timestamp: currentTimestamp(),
  //       symbols: '$DOWI,$NASX,$SPX,$NYA,$GVZ,$DXY,$NKY,$TXCX,$DXY',
  //     },
  //   })
  // )

  const { data: dataKGX } = useSWR(
    '/api/getKGX/DJI,IXIC,N225,NYA,GSPTSE,USDX,CL',
    fetcher,
  )

  let dataMI = []
  dataMI = dataKGX?.Values.Value.map((item) => {
    let name: string
    switch (item.Symbol) {
      case 'DJI':
        name = 'DJIA'
        break
      case 'IXIC':
        name = 'NASDAQ'
        break
      case 'N225':
        name = 'NIKKEI'
        break
      case 'NYA':
        name = 'NYSE'
        break
      case 'GSPTSE':
        name = 'TSX'
        break
      case 'USDX':
        name = 'USD'
        break
      case 'CL':
        name = 'Crude Oil'
        break
      default:
        name = item.Symbol // fallback to the original Symbol if not matched
    }

    return {
      changePercentage: item.ChangePercentage,
      price: item.Price,
      symbol: item.Symbol,
      name: name,
      change: item.Change,
      timestamp: item.Timestamp,
    }
  })

  const TooltipContentUSD: React.FC = () => {
    return (
      <div className="h-[420px] w-[380px] text-sm">
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <img
                  src="/kitco_glossary.gif"
                  width={274}
                  height={39}
                  alt="www.kitco.com glossary"
                />
              </td>
            </tr>
            <tr>
              <td height={5}>&nbsp;</td>
            </tr>
          </tbody>
        </table>
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <p className={styles.titleUSD}>US Dollar Index (USD)</p>
              </td>
            </tr>
            <tr>
              <td height={5}>&nbsp;</td>
            </tr>
          </tbody>
        </table>
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <p>
                  <b>Delayed 30 minutes</b>
                </p>
              </td>
            </tr>
            <tr>
              <td height={5}>&nbsp;</td>
            </tr>
          </tbody>
        </table>
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                The U.S. Dollar Index® is computed using a trade-weighted
                geometric average of six currencies. The six currencies and
                their trade weights are:
              </td>
            </tr>
            <tr>
              <td height={5}>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <table>
                  <tbody>
                    <tr>
                      <td width={99}>Euro</td>
                      <td width={175}>
                        <p className="text-center">57.6 %</p>
                      </td>
                    </tr>
                    <tr>
                      <td width={99}> Japan/yen</td>
                      <td width={175}>
                        <p className="text-center">13.6 %</p>
                      </td>
                    </tr>
                    <tr>
                      <td width={99}> UK/pound</td>
                      <td width={175}>
                        <p className="text-center">11.9 %</p>
                      </td>
                    </tr>
                    <tr>
                      <td width={99}> Canada/dollar </td>
                      <td width={175}>
                        <p className="text-center">9.1 %</p>
                      </td>
                    </tr>
                    <tr>
                      <td width={99}> Sweden/krona</td>
                      <td width={175}>
                        <p className="text-center">4.2 %</p>
                      </td>
                    </tr>
                    <tr>
                      <td width={99}> Switzerland/franc</td>
                      <td width={175}>
                        <p className="text-center">3.6 %</p>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <br />
                These contract specifications are subject to change. Please
                consult the FINEX if you have any questions.
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    )
  }

  const TooltipContentOIL: React.FC = () => {
    return (
      <div className="h-[420px] w-[380px] overflow-auto text-sm">
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <img
                  src="/kitco_glossary.gif"
                  width={274}
                  height={39}
                  alt="www.kitco.com glossary"
                />
              </td>
            </tr>
          </tbody>
        </table>
        <br />
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <p className={styles.titleUSD}>Light Sweet Crude Oil</p>
              </td>
            </tr>
            <tr>
              <td height={10}>&nbsp;</td>
            </tr>
          </tbody>
        </table>
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <p>
                  <b>Delayed 30 minutes</b>
                </p>
              </td>
            </tr>
            <tr>
              <td height={10}>&nbsp;</td>
            </tr>
          </tbody>
        </table>
        <table
          className="text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <p>
                  As of May 1, 2003, we have added <b>Light Sweet Crude Oil</b>{' '}
                  to our list of <b>Indicators</b>
                </p>
              </td>
            </tr>
            <tr>
              <td height={10}>&nbsp;</td>
            </tr>
          </tbody>
        </table>
        <table
          className="mb-4 text-left"
          width="95%"
          cellSpacing={0}
          cellPadding={0}
        >
          <tbody>
            <tr>
              <td>
                <p>
                  Crude oil is the world&apos;s most actively traded commodity.
                  Over the past decade, the NYMEX Division light, sweet
                  (low-sulfur) crude oil futures contract has become the
                  world&apos;s most liquid forum for crude oil trading, as well
                  as the world&apos;s largest-volume futures contract trading on
                  a physical commodity. Because of its excellent liquidity and
                  price transparency, the contract is used as a principal
                  international pricing benchmark.{' '}
                </p>
                <p>
                  The contract&apos;s delivery point is Cushing, Oklahoma, the
                  nexus of spot market trading in the United States, which is
                  also accessible to the international spot markets via
                  pipelines. By providing for delivery of several grades of
                  domestic and internationally traded foreign crudes, the
                  futures contract is designed to serve the diverse needs of the
                  physical market.
                </p>
                <p>
                  Light, sweet crudes are preferred by refiners because of their
                  relatively high yields of high-value products such as
                  gasoline, diesel fuel, heating oil, and jet fuel.
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    )
  }

  return (
    <BlockShell title={title}>
      <div>
        <div className={styles.subtitle}>Index data delayed 10 min.</div>
      </div>
      <ul className={styles.spotPriceGrid}>
        {dataMI?.slice(0, 5).map((x, idx) => (
          <Row key={idx} idx={idx}>
            <RowLabel arrowUpOrDown={arrowUpOrDown} change={x.change}>
              {x.name}
            </RowLabel>
            <Price>{priceFormatter(x.price)}</Price>
            <Change change={x.change}>
              {isUp(x.change) ? '+' : ''}
              {x.change}
            </Change>
          </Row>
        ))}

        <div className={styles.subtitle}>Index data delayed 30 min.</div>
        {dataMI?.slice(5).map((x, idx) => (
          <Row key={idx}>
            <RowLabel arrowUpOrDown={arrowUpOrDown} change={x?.change}>
              {x?.name}
            </RowLabel>
            <Tooltip
              theme="light"
              html={
                x?.name === 'USD' ? (
                  <TooltipContentUSD />
                ) : (
                  <TooltipContentOIL />
                )
              }
              trigger="mouseenter"
              arrow={true}
              animation="fade"
              className="ml-1"
              interactive={true}
              interactiveBorder={20}
            >
              <span>
                <GrCircleInformation />
              </span>
            </Tooltip>
            <Price>{x.price}</Price>
            <Change change={x.change}>
              {isUp(x.change) ? '+' : ''}
              {x.change}
            </Change>
          </Row>
        ))}

        {/* <Row>
          <RowLabel arrowUpOrDown={arrowUpOrDown} change={dataMI[5]?.change}>{dataMI[5]?.name}</RowLabel>
          <Tooltip
            theme='light'
            html={<TooltipContentUSD />}
            trigger="mouseenter"
            arrow={true}
            animation="fade"
            className='ml-1'
            interactive={true}
            interactiveBorder={20}
          >
            <span><GrCircleInformation /></span>
          </Tooltip>
          <Price>{dataMI[5]?.price}</Price>
          <Change

            change={dataMI[5]?.change}
          >
            {isUp(dataMI[5]?.change) ? '+' : ''}
            {dataMI[5].change}
          </Change>
        </Row>

        <Row>
          <RowLabel arrowUpOrDown={arrowUpOrDown} change={dataMI[6]?.change}>{dataMI[6]?.name}</RowLabel>
          <Tooltip
            theme='light'
            html={<TooltipContentOIL />}
            trigger="mouseenter"
            arrow={true}
            animation="fade"
            className='ml-1'
            interactive={true}
            interactiveBorder={20}
          >
            <span><GrCircleInformation /></span>
          </Tooltip>
          <Price>{dataMI[6]?.price}</Price>
          <Change

            change={dataMI[6]?.change}
          >
            {isUp(dataMI[6]?.change) ? '+' : ''}
            {dataMI[6]?.change}
          </Change>
        </Row> */}
      </ul>
    </BlockShell>
  )
}

export default MarketIndicators

type Props = { children: React.ReactNode }

const Row: React.FC<React.PropsWithChildren<{ idx?: number }>> = ({
  children,
  idx,
}) => (
  <li
    className={cs([
      !(idx % 2) ? '' : styles.idxAltBg,
      'flex items-center bg-[#f8f8f8] px-[15px] py-1 text-[#003871]',
    ])}
  >
    {children}
  </li>
)

const RowLabel: React.FC<
  React.PropsWithChildren<{ arrowUpOrDown: any; change: number }>
> = ({ children, arrowUpOrDown, change: percentChange }) => (
  <>
    <span className={arrowUpOrDown(percentChange)} />
    <p className={clsx(styles.priceName, 'capitalize')}>{children}</p>
  </>
)

const Price: React.FC<Props> = ({ children }) => (
  <p className={clsx(styles.convertPrice, 'ml-auto justify-self-end')}>
    {children}
  </p>
)

const Change: React.FC<React.PropsWithChildren<{ change: number }>> = ({
  children,
  change: percentChange,
}) => {
  return (
    <p
      className={cs([
        styleUpOrDown(percentChange, styles),
        styles.convertPrice,
        'min-w-[82px]',
      ])}
    >
      {children}
    </p>
  )
}
