import clsx from 'clsx'
import type { FC } from 'react'
import { markets } from '~/src/lib/markets-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

export const MarketStatus: FC = () => {
  const { data } = kitcoQuery(markets.marketStatus())
  const { status } = data?.GetMarketStatus || {}
  const isClosed = status === 'CLOSED'

  return (
    <div className="block min-w-[150px]">
      <div
        className={clsx(
          'my-1 flex items-center rounded-sm px-2 uppercase',
          isClosed ? 'bg-red-200' : 'bg-green-200',
        )}
      >
        <p className="w-full text-center">
          Market is{' '}
          <span
            className={clsx(
              'leading-1 font-semibold',
              isClosed ? 'text-red-600' : 'text-green-700',
            )}
          >
            {status}
          </span>
        </p>
      </div>
    </div>
  )
}
