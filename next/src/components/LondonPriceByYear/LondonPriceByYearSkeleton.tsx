import type { FC } from 'react'
import LondonPriceByYearValues from '~/src/components/LondonPriceByYear/LondonPriceByYearValues'
import LondonPriceColumnTitles from '~/src/components/LondonPriceByYear/LondonPriceColumnTitles'
import Table from '~/src/components/Table/Table'
import useWeight from '~/src/hooks/Weight/useWeight'
import WeightType from '~/src/types/WeightSelect/WeightType'
import cs from '~/src/utils/cs'
import { CurrencySelectCNY } from '../CurrencySelect'
import { TimeSelect } from '../year-select/year-select.component'
import styles from './LondonPriceByYear.module.scss'

interface Props {
  isFetching: boolean
}

const LondonPriceByYearSkeleton: FC<Props> = ({ isFetching }) => {
  const weight = useWeight(WeightType.PreciousMetals)
  return (
    <>
      <div className="mb-2 flex items-center justify-end">
        <CurrencySelectCNY classNamesListbox={styles.listbox} />
        <div className={cs(['pl-4'])}>
          <TimeSelect styleSelect={styles.selectStyle} />
        </div>
      </div>
      <Table title="Shanghai Fix Latest Price">
        <LondonPriceColumnTitles />
        <LondonPriceByYearValues
          timestamp={undefined}
          goldAM={undefined}
          goldPM={undefined}
          palladiumAM={undefined}
          palladiumPM={undefined}
          platinumAM={undefined}
          platinumPM={undefined}
          silver={undefined}
          isLoading={isFetching}
          weight={weight}
          idx={0}
        />
      </Table>
      <div className="mt-10" />
      <Table title="Shanghai Fix Historical Prices">
        <div className="no-scrollbar overflow-hidden overflow-x-scroll">
          <LondonPriceColumnTitles />
          <div className="h-[350px] overflow-x-hidden">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((_) => (
              <LondonPriceByYearValues
                timestamp={undefined}
                goldAM={undefined}
                goldPM={undefined}
                palladiumAM={undefined}
                palladiumPM={undefined}
                platinumAM={undefined}
                platinumPM={undefined}
                silver={undefined}
                isLoading={isFetching}
                weight={weight}
                idx={undefined}
                key={weight.label}
              />
            ))}
          </div>
        </div>
      </Table>
    </>
  )
}

export default LondonPriceByYearSkeleton
