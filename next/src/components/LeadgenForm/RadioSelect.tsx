import { Field, Label, Radio, RadioGroup } from '@headlessui/react'
import React, { useState } from 'react'

const ListItem = [
  { id: 1, name: 'Shareholder' },
  { id: 2, name: 'Broker' },
  { id: 3, name: 'Fund manager' },
  { id: 4, name: 'Potential investor' },
  { id: 5, name: 'Analyst/Researcher' },
  { id: 6, name: 'Field professional' },
]

interface Props {
  defaultValue: { id: string; name: string }
  onChecked: (val) => void
  isDisabled?: boolean
}

const RadioSelect: React.FC<Props> = (Props) => {
  const { defaultValue, onChecked, isDisabled } = Props
  let [selected, setSelected] = useState(
    ListItem.find((item) => item.name === defaultValue?.name),
  )
  const onCheckedChange = (value) => {
    setSelected(value)
    onChecked(value)
  }

  return (
    <RadioGroup
      value={selected}
      onChange={onCheckedChange}
      aria-label="Server size"
      className="flex flex-wrap gap-3 text-sm"
    >
      {ListItem.map((item) => (
        <Field
          key={item.name}
          disabled={isDisabled}
          className="flex inline-flex items-center gap-2"
        >
          <Radio
            value={item}
            className="group flex size-5 items-center justify-center rounded-full border bg-white data-[checked]:bg-yellow-500 data-[disabled]:bg-gray-100 hover:"
          >
            <span className="invisible size-2 rounded-full bg-white group-data-[checked]:visible" />
          </Radio>
          <Label className="data-[disabled]:opacity-50">{item.name}</Label>
        </Field>
      ))}
    </RadioGroup>
  )
}

export default RadioSelect
