import { useState } from 'react'
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3'
import { useForm } from 'react-hook-form'
import { RxCheckCircled } from 'react-icons/rx'
import Checkbox from '../Auth/Form/Elements/Checkbox'
import RadioSelect from './RadioSelect'

interface FormInput {
  name: string
  email: string
  phone: string
  location: string
  postcode: string
}

const DATA_CHECKBOX = [
  {
    id: '1',
    checked: false,
    label: 'Add me to the email list for future news.',
  },
  {
    id: '2',
    checked: false,
    label: 'Send me an investor package via email.',
  },
  {
    id: '3',
    checked: false,
    label: 'Contact me directly.',
  },
]

interface Props {
  dataProps: { title: string; urlAlias: string }
}

const LeadgenForm: React.FC<Props> = (Props) => {
  const { dataProps } = Props
  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<FormInput>()

  const [checkList, setCheckList] = useState(DATA_CHECKBOX)
  // const [selected, setSelected] = useState<ItemSelect>({ name: "United States", code: "US" })
  const [checkRadio, setCheckRadio] = useState(null)
  const [isprocess, setIsprocess] = useState(false)
  const { executeRecaptcha } = useGoogleReCaptcha()
  const [msgForm, setMsgForm] = useState({ type: '', textMsg: '' })

  const onSubmit = async (data: FormInput) => {
    const filterData = {}
    Object.keys(data).forEach((key) => {
      if (data[key]) {
        filterData[key] = data[key]
      }
    })

    if (!executeRecaptcha) {
      setMsgForm({
        type: 'error',
        textMsg: 'Execute recaptcha not yet available',
      })
      return
    }

    executeRecaptcha('submitLeagForm').then((gReCaptchaToken) => {
      setIsprocess(true)
      const bodyData = {
        ...filterData,
        role: checkRadio?.name,
        investorInfo: checkList.filter((check) => check.checked),
        gRecaptchaToken: gReCaptchaToken,
        slug: dataProps.urlAlias,
      }
      try {
        fetch(`${process.env.NEXT_PUBLIC_DRUPAL_URI}/api/leadgen-form`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...bodyData }),
        })
          .then((res) => {
            if (!res.ok) {
              setIsprocess(false)
              setMsgForm({
                type: 'error',
                textMsg: 'Sorry. An error occurred.',
              })
            }
            return res.json()
          })
          .then((data) => {
            const { status, message } = data
            setIsprocess(false)
            setMsgForm({ type: status, textMsg: message })
            // if (status !== 'error') {
            //   resetForm()
            // }
            return
          })
      } catch {
        setIsprocess(false)
        // resetForm()
        setMsgForm({ type: 'error', textMsg: 'Sorry. An error occurred.' })
      }
    })
  }

  // const resetForm = () => {
  //   setTimeout(() => {
  //     reset({
  //       name: '',
  //       email: '',
  //       phone: '',
  //       location: '',
  //       postcode: '',
  //     })
  //     setCheckList(DATA_CHECKBOX);
  //     setCheckRadio({ id: "3", name: 'Fund manager' });
  //   }, 2000)
  // }

  const onChange = (val) => {
    setCheckList(
      checkList.map((select) => {
        if (select.id.toString() === val) {
          return { ...select, checked: !select.checked }
        }
        return select
      }),
    )
  }

  if (msgForm.type === 'success') {
    return (
      <div className="pb-5 md:px-0 mx-auto mt-5 bg-gray-50">
        <div className="pl-3 flex flex-col py-4 bg-black">
          <h2 className="mb-2 md:mb-0 text-[18px] md:text-[24px] text-yellow-600">
            Free Mining Sector Investor Information
          </h2>
          <p className="md:mb-0 text-[20px] text-white">Sponsored content</p>
        </div>
        <div className="p-5 mb-5">
          <div className="flex items-center py-5 justify-center">
            <RxCheckCircled color="#eebc4f" size={100} />
          </div>
          <div className="text-[36px] text-[#eebc4f]">
            Thank you for your submission.
          </div>
          <div className="text-[18px] text-gray-black">
            You’ll receive the confirmation email shortly.
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="pb-5 md:px-0 mx-auto mt-5 bg-gray-50">
      <div className="pl-3 flex flex-col py-4 bg-black">
        <h2 className="mb-2 md:mb-0 text-[18px] md:text-[24px] text-yellow-600">
          Free Mining Sector Investor Information
        </h2>
        <p className="md:mb-0 text-[20px] text-white">Sponsored content</p>
      </div>
      <div className="p-5">
        <div className="mb-3 flex items-center gap-3">
          <p className="font-semibold text-base text-gray-900">
            Selected company :
          </p>
          <div className="text-gray-900">{dataProps.title}</div>
        </div>
        <div className="mb-3">
          <p className="pb-3 font-semibold text-base text-gray-900">
            Choose Investor Information
          </p>
          <div className="flex flex-col">
            {checkList.map((checkbox) => (
              <div className="pb-2 pl-5">
                <Checkbox
                  key={checkbox.id}
                  checked={checkbox.checked}
                  id={checkbox.id.toString()}
                  label={checkbox.label}
                  onChange={() =>
                    isprocess ? null : onChange(checkbox.id.toString())
                  }
                />
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-col lg:flex-row items-center pb-3 ">
          <p className="font-semibold text-base text-gray-900 mr-2">
            {' '}
            Complete Your Subscription
          </p>
          {/* <p className='text-rose-600'> (*Required fields)</p> */}
        </div>
        <form
          className="mt-5 text-xs lg:mt-0 md:basis-full"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="flex flex-col ">
            <div className="flex flex-col flex-1">
              <div>
                <input
                  className={`block w-full p-2.5 mt-2.5 bg-transparent border border-gray-400 text-gray-600 h-9 focus:outline-none ${errors.name ? 'border border-red-600' : ''}`}
                  placeholder="Name"
                  {...register('name', {
                    required: {
                      value: true,
                      message: 'Name field is empty',
                    },
                  })}
                  autoComplete="off"
                  disabled={isprocess}
                />
                {errors['name']?.message && (
                  <span className="text-red-600 ">
                    {errors['name']?.message}
                  </span>
                )}
              </div>
              <div>
                <input
                  className={`block w-full p-2.5 mt-2.5 bg-transparent border border-gray-400 text-gray-600 h-9 focus:outline-none ${errors.email ? 'border border-red-600' : ''}`}
                  placeholder="Email"
                  type="email"
                  disabled={isprocess}
                  {...register('email', {
                    required: {
                      value: true,
                      message: 'Email field is empty',
                    },
                    pattern: {
                      value:
                        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                  })}
                />
                {errors['email']?.message && (
                  <span className="text-red-600 ">
                    {errors['email']?.message}
                  </span>
                )}
              </div>
            </div>
            <div className="flex-1">
              <input
                className="block w-full p-2.5 mt-2.5 bg-transparent border border-gray-400 text-gray-600 h-9 focus:outline-none"
                placeholder="Phone"
                type="phone"
                autoComplete="off"
                disabled={isprocess}
                {...register('phone', { required: false })}
              />
              <input
                className="block w-full p-2.5 mt-2.5 bg-transparent border border-gray-400 text-gray-600 h-9 focus:outline-none"
                placeholder="Location"
                autoComplete="off"
                disabled={isprocess}
                {...register('location', { required: false })}
              />
            </div>
          </div>
          <div className="flex flex-col gap-5">
            <div className="flex-1">
              <input
                className="block w-full p-2.5 mt-2.5 bg-transparent border border-gray-400 text-gray-600 h-9 focus:outline-none"
                placeholder="Postal/zip"
                autoComplete="off"
                disabled={isprocess}
                {...register('postcode', { required: false })}
              />
            </div>
            <div className="flex-1" />
          </div>
          <div className="pt-5">
            <p className="pb-3 font-semibold text-base text-gray-900">
              Which of the following more accurately describes you?
            </p>
            <div className="flex flex-col">
              <RadioSelect
                defaultValue={checkRadio}
                onChecked={setCheckRadio}
                isDisabled={isprocess}
              />
            </div>
          </div>
          {msgForm.type === 'error' && (
            <div className="text-red-600 py-5">{msgForm.textMsg}</div>
          )}
          <div className="flex mt-8">
            <button
              className="py-3 px-6 border-0 bg-gray-900 text-white uppercase font-bold rounded leading-none"
              type="submit"
              disabled={isprocess}
            >
              {isprocess ? 'processing' : 'Submit'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default LeadgenForm
