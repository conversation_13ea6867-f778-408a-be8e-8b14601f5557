import dayjs from 'dayjs'
import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import { renderFn } from '~/src/utils/SangHai/priceConversion'
import cs from '~/src/utils/cs'

const options = { defaultWeight: 'GRAM' }

export const Values = ({ timestamp, am, pm, idx, isLoading, weight }) => (
  <div
    className={cs([
      `grid grid-cols-3 items-center border-b border-gray-200 px-2 py-1 ${
        !(idx % 2) && 'bg-alt-row'
      }`,
      !isLoading ? 'undefined' : 'opacity-50',
    ])}
  >
    <h4 className="text-gray-600 sm:text-sm lg:text-lg">
      {timestamp ? (
        dayjs.unix(timestamp).format('MMM DD, YYYY')
      ) : (
        <SkeletonTable />
      )}
    </h4>
    <h3 className="text-gray-800 sm:text-sm lg:text-lg">
      {(am ?? Number.NaN) !== am ? (
        <SkeletonTable />
      ) : (
        renderFn(weight, am, options)
      )}
    </h3>
    <h3 className="text-gray-800 sm:text-sm lg:text-lg">
      {(pm ?? Number.NaN) !== pm ? (
        <SkeletonTable />
      ) : (
        renderFn(weight, pm, options)
      )}
    </h3>
  </div>
)
