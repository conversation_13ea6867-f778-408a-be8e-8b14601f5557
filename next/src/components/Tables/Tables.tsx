/**
 * Building blocks for tables
 * If you need to see usage examples, please check out
 * src/components-cryptos/CryptosTable/CryptosTable.tsx
 */
import clsx from 'clsx'
import {
  Cell as CellAria,
  Column,
  Row as RowAria,
  Table,
  TableBody,
  TableHeader,
} from 'react-aria-components'

type BaseProps = {
  children: React.ReactNode
}

const Root = (props: BaseProps & { label: string }) => (
  <div className="w-full max-w-full overflow-x-scroll tablet:overflow-x-visible">
    <Table aria-label={props.label} className="w-full" selectionMode="single">
      {props.children}
    </Table>
  </div>
)

const Header = (props: BaseProps) => <TableHeader>{props.children}</TableHeader>

const ColumnHeader = (
  props: BaseProps & { isRowHeader?: boolean; index: number },
) => (
  <Column
    className={clsx(
      'text-[#898989]',
      props.index !== 1
        ? clsx('relative text-right')
        : clsx(
            'absolute text-left tablet:sticky',
            'left-0 z-20 w-[146px] bg-white',
          ),
    )}
    isRowHeader
  >
    <div
      className={clsx(
        'py-2',
        props.index !== 1
          ? clsx('')
          : clsx(
              'sticky left-0 top-0 z-20 h-full w-full',
              'shadow-[rgba(0,0,15,0.075)_2px_0px_4px_0px] md:shadow-noshadow',
              'flex w-[145px] items-center px-3',
            ),
      )}
    >
      {props.children}
    </div>
  </Column>
)

const Body = (props: BaseProps) => <TableBody>{props.children}</TableBody>

const Row = (props: BaseProps & { index: number }) => (
  <RowAria
    className={clsx(
      'h-14 bg-white tablet:bg-transparent',
      'hover:bg-[#F8F8F8]',
      'group cursor-pointer active:bg-[#EFF5FF]',
      'focus-visible:outline-none',
      '[&[data-selected]>td]:bg-[#EFF5FF]',
    )}
  >
    {props.children}
  </RowAria>
)

const RowLabelCell = (props: BaseProps & { index: number }) => (
  <CellAria
    className={clsx(
      'absolute left-0 z-20 bg-white tablet:static tablet:bg-transparent',
      'h-14 w-[146px] tablet:w-full',
      'flex items-center',
      'shadow-[rgba(0,0,15,0.075)_2px_0px_4px_0px] md:shadow-noshadow',
      'tablet:border-t tablet:border-[#E2E8F0]',
    )}
  >
    {props.children}
  </CellAria>
)

const NumberCell = (props: BaseProps) => (
  <CellAria className="border-t border-[#E2E8F0] px-2 text-right">
    {props.children}
  </CellAria>
)

export { Body, ColumnHeader, Header, NumberCell, Root, Row, RowLabelCell }
