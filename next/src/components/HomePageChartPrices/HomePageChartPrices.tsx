import clsx from 'clsx'
import type { FC, ReactNode } from 'react'
import type { MetalQuoteQuery } from '~/src/generated'
import isNegative from '~/src/utils/isNegative'
import priceFormatter from '~/src/utils/priceFormatter'
import styles from './HomePageChartPrices.module.scss'

/**
 * Props for the HomePageChartPrices component.
 *
 * @interface HomePageChartPricesProps
 * @property {MetalQuoteQuery} data - The current data for the chart.
 */
type HomePageChartPricesProps = {
  data: MetalQuoteQuery
}

/**
 * HomePageChartPrices component.
 *
 * @param {HomePageChartPricesProps} props - The props for the component.
 * @returns {JSX.Element} - The rendered component.
 */
const HomePageChartPrices: FC<HomePageChartPricesProps> = ({ data }) => {
  // Get the value from the data
  const value = data?.GetMetalQuoteV3?.results?.[0]

  return (
    <>
      <Grid>
        <Cell label="BID">
          <div className={clsx('text-right font-medium')}>
            {priceFormatter(value?.bid)}
          </div>
        </Cell>
        <Cell label="ASK">
          <div className={clsx('text-right font-medium')}>
            {priceFormatter(value?.ask)}
          </div>
        </Cell>
        <Cell label="+/-">
          <ColorizedValue basis={value?.change}>
            <span>{priceFormatter(value?.change)}</span>
          </ColorizedValue>
        </Cell>
        <Cell label="%">
          <ColorizedValue basis={value?.changePercentage}>
            <span>{priceFormatter(value?.changePercentage)}</span>
          </ColorizedValue>
        </Cell>
      </Grid>
    </>
  )
}

export default HomePageChartPrices

type GridProps = { children: ReactNode }

const Grid: FC<GridProps> = ({ children }) => {
  return <div className="flex justify-between gap-2">{children}</div>
}

type CellProps = { label: string; children: ReactNode }

const Cell: FC<CellProps> = ({ children, label }) => {
  return (
    <div className="flex flex-col gap-[2px]">
      <div className={clsx('text-right text-xs text-black/40')}>{label}</div>
      {children}
    </div>
  )
}

type ColorizedValueProps = { basis: number; children: ReactNode }

const ColorizedValue: FC<ColorizedValueProps> = ({ basis, children }) => {
  return (
    <div
      className={clsx(
        isNegative(basis) ? styles.down : styles.up,
        'text-right font-medium',
      )}
    >
      {children}
    </div>
  )
}
