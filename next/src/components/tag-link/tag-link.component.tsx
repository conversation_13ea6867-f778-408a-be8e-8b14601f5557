import Link from 'next/link'
import type { <PERSON> } from 'react'
import cs from '~/src/utils/cs'

export const TagLink: FC<{
  href: string
  name: string
}> = ({ href, name }) => {
  return (
    <Link
      className={cs([
        'rounded-md border border-[#111111] border-opacity-20 px-2',
        'flex items-center justify-center font-normal',
      ])}
      href={href}
    >
      <span className="my-1 text-xs text-[#111] text-opacity-80">{name}</span>
    </Link>
  )
}
