import { useEffect, useState } from 'react'
import { est } from '~/src/utils/time'

/**
 * MiniLabel component
 *
 * @param name
 * @constructor
 */
export const MiniLabel: React.FC<{ name: string }> = ({ name }) => {
  // Current time
  const [currentTime, setCurrentTime] = useState<string>('')

  // Function to update current time every second
  const updateCurrentTime = () => {
    setCurrentTime(`${est().format('MMM DD, YYYY - H:mm')} NY Time`)
  }

  // Update current time every second
  useEffect(() => {
    const intervalId = setInterval(updateCurrentTime, 1000) // 1000ms = 1s
    return () => clearInterval(intervalId)
  }, [])

  return (
    <div className="mb-6 ml-0.5">
      <h2 className="font-mulish mb-0.5 text-[15px] font-bold capitalize text-[#000000]">
        Live {name} Price
      </h2>
      <p
        className="text-[13px] font-normal text-[#595959]"
        suppressHydrationWarning
      >
        {currentTime}
      </p>
    </div>
  )
}
