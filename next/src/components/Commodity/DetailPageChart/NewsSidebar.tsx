import dynamic from 'next/dynamic'
import { useEffect } from 'react'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'
import { recordABTest } from '~/src/hooks/Global/GoogleTag'
import type { UTMParams } from '~/src/hooks/Global/UTMGenerator'
import useABTest from '~/src/hooks/Marketing/useABTest'
import { CommoditiesNewsSidebarQueries } from '~/src/lib/Commodities/NewsSidebar'
import kitcoQuery from '~/src/services/database/kitcoQuery'

/**
 * Latest news component props
 */
interface LatestNewsProps {
  name: string
}

interface AdvertisingSlotProps {
  id: string
  className?: string
}

// Dynamic Import with Explicit Typing
const AdvertisingSlot = dynamic(
  () => import('react-advertising').then((mod) => mod.AdvertisingSlot),
  {
    ssr: false,
  },
) as React.FC<AdvertisingSlotProps>

/**
 * Latest news component
 *
 * @param name - The name of the commodity (e.g. gold, silver, etc.)
 * @constructor
 */
const NewsSidebar = ({ name }: LatestNewsProps) => {
  // Number of news to show
  const newsLimit: number = 5

  // Use AB Test hook to determine variant
  const { variant, experimentId } = useABTest('commodity-news-sidebar', [
    'A',
    'B',
  ])
  let windowWidth = null

  if (typeof window !== 'undefined') {
    windowWidth = window.innerWidth
  }

  // Record the AB Test view
  useEffect(() => {
    if (variant) {
      recordABTest(
        'ab_view',
        experimentId,
        variant,
        variant === 'A' ? 'latest_news' : 'trending_news',
      )
    }
  }, [variant, experimentId])

  // Conditional queries based on the variant
  const { data: latestNewsData } = kitcoQuery(
    CommoditiesNewsSidebarQueries.commoditiesNewsSidebarLatestNews({
      variables: {
        urlAlias: '/news/category/commodities',
        limit: newsLimit,
        offset: 0,
      },
      options: { enabled: variant === 'A' },
    }),
  )

  const { data: trendingNewsData } = kitcoQuery(
    CommoditiesNewsSidebarQueries.commoditiesNewsSidebarTrending({
      variables: { limit: newsLimit },
      options: { enabled: variant === 'B' },
    }),
  )

  // UTM Parameters
  const utmParams: UTMParams = {
    source: 'site_navigation',
    medium: 'sidebar_link',
    campaign: variant === 'A' ? 'latest_news' : 'trending_news',
    term: `${name}_ab_sidebar_news`,
  }

  return (
    <>
      {windowWidth >= 1270 && (
        <AdvertisingSlot
          id={'right-rail-1'}
          className="mx-auto mb-[15px] h-[250px] w-[300px]"
        />
      )}

      {/* AB Test variant A - Display Latest News */}
      {variant === 'A' && (
        <div className="flex flex-col">
          <h2 className="border-b border-ktc-borders pb-2.5 text-[20px] uppercase">
            <span>Latest News</span>
          </h2>
          <div className="flex flex-grow flex-col">
            {latestNewsData?.nodeListByCategory?.items.map(
              (x: ArticleTeaserFragmentFragment) => {
                return (
                  <div className="mt-5 flex" key={x.id}>
                    <TeaserTextOnly
                      key={x?.id}
                      node={x}
                      hideSummary={true}
                      size={'sm'}
                      utmParams={utmParams}
                    />
                  </div>
                )
              },
            )}
          </div>
        </div>
      )}

      {/* AB Test variant B - Display Trending News */}
      {variant === 'B' && (
        <div className="flex flex-col mt-8">
          <h2 className="border-b border-ktc-borders pb-2.5 text-[20px] uppercase">
            <span>Trending News</span>
          </h2>
          <div className="flex flex-grow flex-col">
            {trendingNewsData?.nodeListTrending?.map(
              (x: ArticleTeaserFragmentFragment) => {
                return (
                  <div className="mt-5 flex" key={x.id}>
                    <TeaserTextOnly
                      key={x?.id}
                      node={x}
                      hideSummary={true}
                      size={'sm'}
                      utmParams={utmParams}
                    />
                  </div>
                )
              },
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default NewsSidebar
