import Link from 'next/link'
import type { FC } from 'react'
import type { ReportersQuery } from '~/src/generated'
import styles from './reporters-sidebar.module.scss'

export const ReportersSidebar: FC<{ data: ReportersQuery }> = ({ data }) => {
  return (
    <div className={styles.reportersSidebar}>
      <div>
        <h2>Reporters</h2>
        <ul>
          {data?.reporters?.map((reporter) => (
            <li key={reporter.id}>
              <Link href={reporter.urlAlias}>
                <div className={styles.imgWrapperCircle}>
                  <img
                    className="h-[30px] w-[30px] bg-[#f7f7f7] object-cover"
                    src={reporter.imageUrl ?? '/default-avatar.svg'}
                    alt={reporter.name}
                  />
                </div>
                {reporter.name}
                <span>&gt;</span>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}
