import clsx from 'clsx'
import Link from 'next/link'
import { type FC, Suspense } from 'react'
import { HiArrowSmDown, HiArrowSmUp } from 'react-icons/hi'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import { markets } from '~/src/lib/markets-factory.lib'
import colorize from '~/src/utils/colorize'
import cs from '~/src/utils/cs'
import isNegative from '~/src/utils/isNegative'
import * as timestamps from '~/src/utils/timestamps'
import { ErrBoundary } from '../ErrBoundary/ErrBoundary'
import { Query } from '../Query/Query'
import styles from './gold-indicators.module.scss'

export const GoldIndicators: FC = () => {
  const fetcher = markets.barchartsGoldIndicators({
    variables: {
      symbols: '$TTGD,$XAU,$HUI',
      timestamp: timestamps.current(),
    },
  })

  const arrowUpOrDown = (v: number) => {
    if (!isNegative(v)) {
      return 'up'
    }
    return 'down'
  }

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data }) => {
            return (
              <BlockShell title="Gold Indicators">
                {data?.GetBarchartQuotes?.results.map((x, idx: number) => (
                  <div
                    className={clsx(
                      `${
                        !(idx % 2)
                          ? styles.indexContainer
                          : cs([styles.indexContainer, styles.isOdd])
                      }`,
                      'flex justify-end',
                    )}
                    key={x.symbol}
                  >
                    <h3 className="mr-auto flex items-center">
                      {arrowUpOrDown(x.percentChange) === 'up' ? (
                        <HiArrowSmUp color="#18A751" />
                      ) : (
                        <HiArrowSmDown color="#A70202" />
                      )}
                      &nbsp;
                      <Link
                        href={`/markets/indices/${x.symbol}`}
                        className="text-kitco-black"
                      >
                        {
                          {
                            $HUI: 'HUI',
                            $TTGD: 'TSX',
                            $XAU: 'XAU',
                          }[x.symbol]
                        }
                      </Link>
                    </h3>
                    <p
                      className={clsx(
                        colorize(x.percentChange),
                        'mr-[40px] lg:mr-5',
                      )}
                    >
                      {x.lastPrice}
                    </p>
                    <p className={colorize(x.percentChange)}>
                      {x.percentChange}%
                    </p>
                  </div>
                ))}
              </BlockShell>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}
