import Link from 'next/link'
import { useRouter } from 'next/router'
import dataJeweler from '~/src/lib/dataJeweler'
import cs from '~/src/utils/cs'
import styles from './JewelryTitle.module.scss'

export const JewelryTitle = () => {
  const r = useRouter()

  const baseH1 = 'uppercase text-[32px] md:text-[48px]'
  const baseSubH1 =
    'uppercase text-[32px] md:leading-[58px] leading-[38px] md:text-[48px]'

  const parentCSS = 'text-ktc-date-gray hidden md:block'

  const subTitle =
    dataJeweler.find((item) => item.urlAlias === r.asPath)?.name ?? ''

  const showTitle = 'Jeweler Resources'
  const handleLink = '/jeweler-resources'

  return (
    <>
      <div className="block items-center justify-between gap-5 md:flex">
        <div className={styles.leftTitle}>
          <div className="flex flex-wrap items-center leading-[38px] md:leading-[58px]">
            <Link href={handleLink}>
              <h1 className={cs([baseH1, parentCSS])}>{showTitle}</h1>
            </Link>
            <h1 className={cs([baseH1, parentCSS, 'px-1 md:px-2'])}>/</h1>
            <h1 className={cs([baseSubH1, 'text-kitco-black'])}>{subTitle}</h1>
          </div>
        </div>
      </div>
    </>
  )
}

export const JewelryResourcesTitle = () => {
  const r = useRouter()

  const baseH1 = 'uppercase text-[32px] md:text-[48px]'
  const baseSubH1 =
    'uppercase text-[32px] md:leading-[58px] leading-[38px] md:text-[48px]'

  const parentCSS = 'text-ktc-date-gray hidden md:block'

  const subTitle =
    dataJeweler.find((item) => item.urlAlias === r.asPath)?.name ?? ''

  const showTitle = 'Jeweler Resources'

  return (
    <>
      <div className="mb-5 block items-center justify-between gap-5 md:mb-10 lg:flex">
        <div className={styles.leftResourcesTitle}>
          <div className="flex flex-wrap items-center leading-[38px] md:leading-[58px]">
            <h1 className={cs([baseH1, parentCSS])}>{showTitle}</h1>

            <h1 className={cs([baseH1, parentCSS, 'px-1 md:px-2'])}>/</h1>
            <h1 className={cs([baseSubH1, 'text-kitco-black'])}>{subTitle}</h1>
          </div>

          <div className="mb-[34px] mt-2.5 !font-lato text-[1.125rem] text-xl font-bold leading-7 md:mb-0 md:mt-0 md:w-[540px]">
            Calculate gold scrap right here. Real-time gold scrap value
            calculator for professionals.
          </div>
        </div>
      </div>
    </>
  )
}
