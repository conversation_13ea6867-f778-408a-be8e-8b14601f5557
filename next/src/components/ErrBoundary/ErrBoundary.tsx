import { clsx } from 'clsx'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { ErrorBoundary, type FallbackProps } from 'react-error-boundary'
import { FiAlertTriangle } from 'react-icons/fi'

export const ErrBoundary: React.FC<{
  children: React.ReactNode
  /**
   * If true, the error boundary will reset after 3 seconds
   */
  enableReset?: boolean
  errorTitle?: string
  enableDarkMode?: boolean
}> = ({ children, enableReset, errorTitle, enableDarkMode }) => {
  // 	const { mutate } = useMutation<any>("/api/logger/component-error")
  // const logError = (error: Error, info: { componentStack: string }) => {
  //   console.error(error, info)
  // 		mutate({ error, info })
  // }

  return (
    <ErrorBoundary
      fallbackRender={(props) => (
        <ErrorFallback
          {...props}
          enableReset={enableReset}
          errorTitle={errorTitle}
          enableDarkMode={enableDarkMode}
        />
      )}
      onError={() => {
        return
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

interface ErrorFallbackProps extends FallbackProps {
  enableReset?: boolean
  errorTitle?: string
  enableDarkMode?: boolean
}

function ErrorFallback({
  resetErrorBoundary,
  enableReset,
  errorTitle,
  enableDarkMode = false,
}: ErrorFallbackProps) {
  // when component mounted, call resetErrorBoundary to reset the error boundary

  const router = useRouter()

  useEffect(() => {
    if (enableReset) {
      const timeout = setTimeout(() => {
        resetErrorBoundary()
      }, 3000)

      return () => {
        clearTimeout(timeout)
      }
    }
  }, [])

  return (
    <div className="relative flex w-full flex-col justify-center border border-gray-300 bg-white">
      {errorTitle && (
        <h1
          className={clsx(
            'flex w-full items-center justify-center bg-black text-base font-semibold capitalize text-white',
            {
              'bg-black text-white': !enableDarkMode,
              'bg-white text-black': enableDarkMode,
            },
          )}
        >
          {errorTitle}
        </h1>
      )}
      <div className="mx-3 flex items-center justify-center gap-2 py-3">
        <FiAlertTriangle fontSize={20} color="#ce0404" />
        <div className="text-lg font-thin leading-tight text-gray-900">
          Something went wrong
        </div>
      </div>
      <p className="mb-3 text-center text-xs font-thin text-gray-900">
        Reloading page may fix this
      </p>
      <div className="mb-3 flex justify-center">
        <button
          onClick={() => router.reload()}
          type="button"
          className="inline-flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-xs font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:bg-gray-800 dark:focus:ring-gray-600 "
        >
          Reload
        </button>
      </div>
    </div>
  )
}
