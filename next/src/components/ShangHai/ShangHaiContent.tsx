import ShanghaiPriceByYear from '~/src/components/ShanghaiPriceByYear/ShanghaiPriceByYear'
import ShanghaiPriceByYearSkeleton, {
  ShanghaiPriceByYearNoData,
} from '~/src/components/ShanghaiPriceByYear/ShanghaiPriceByYearSkeleton'
import conversionRateFromCNY from '~/src/utils/Conversion/conversionRateFromCNY'

const ShangHaiContent = ({
  data,
  transformedData,
  currency,
  currencies,
  isFetchingData,
}) => {
  if (!data?.GetShanghaiFixByYearV3?.results) {
    return <ShanghaiPriceByYearNoData />
  }

  if (transformedData?.latestTimestamp) {
    return (
      <ShanghaiPriceByYear
        data={data}
        symbol={currency.symbol}
        timeStamp={transformedData?.latestTimestamp}
        isLoading={isFetchingData}
        conversionRate={conversionRateFromCNY(currency.symbol, currencies)}
      />
    )
  }

  return <ShanghaiPriceByYearSkeleton isFetching={isFetchingData} />
}

export default ShangHaiContent
