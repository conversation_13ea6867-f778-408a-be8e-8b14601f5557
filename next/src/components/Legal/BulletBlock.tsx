import clsx from 'clsx'

type SectionProps = {
  children?: React.ReactNode
  className: string
}

export default ({ children, className }: SectionProps): JSX.Element => {
  return (
    <p
      className={clsx(className, '!mt-4')}
      style={{
        textAlign: 'justify',
        textJustify: 'inter-word',
        lineHeight: '16.0pt',
        marginBottom: '0pt',
      }}
    >
      {/* display bullet */}
      <span
        style={{
          fontSize: '10.0pt',
          fontFamily: 'Symbol',
          color: '#373737',
        }}
      >
        <span
          style={{
            fontFamily: 'Times New Roman',
            marginRight: '6pt',
          }}
        >
          &#8226;
        </span>
      </span>

      {/* content */}
      <span
        lang="EN-US"
        style={{
          fontSize: '12.0pt',
          fontFamily: '"Mulish",serif',
          color: '#373737',
        }}
      >
        {children}
      </span>
    </p>
  )
}
