type SectionProps = {
  children?: React.ReactNode
  className: string
}

export default ({ children, className }: SectionProps): JSX.Element => {
  return (
    <p
      className={className}
      style={{
        marginTop: '8pt',
        marginRight: '0cm',
        marginBottom: '0pt',
        textAlign: 'justify',
        textJustify: 'inter-word',
        lineHeight: '16.0pt',
      }}
    >
      <span
        lang="EN-US"
        style={{
          fontSize: '12.0pt',
          fontFamily: '"Mulish",serif',
          color: '#373737',
        }}
      >
        {children}
      </span>
    </p>
  )
}
