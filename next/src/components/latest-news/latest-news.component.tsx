import type { FC } from 'react'
import { Fragment } from 'react'
import { TeaserLabelTitleDate } from '~/src/components-news/ArticleTeasers/TeaserLabelTitleDate'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

// used on news landing and article detail page so far
export const LatestNewsNewsPages: FC = () => {
  const { data } = kitcoQuery(
    news.nodeListQueue({
      variables: {
        limit: 5,
        offset: 0,
        queueId: 'latest_news',
      },
    }),
  )
  return (
    <Fragment>
      <h3 className="text-base uppercase">
        <span>Latest News</span>
      </h3>
      <Spacer />
      {data?.queue?.items?.map((x: ArticleTeaserFragmentFragment) => (
        <div className="pb-4" key={x.id}>
          <TeaserLabelTitleDate node={x} size="sm" />
        </div>
      ))}
    </Fragment>
  )
}

const Spacer = () => <div className="my-2 h-[1px] bg-ktc-borders" />
