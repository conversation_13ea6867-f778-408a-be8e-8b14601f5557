import type { FC } from 'react'
import styles from './BreakingNewsWatchNow.module.scss'

const WatchNowSVG: FC<any> = () => {
  return (
    <div className="h-full w-auto">
      <svg
        className="h-full w-auto"
        version="1.1"
        id="Layer_1"
        x="0px"
        y="0px"
        viewBox="0 0 838 297"
        xmlSpace="preserve"
      >
        <g>
          <polygon points="528,0 402,0 0,0 0,297 405,297 529.3,297 677.1,149.1 	" />
          <polygon
            className={styles.st0}
            points="674.8,0 594.2,0 746.7,152.5 602.2,297 682.8,297 827.3,152.5 	"
          />
        </g>
        <path
          className={styles.st1}
          d="M47.6,106.8c-0.1,2.1-0.1,4-0.1,5.9v3.7c-0.1,2.2-0.3,4.4-0.4,6.6c-0.1,2.2-0.1,4.4-0.1,6.6h15
   c0-1.9,0-3.8-0.1-5.7c-0.1-1.9-0.2-3.8-0.3-5.7V79.5c0.1-2,0.2-4,0.3-6s0.1-4.3,0.1-6.3H47.1c0,4.9,0.1,10.2,0.3,15.2
   c0.2,4.9,0.3,9.9,0.3,14.9v3.8c0,0.2,0,0.5,0,0.7C47.7,103.4,47.7,105,47.6,106.8z M63.3,96.8c-0.3,0.4-0.6,0.7-0.7,0.9
   c-0.2,0.3-0.2,0.6-0.2,0.9c0,0.5,0.2,1,0.5,1.3l28.7,29.6h16.1l-32-33.4L103,67.2H89.6L63.3,96.8z"
        />
        <path
          className={styles.st2}
          d="M110.1,82.9c-0.2-5.2-0.3-10.6-0.3-15.7h13.9c0,5.2-0.1,10.6-0.4,15.8c-0.2,5.2-0.4,10.4-0.4,15.7
   c0,5.2,0.1,10.3,0.4,15.4c0.2,5.1,0.4,10.3,0.4,15.4h-14.3c0-5.2,0.2-10.4,0.5-15.6s0.5-10.4,0.5-15.6
   C110.3,93.2,110.3,88.1,110.1,82.9z"
        />
        <path
          className={styles.st2}
          d="M149.1,87.3c0-3.7-0.2-7.5-0.6-11.2H146c-0.4,0.1-1.4,0.2-3,0.4l-5.2,0.6l-5.2,0.6c-1.6,0.2-2.6,0.3-3,0.4H129
   V67.2h51.7v10.7c-2.6,0-5.2-0.2-7.7-0.7s-5.1-0.7-7.7-0.7c-0.2,0-0.6,0-1.2,0c-0.6,0-1,0-1.2,0c-0.1,2.2-0.2,4.2-0.2,6.1
   s-0.1,3.7-0.1,5.4c0,1.8,0,3.6,0,5.4v6c0,5.1,0.2,10.1,0.5,15.1s0.5,10,0.5,15.1h-15.1c0-5.2,0.1-10.4,0.4-15.6
   c0.3-5.2,0.4-10.4,0.4-15.6V87.3H149.1z"
        />
        <path
          className={styles.st2}
          d="M184.4,82.5c1.2-4.2,3.2-7.4,6-9.7c2.8-2.2,6.5-3.8,11-4.5c5.6-0.8,11.3-1.2,16.9-1.2h2.2c1,0,2,0,2.9,0.1
   s1.8,0.1,2.5,0.1h6.1c0.5,0.1,1,0.3,1.5,0.5c0.5,0.3,0.7,0.9,0.6,1.4c-0.1,0.3-0.2,1-0.4,1.9s-0.4,2.2-0.6,3.2
   c-0.2,1.1-0.5,2.1-0.7,3s-0.3,1.5-0.4,1.9c-0.7-0.1-1.3-0.5-1.7-1.1s-1-1-1.7-1.3c-1.7-0.6-3.4-1-5.2-1.1c-1.7-0.1-3.5-0.2-5.3-0.2
   c-3.5-0.1-6.9,0.4-10.2,1.5c-2.8,1-5.1,2.3-6.8,4.3c-1.8,2.2-3.1,4.7-3.7,7.4c-0.8,3.4-1.2,7-1.2,10.5c-0.1,3.5,0.3,7,1,10.4
   c0.5,2.7,1.7,5.2,3.4,7.4c1.7,2,4,3.5,6.5,4.3c2.7,0.9,6.2,1.4,10.5,1.4c2.1,0,4.2-0.1,6.5-0.3c2.2-0.2,4.3-0.7,6.4-1.4
   c0.6-0.2,1.1-0.6,1.6-1s1.2-0.6,1.9-0.6v7.9c0,0.1-0.6,0.4-1.7,0.8c-2.4,0.7-4.8,1.1-7.2,1.2c-2.3,0.1-4.8,0.1-7.3,0.1
   c-4.4,0-8.9-0.3-13.3-0.9c-4.4-0.6-8.7-2.1-12.6-4.3c-2.1-1.1-3.8-2.7-5-4.6c-1.2-2-2.2-4.2-2.8-6.4c-0.6-2.4-1-4.8-1.1-7.2
   c-0.1-2.4-0.2-4.7-0.2-6.9C182.5,92.5,183.1,86.7,184.4,82.5z"
        />
        <path
          className={styles.st1}
          d="M247.9,124.6c3.6,1.5,7.5,2.4,11.4,2.7c4.2,0.3,8.2,0.5,12.1,0.5c2.6,0,5.3-0.1,8.1-0.2c2.7-0.2,5.4-0.5,8.1-1
   c2.6-0.5,5.1-1.2,7.5-2.2c2.3-0.9,4.4-2.2,6.2-3.8c1.8-1.7,3.3-3.7,4.2-5.9c1.1-2.7,1.7-5.5,1.6-8.4V87.5c0.1-3.1-0.7-6.2-2.3-8.9
   c-1.5-2.5-3.6-4.7-6.1-6.4c-2.6-1.7-5.4-3-8.4-3.8c-3-0.8-6.2-1.2-9.3-1.2h-20.1c-3.2,0-6.4,0.5-9.4,1.5c-2.9,0.9-5.6,2.4-8,4.3
   c-2.3,1.9-4.2,4.2-5.6,6.8c-1.4,2.8-2.1,5.9-2.1,9v17.9c0,4.9,1.1,8.8,3.4,11.6C241.6,121,244.6,123.2,247.9,124.6z M253.8,115.3
   c-1.7-2.1-2.8-4.5-3.3-7.1l0,0c-0.7-3.5-1-7-0.9-10.5c-0.1-3.4,0.3-6.8,1.1-10.1c0.6-2.6,1.9-5.1,3.7-7.1c1.8-1.9,4.2-3.4,6.7-4.2
   c3.3-1,6.8-1.4,10.3-1.4c3.6-0.1,7.1,0.4,10.6,1.4c2.6,0.8,5,2.2,6.9,4.2c1.8,2.1,3.1,4.5,3.7,7.2c0.8,3.4,1.2,6.8,1.1,10.2
   c0,3.6-0.3,7.2-1.1,10.7c-0.5,2.6-1.8,5-3.6,7c-1.9,1.9-4.3,3.2-6.9,3.8c-3.6,0.9-7.3,1.2-10.9,1.2c-4.4,0-8-0.4-10.8-1.3
   C257.8,118.7,255.5,117.3,253.8,115.3z"
        />
        <path
          className={styles.st1}
          d="M313.4,59.4c0.6,0.6,1.1,1.4,1.6,2.1l1.3,2.1h-2.1l-0.9-1.7c-0.4-0.9-1-1.8-1.8-2.5c-0.4-0.3-0.9-0.4-1.4-0.4
   h-1v4.5h-1.7V52.9h3.7c0.9,0,1.8,0.1,2.7,0.3c0.4,0.1,0.7,0.4,1,0.7c0.5,0.5,0.8,1.2,0.8,2c0,0.7-0.3,1.5-0.8,2
   c-0.6,0.6-1.4,0.9-2.2,1C312.8,58.9,313.1,59.1,313.4,59.4z M309.1,57.6h2.1l0,0c0.7,0.1,1.4-0.1,2.1-0.4c0.4-0.3,0.6-0.7,0.5-1.2
   c0-0.3-0.1-0.6-0.3-0.8c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.3-0.2-0.4-0.2c-0.6-0.2-1.1-0.2-1.7-0.2h-1.9v3.1
   C309.2,57.6,309.1,57.6,309.1,57.6z"
        />
        <path
          className={styles.st1}
          d="M311.8,49.3c-5.4,0-9.8,4.4-9.8,9.8s4.4,9.8,9.8,9.8s9.8-4.4,9.8-9.8C321.6,53.7,317.2,49.3,311.8,49.3z
    M311.8,67.5c-4.6,0-8.4-3.8-8.4-8.4s3.8-8.4,8.4-8.4s8.4,3.8,8.4,8.4C320.2,63.8,316.5,67.5,311.8,67.5z"
        />
        <path
          className={styles.st2}
          d="M467.9,102.8h10.7l-5.4-16L467.9,102.8L467.9,102.8z"
        />
        <path
          className={styles.st2}
          d="M428.6,85h-7.1v26.7h7.1c4.9,0,8.9-0.9,8.9-6.3v-14C437.6,85.8,433.5,85,428.6,85z"
        />
        <path
          className={styles.st1}
          d="M328.5,67.2h182.3c1.4,0,2.7,0.5,3.6,1.5c1,0.9,1.5,2.2,1.5,3.6v50.6c0,2.8-2.3,5-5.1,5H328.5
   c-2.8,0-5.1-2.2-5.1-5V72.2C323.4,69.4,325.7,67.2,328.5,67.2z M377.4,114.4c1.4,0.2,2.8,0.2,4.2,0V80.5c-1.5-0.2-2.9-0.2-4.4,0
   l-11,20.5l-10.9-20.5c-1.5-0.2-3-0.2-4.6,0v33.8c1.4,0.2,2.8,0.2,4.2,0V87.5l9.7,18c1,0.2,2.1,0.2,3.1,0l9.6-17.9L377.4,114.4
   L377.4,114.4z M409.4,95.5c0.3,1.1,0.3,2.3,0,3.4h-14.5v12h15c0.3,1.1,0.3,2.3,0,3.5h-19.1V80.5h19.1c0.3,1.1,0.3,2.3,0,3.4h-15
   v11.5L409.4,95.5L409.4,95.5z M428.5,114.4c8.1,0,13.1-2.4,13.1-9.3V89.8c0-6.9-5-9.2-13.1-9.2h-11.2v33.8
   C417.3,114.4,428.5,114.4,428.5,114.4z M453.9,114.4c-1.4,0.2-2.8,0.2-4.2,0V80.5c1.4-0.2,2.8-0.2,4.2,0V114.4z M480.9,104.6
   l3.4,9.8l0,0c1.4,0.2,2.9,0.2,4.3,0l-12.2-33.9c-1.4-0.2-2.8-0.2-4.2,0L460,114.3c1.4,0.2,2.8,0.2,4.2,0l3.4-9.8L480.9,104.6
   L480.9,104.6z"
        />
        <g>
          <path
            className={styles.st2}
            d="M58,226.2l-12.7-53.3h11l8,36.6l9.7-36.6h12.8l9.3,37.2l8.2-37.2h10.8l-12.9,53.3H90.8l-10.6-39.8l-10.6,39.8
     H58z"
          />
          <path
            className={styles.st2}
            d="M164.6,226.2h-11.7l-4.7-12.1H127l-4.4,12.1h-11.4l20.8-53.3h11.4L164.6,226.2z M144.8,205.1l-7.3-19.8
     l-7.2,19.8H144.8z"
          />
          <path
            className={styles.st2}
            d="M176.8,226.2v-44.3H161v-9h42.3v9h-15.8v44.3H176.8z"
          />
          <path
            className={styles.st2}
            d="M244.4,206.6l10.4,3.3c-1.6,5.8-4.3,10.1-8,13c-3.7,2.8-8.4,4.2-14.2,4.2c-7.1,0-12.9-2.4-17.4-7.3
     c-4.6-4.8-6.8-11.4-6.8-19.8c0-8.9,2.3-15.8,6.9-20.7c4.6-4.9,10.6-7.4,18.1-7.4c6.5,0,11.8,1.9,15.9,5.8c2.4,2.3,4.2,5.5,5.5,9.8
     l-10.7,2.5c-0.6-2.8-1.9-4.9-3.9-6.5c-2-1.6-4.4-2.4-7.3-2.4c-4,0-7.2,1.4-9.6,4.3c-2.5,2.8-3.7,7.4-3.7,13.8
     c0,6.7,1.2,11.5,3.6,14.4c2.4,2.9,5.6,4.3,9.5,4.3c2.9,0,5.3-0.9,7.4-2.7C242,213.4,243.5,210.5,244.4,206.6z"
          />
          <path
            className={styles.st2}
            d="M264.1,226.2v-53.3h10.8v21h21.1v-21h10.8v53.3h-10.8v-23.3h-21.1v23.3H264.1z"
          />
          <path
            className={styles.st2}
            d="M338.6,226.2v-53.3h10.5l21.8,35.6v-35.6h10v53.3h-10.8l-21.5-34.8v34.8H338.6z"
          />
          <path
            className={styles.st2}
            d="M390.1,199.9c0-5.4,0.8-10,2.4-13.7c1.2-2.7,2.9-5.1,5-7.3c2.1-2.2,4.4-3.8,6.9-4.8c3.3-1.4,7.1-2.1,11.5-2.1
     c7.9,0,14.1,2.4,18.8,7.3c4.7,4.9,7.1,11.6,7.1,20.3c0,8.6-2.3,15.3-7,20.2c-4.7,4.9-10.9,7.3-18.8,7.3c-7.9,0-14.2-2.4-18.9-7.3
     C392.4,215,390.1,208.4,390.1,199.9z M401.2,199.5c0,6,1.4,10.6,4.2,13.7c2.8,3.1,6.3,4.7,10.6,4.7c4.3,0,7.8-1.5,10.6-4.6
     c2.8-3.1,4.1-7.7,4.1-13.9c0-6.1-1.3-10.7-4-13.7s-6.2-4.5-10.7-4.5c-4.4,0-8,1.5-10.7,4.6C402.5,188.8,401.2,193.4,401.2,199.5z"
          />
          <path
            className={styles.st2}
            d="M457.7,226.2L445,172.9h11l8,36.6l9.7-36.6h12.8l9.3,37.2l8.2-37.2h10.8L502,226.2h-11.4L480,186.4l-10.6,39.8
     H457.7z"
          />
        </g>
      </svg>
    </div>
  )
}

export default WatchNowSVG
