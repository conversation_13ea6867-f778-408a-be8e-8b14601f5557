import { BsCheck2Square, BsSquare } from 'react-icons/bs'
import cs from '~/src/utils/cs'
import styles from './ServicesHead.module.scss'

interface Props {
  isChooseAll: boolean
  toggle: (event: React.MouseEvent<HTMLElement>) => void
}

const ServicesHead = ({ isChooseAll, toggle }: Props) => {
  return (
    <>
      <h1
        className={cs([
          'text-[32px] uppercase leading-[38px] text-kitco-black md:text-[48px] md:leading-[58px]',
          styles.title,
        ])}
      >
        SUBSCRIBE NOW TO EXCLUSIVE NEWSLETTERS
      </h1>
      <div className="mb-10 flex items-center justify-between gap-3">
        <p className={cs([styles.subTitle, 'text-kitco-black'])}>
          Market alerts, expert analysis, & hand-picked content delivered
          straight to your inbox
        </p>
        <div className="hidden items-center justify-between md:flex">
          <span className={cs([styles.subscribeAll, 'mr-2'])}>
            SUBSCRIBE ALL
          </span>
          <div onClick={toggle} className="my-2 flex cursor-pointer">
            <div className="">
              {!isChooseAll && <BsSquare size={26} color="#707070" />}
              {isChooseAll && <BsCheck2Square size={26} color="#707070" />}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ServicesHead
