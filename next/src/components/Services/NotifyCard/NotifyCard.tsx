import type { FC } from 'react'
import { IoCheckmark } from 'react-icons/io5'
import cs from '~/src/utils/cs'
import styles from './NotifyCard.module.scss'

const NotifyCard: FC<{ loading: boolean }> = ({ loading }) => {
  return (
    <div className={styles.notify}>
      {loading && (
        <span
          className={cs([
            styles.dotsCircleSpinner,
            styles.loading,
            '!hidden lg:!mr-12 lg:!inline-block',
          ])}
        ></span>
      )}
      <IoCheckmark
        className="mx-auto block md:mr-1 md:inline-block"
        size={32}
      />
      Thank you for <br className="md:hidden" />
      subscribing
      {loading && (
        <span
          className={cs([
            styles.dotsCircleSpinner,
            styles.loading,
            'lg:!hidden',
          ])}
        ></span>
      )}
    </div>
  )
}

export default NotifyCard
