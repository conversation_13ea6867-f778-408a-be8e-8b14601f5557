import Image from 'next/image'
import Link from 'next/link'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import type { LeadGen } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import cs from '~/src/utils/cs'
import styles from './InvestorInformation.module.scss'

const InvestorInformation = () => {
  const { data } = kitcoQuery(
    news.leadGen({
      options: {
        enabled: true,
      },
    }),
  )

  const openNewWindow = (event, url) => {
    event.preventDefault()
    window.open(
      url,
      'LeadGen',
      'scrollbars=yes,resizable=yes,top=50,left=200,width=670,height=500',
    )
  }

  return (
    <BlockShell title={'Investor Information '}>
      <div>
        <h3 className={styles.subTitle}>Sponsored content</h3>
        <ul>
          {data?.queue?.items?.map((item: LeadGen) => (
            <li key={item.id} className={styles.leadGenItem}>
              <Link
                onClick={(event) => openNewWindow(event, item?.urlAlias)}
                href={item?.urlAlias}
                className={cs([
                  'flex items-center justify-between',
                  item?.featured && 'font-bold',
                ])}
              >
                <span>{item?.title}</span>
                <span>
                  {item?.featured && (
                    <Image
                      width={9}
                      height={18}
                      src={'/arrow-red.png'}
                      alt="leadGen featured"
                    />
                  )}
                  {!item?.featured && (
                    <Image
                      width={9}
                      height={18}
                      src={'/arrow-blue.png'}
                      alt="leadGen"
                    />
                  )}
                </span>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </BlockShell>
  )
}

export default InvestorInformation
