import { TZDate } from '@date-fns/tz'
import { format } from 'date-fns'
import { type FC, useEffect, useState } from 'react'

interface DateTimeProps {
  timeZone: string
  timeFormat?: string
  date?: string
}

const DateTime: FC<DateTimeProps> = ({
  date,
  timeZone = process.env.NEXT_PUBLIC_TIMEZONE || 'America/New_York',
  timeFormat = 'MMM dd, yyyy HH:mm',
}: DateTimeProps) => {
  const [formattedDate, setFormattedDate] = useState('')

  const setDate = () => {
    // Create a TZDate based on the provided date and time zone
    const tzDate = new TZDate(date ? new Date(date) : new Date(), timeZone)

    // Format the date based on the given time zone and format
    setFormattedDate(format(tzDate, timeFormat))
  }

  useEffect(() => {
    // Set the interval to update the date every minute
    const intervalId = setInterval(() => {
      setDate() // Update the date to the current one
    }, 30000) // 30000 ms = 0/1 minute

    // Clear the interval when the component unmounts
    return () => clearInterval(intervalId)
  }, [date])

  useEffect(() => {
    // Format the date with the provided time zone and format
    setDate()
  }, [])

  return <div>{formattedDate}</div>
}

export default DateTime
