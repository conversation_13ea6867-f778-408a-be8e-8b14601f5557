import clsx from 'clsx'
import Link from 'next/link'
import { FC, useEffect, useState } from 'react'
import BuySellButton from '~/src/components/BuySellButton/BuySellButton'
import type { MetalMonthAnnualQuery, MetalQuoteQuery } from '~/src/generated'
import { isUp } from '~/src/utils/Prices/isUp'
import { styleUpOrDown } from '~/src/utils/Prices/styleUpOrDown'
import priceFormatter from '~/src/utils/priceFormatter'
import styles from './MetalMonthHomePage.module.scss'

/**
 * MetalMonthHomePage component props
 *
 * @interface Props
 * @property {MetalQuoteQuery} data - The data for the component.
 * @property {MetalMonthAnnualQuery} historicalData - The historical data for the component.
 */
interface MetalMonthHomePageProps {
  data: MetalQuoteQuery
  historicalData: MetalMonthAnnualQuery
}

/**
 * MetalMonthHomePage component props
 *
 * @interface MetalMonthHomePageProps
 * @property {MetalQuoteQuery} data - The data for the component.
 * @property {MetalMonthAnnualQuery} historicalData - The historical data for the component.
 */
const MetalMonthHomePage: FC<MetalMonthHomePageProps> = ({
  data,
  historicalData,
}) => {
  // Extract the current data from the query
  const nowData = data?.GetMetalQuoteV3?.results?.[0]
  const hData = historicalData?.GetHistoricalPointsV3
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const ValuesRow = ({ title, valueLeft, valueRight, altBg }) => (
    <div className={`border-t border-gray-200 px-2 ${altBg && 'bg-gray-100'}`}>
      <div className={'flex py-1 desktop:py-[2px]'}>
        <p className={'text-sm capitalize desktop:text-[11.5px]'}>{title}</p>
        {isClient && (
          <>
            <span
              className={clsx(
                styleUpOrDown(valueLeft, styles),
                'ml-auto text-sm font-semibold desktop:text-[11.5px]',
              )}
            >
              {isUp(valueLeft) ? '+' : ''}
              {priceFormatter(valueLeft) || '-'}
            </span>
            <span
              className={clsx(
                styleUpOrDown(valueRight, styles),
                'flex min-w-[140px] justify-end text-sm font-semibold tablet:min-w-[155px] desktop:min-w-[55px] desktop:text-[11.5px]',
              )}
            >
              {isUp(valueRight) ? '+' : ''}
              {valueRight ? `${priceFormatter(valueRight)}%` : '-'}
            </span>
          </>
        )}
      </div>
    </div>
  )

  return (
    <>
      <div className={styles.table}>
        {/* BID ASK*/}
        <div className={'border-t border-gray-200 bg-gray-100 px-2'}>
          <div className={'flex py-1 desktop:py-[2px]'}>
            <p
              className={'text-sm font-bold capitalize  desktop:text-[11.5px]'}
            >
              {'Bid/Ask'}
            </p>
            <span className="ml-auto text-sm font-bold desktop:text-[11.5px]">
              {priceFormatter(nowData?.bid) || '-'}
            </span>
            <span className="flex min-w-[140px] justify-end text-sm font-bold tablet:min-w-[155px] desktop:min-w-[55px] desktop:text-[11.5px]">
              {priceFormatter(nowData?.ask) || '-'}
            </span>
          </div>
        </div>

        {/* HIGH LOW*/}
        <div className={'border-t border-gray-200 px-2'}>
          <div className={'flex py-1 desktop:py-[2px]'}>
            <p className={'text-sm capitalize  desktop:text-[11.5px]'}>
              {'Low/High'}
            </p>
            <span className="ml-auto text-sm font-semibold desktop:text-[11.5px]">
              {priceFormatter(nowData?.low) || '-'}
            </span>
            <span className="flex min-w-[140px] justify-end text-sm font-semibold tablet:min-w-[155px] desktop:min-w-[55px] desktop:text-[11.5px]">
              {priceFormatter(nowData?.high) || '-'}
            </span>
          </div>
        </div>

        {/* CHANGE */}
        <ValuesRow
          title={'Change'}
          valueLeft={nowData?.change}
          valueRight={nowData?.changePercentage}
          altBg={true}
        />

        <ValuesRow
          title={'30daychg'}
          valueLeft={hData?.thirtyDay?.change}
          valueRight={hData?.thirtyDay?.changePercentage}
          altBg={false}
        />

        <ValuesRow
          title={'1yearchg'}
          valueLeft={hData?.oneYear?.change}
          valueRight={hData?.oneYear?.changePercentage}
          altBg={true}
        />
      </div>
      <footer
        className={clsx(styles.footer, '!hidden justify-center desktop:!flex')}
      >
        <Link
          className="mx-auto text-center font-semibold"
          href="/price/precious-metals"
        >
          + More Spot Prices
        </Link>
      </footer>
      <div className="min-h-[46px]">
        <BuySellButton
          containerClassName="pt-[5px] pb-[0.6875rem] flex items-center justify-center"
          buttonClassName="text-[11px] h-[30px] !w-[240px] min-[1240px]:!w-[160px]"
        />
      </div>
    </>
  )
}

export default MetalMonthHomePage
