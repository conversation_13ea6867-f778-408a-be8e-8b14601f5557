.show {
  padding-bottom: 10px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #9c9a94;
  width: 100%;
  overflow-x: auto;

  &::-webkit-scrollbar {
    display: none;
  }
}

.tab {
  display: flex;
  font-size: 16px;
  font-weight: normal !important;
  font-weight: bold;
  cursor: pointer;
  padding: 0 15px;
  height: 45px;
  align-items: center;
  background-color: #f4f4f4;
  border: 1px solid #dddddd;
  width: 100%;
  justify-content: center;
  white-space: nowrap;
}

.activeTab {
  color: #094f8c;
  font-weight: bold;
  border-top: 3px solid #f0b310;
}

.limit {
  cursor: pointer;
  padding: 3px;
}

.activeLimit {
  border-radius: 50%;
  background-color: #f0b310;
}

.articleList {
  margin-bottom: 10px;
}

.icon {
  margin-right: 0.5em;
  width: 22px;
  height: 22px;
}
