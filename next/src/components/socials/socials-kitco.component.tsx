import { clsx } from 'clsx'
import { useState } from 'react'
import { AiOutlinePrinter } from 'react-icons/ai'
import { BiLink } from 'react-icons/bi'
import { FaFacebookF } from 'react-icons/fa'
import { FaXTwitter } from 'react-icons/fa6'
import { IoMailOutline } from 'react-icons/io5'
import {
  EmailShareButton,
  FacebookShareButton,
  TwitterShareButton,
} from 'react-share'
import {
  copyLinkHandler,
  getCurrentURL,
  getMeta,
  handlePrint,
} from '~/src/components/socials/SocialUtils'

export const SocialsKitco = (p: {
  className?: string
  hidePrint?: boolean
  listAuthorStr?: string
  bodyEmail?: string
}) => {
  const [showCopyAlert, setShowCopyAlert] = useState(false)

  const newLine = `\n`

  const bodyEmail = p.bodyEmail
    ? p.bodyEmail
    : getMeta('twitter:title') +
      ' | Kitco News' +
      newLine +
      newLine +
      p.listAuthorStr +
      newLine +
      newLine +
      getMeta('description') +
      newLine +
      newLine

  return (
    <div
      className={clsx([
        'flex items-center gap-2',
        !p?.className ? undefined : p.className,
      ])}
    >
      <FacebookShareButton url={getCurrentURL} className="text-kitco-black">
        <FaFacebookF size={20} />
      </FacebookShareButton>

      <TwitterShareButton
        title={getMeta('twitter:title')}
        url={getCurrentURL}
        via={getMeta('twitter:site').slice(1)}
        className="text-kitco-black"
      >
        <FaXTwitter size={20} />
      </TwitterShareButton>

      <EmailShareButton
        url={getCurrentURL}
        body={bodyEmail}
        subject={getMeta('twitter:title')}
        className="text-kitco-black"
      >
        <IoMailOutline size={24} />
      </EmailShareButton>

      <div className="relative flex">
        <button
          type="button"
          className="text-kitco-black"
          onClick={() => copyLinkHandler(setShowCopyAlert)}
        >
          <BiLink size={24} />
        </button>

        {!showCopyAlert ? null : (
          <span className="width-[75px] absolute left-[-26px] top-6 block whitespace-nowrap">
            Link copied!
          </span>
        )}
      </div>

      {p.hidePrint ? null : (
        <a className="text-kitco-black" onClick={handlePrint}>
          <AiOutlinePrinter size={24} />
        </a>
      )}
    </div>
  )
}
