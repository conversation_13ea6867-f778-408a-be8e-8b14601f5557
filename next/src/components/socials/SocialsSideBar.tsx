import { type ReactNode, useState } from 'react'
import { BiLink } from 'react-icons/bi'
import { FaFacebookF } from 'react-icons/fa'
import { FaXTwitter } from 'react-icons/fa6'
import { IoMailOutline } from 'react-icons/io5'
import {
  EmailShareButton,
  FacebookShareButton,
  TwitterShareButton,
} from 'react-share'
import {
  copyLinkHandler,
  getCurrentURL,
  getMeta,
} from '~/src/components/socials/SocialUtils'

interface SocialsSideBarProps {
  className?: string
  hidePrint?: boolean
  listAuthorStr?: string
  bodyEmail?: string
  children?: ReactNode
}

function SocialsSideBar({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  className,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  hidePrint,
  listAuthorStr,
  bodyEmail,
  children,
}: SocialsSideBarProps) {
  const [showCopyAlert, setShowCopyAlert] = useState(false)

  //const [isFavorite, setIsFavorite] = useState(false);

  const newLine = '\n'

  const fullBodyEmail = bodyEmail
    ? bodyEmail
    : `${getMeta(
        'twitter:title',
      )} | Kitco News${newLine}${newLine}${listAuthorStr}${newLine}${newLine}${getMeta(
        'description',
      )}${newLine}${newLine}`

  /*
  const handleAddToFavorites = () => {
    setIsFavorite(!isFavorite)
  }

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
   */

  return (
    <>
      <FacebookShareButton url={getCurrentURL}>
        <FaFacebookF size={20} className="text-gray-400 hover:text-gray-700" />
      </FacebookShareButton>

      <TwitterShareButton
        title={getMeta('twitter:title')}
        url={getCurrentURL}
        via={getMeta('twitter:site').slice(1)}
      >
        <FaXTwitter size={20} className="text-gray-400 hover:text-gray-700" />
      </TwitterShareButton>

      <EmailShareButton
        url={getCurrentURL}
        body={fullBodyEmail}
        subject={getMeta('twitter:title')}
      >
        <IoMailOutline
          size={24}
          className="text-gray-400 hover:text-gray-700"
        />
      </EmailShareButton>

      <div className="relative inline-block">
        <button type="button" onClick={() => copyLinkHandler(setShowCopyAlert)}>
          <BiLink size={24} className="text-gray-400 hover:text-gray-700" />
          {!showCopyAlert ? null : (
            <span
              className="absolute right-0 top-0 inline-flex h-5 w-5
                        -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-green-600 text-xs font-bold
                        leading-none text-white"
            >
              5
            </span>
          )}
        </button>
      </div>

      {children}

      {/*
      <button
        type="button"
        onClick={handleAddToFavorites}
        className={clsx(
          'block',
          isFavorite
            ? 'text-yellow-400 hover:text-yellow-700'
            : 'text-gray-400 hover:text-gray-700',
        )}
      >
        <FaStar size={24} />
      </button>

      <button
        type="button"
        onClick={scrollToTop}
        className="block text-gray-400 hover:text-gray-700"
      >
        <BiArrowToTop size={24} />
      </button>
      */}
    </>
  )
}

export default SocialsSideBar
