import Image from 'next/image'

interface KitcoIconProps {
  src?: string
  color?: string
  size?: number
}

const KitcoIcon = ({ src, color, size }: KitcoIconProps) => {
  // Default width for the icon
  const defaultSize = 18

  // Size of the icon in pixels
  const cssSize = size ? `${size}px` : `${defaultSize}px`

  return (
    <div className={'relative'} style={{ width: cssSize, height: cssSize }}>
      <Image
        src={src ?? '/logos/kitco-logo-gold.svg'}
        alt="Kitco Logo"
        unoptimized={true}
        fill={true}
        style={color ? { color: color } : {}}
      />
    </div>
  )
}

export default KitcoIcon
