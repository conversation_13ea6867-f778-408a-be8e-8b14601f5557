import { getSortedRowModel, type SortingState } from '@tanstack/table-core'
// import React, { useMemo, useState, type FC } from 'react'
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { useRouter } from 'next/router'
import React, {
  FC,
  Suspense,
  useCallback,
  useMemo,
  useState,
  useTransition,
} from 'react'
import DataTable from '~/src/components/DataTable/DataTable'
import DataTableFilter from '~/src/components/DataTable/Filter/DataTableFilter'
import DataTableColumns from '~/src/components/GoldIndex/DataTable/DataTableColumns'
import commodityCategories from '~/src/data/GoldIndex/CommodityCategories'
import type CommodityData from '~/src/types/DataTable/CommodityData'

interface GoldIndexDataTableProps {
  data: CommodityData[]
  isLoading?: boolean
}

const GoldIndexDataTable: FC<GoldIndexDataTableProps> = ({
  // data,
  data: initialData,
  isLoading,
}: GoldIndexDataTableProps) => {
  const router = useRouter()
  const [isPending, startTransition] = useTransition()

  // Get initial filter from URL query parameter
  const getInitialFilter = useCallback(() => {
    const { filter, commodity } = router.query

    // First check for direct filter parameter
    if (filter && typeof filter === 'string') {
      // Convert filter parameter to match our category keys
      const filterMap: Record<string, string> = {
        cryptocurrencies: 'CRYPTOCURRENCIES',
        'precious-metals': 'PRECIOUS METALS',
        'base-metals': 'BASE METALS',
        energy: 'ENERGY',
      }
      return filterMap[filter.toLowerCase()] || filter.toUpperCase()
    }

    // Then check for commodity parameter and determine its category
    if (commodity && typeof commodity === 'string') {
      // Find which category this commodity belongs to
      for (const [categoryName, commodities] of Object.entries(
        commodityCategories,
      )) {
        if (commodities.includes(commodity)) {
          return categoryName
        }
      }
    }

    return 'ALL'
  }, [router.query])

  const [activeFilter, setActiveFilter] = useState<string>('ALL')
  const [items, setItems] = useState<CommodityData[]>(initialData || [])

  // Update filter when URL changes
  React.useEffect(() => {
    if (router.isReady) {
      const newFilter = getInitialFilter()
      setActiveFilter(newFilter)
    }
  }, [
    router.query.filter,
    router.query.commodity,
    router.isReady,
    getInitialFilter,
  ])

  React.useEffect(() => {
    if (!initialData?.length) return

    setItems((currentItems) => {
      if (currentItems.length === 0) {
        return [...initialData]
      }

      const updatedItems = currentItems.map((currentItem) => {
        const updatedItem = initialData.find(
          (item) => item.commodity === currentItem.commodity,
        )
        return updatedItem ? { ...currentItem, ...updatedItem } : currentItem
      })

      return updatedItems
    })
  }, [initialData])

  const handleFilterChange = (filter: string) => {
    startTransition(() => {
      setActiveFilter(filter)
    })
  }

  const [sorting, setSorting] = React.useState<SortingState>([])

  const extraConfig = {
    initialState: {
      columnPinning: {
        left: ['commodity'],
      },
      pagination: {
        pageIndex: 0,
        pageSize: 25,
      },
    },
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  }

  const filteredData = useMemo(() => {
    let result = [...items]

    if (activeFilter !== 'ALL') {
      const categoryCommodities = commodityCategories[activeFilter]
      if (categoryCommodities) {
        result = result.filter((item) =>
          categoryCommodities.includes(item.commodity),
        )
      }
    }

    return result
  }, [items, activeFilter])

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleDragEnd = useCallback((event: any) => {
    const { active, over } = event

    if (active.id !== over?.id) {
      setItems((currentItems) => {
        const newItems = [...currentItems]
        const oldIndex = newItems.findIndex(
          (item) => item.commodity === active.id,
        )
        const newIndex = newItems.findIndex(
          (item) => item.commodity === over?.id,
        )

        if (oldIndex === -1 || newIndex === -1) return currentItems

        const [movedItem] = newItems.splice(oldIndex, 1)
        newItems.splice(newIndex, 0, movedItem)
        return newItems
      })
    }
  }, [])

  return (
    <>
      <Suspense fallback={<div>Loading filters...</div>}>
        <DataTableFilter
          onFilterChange={handleFilterChange}
          activeFilter={activeFilter}
          filters={Object.keys(commodityCategories)}
          disabled={isPending}
        />
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis]}
        >
          <SortableContext
            items={items.map((item) => item.commodity)}
            strategy={verticalListSortingStrategy}
          >
            <DataTable<CommodityData>
              columns={DataTableColumns}
              data={filteredData}
              extraConfig={extraConfig}
              isLoading={isLoading || isPending}
              onReorder={setItems}
              paginationEnabled={false}
            />
          </SortableContext>
        </DndContext>
      </Suspense>
    </>
  )
}

export default GoldIndexDataTable
