import Image from 'next/image'
import { useEffect, useState } from 'react'
import { FaSpinner } from 'react-icons/fa'
import ImageBarchartFilter from '~/src/components/ImageBarchart/ImageBarchartFilter'
import { current } from '~/src/utils/timestamps'

const GoldIndexBarchartImage = () => {
  const barchartImageFilters = [
    {
      label: 'Real Time',
      url: 'https://www.weblinks247.com/indexes/2a-kgdx-us-d-large.gif',
    },
    {
      label: '30D',
      url: 'https://www.weblinks247.com/indexes/2a-kgdx-us-30d-Large.gif',
    },
    {
      label: '60D',
      url: 'https://www.weblinks247.com/indexes/2a-kgdx-us-60d-Large.gif',
    },
    {
      label: '6M',
      url: 'https://www.weblinks247.com/indexes/2a-kgdx-us-6m-Large.gif',
    },
    {
      label: '1Y',
      url: 'https://www.weblinks247.com/indexes/2a-kgdx-us-1y-Large.gif',
    },
    {
      label: '5Y',
      url: 'https://www.weblinks247.com/indexes/2a-kgdx-us-5y-Large.gif',
    },
    {
      label: '10Y',
      url: 'https://www.weblinks247.com/indexes/2a-kgdx-us-10y-Large.gif',
    },
  ]

  // const [activeBarchartImage, setActiveBarchartImage] = useState<string>(
  //   // Set the default image to the first image in the list
  //   barchartImageFilters[0].url,
  // )

  const [activeBarchartImage, setActiveBarchartImage] = useState<string>(
    barchartImageFilters[0].url,
  )
  const [loading, setLoading] = useState<boolean>(true)
  const [imageSrc, setImageSrc] = useState<string>(activeBarchartImage)

  useEffect(() => {
    setLoading(true)
    setImageSrc(`${activeBarchartImage}?random=${current()}`)
  }, [activeBarchartImage])

  /**
   * Handle the image finish load state
   */
  const handleImageLoaded = () => {
    setLoading(false)
  }

  /**
   * Handle the image change
   * Set the active image URL and set the loading state to true
   *
   * @param {string} url - The image URL
   */
  const handleImageChange = (url: string) => {
    setLoading(true)
    setActiveBarchartImage(url)
  }

  return (
    <>
      <div className="mb-10 mt-5 flex items-center justify-center">
        {loading && (
          <div className="absolute flex items-center justify-center">
            <FaSpinner
              className="animate-spin text-4xl text-gray-600"
              size={52}
            />
          </div>
        )}
        <Image
          // src={`${activeBarchartImage}?random=${current()}`}
          // alt="24hr Spot Gold in Kitco Gold Index vs US Dollars"
          src={imageSrc}
          alt="24hr Spot Gold in Kitco Global Index vs US Dollars"
          width={630}
          height={400}
          unoptimized={true}
          onLoad={handleImageLoaded}
          onError={handleImageLoaded}
        />
      </div>

      <ImageBarchartFilter
        onImageChange={handleImageChange}
        activeImage={activeBarchartImage}
        filters={barchartImageFilters}
      />
    </>
  )
}

export default GoldIndexBarchartImage
