import type { SortingState } from '@tanstack/table-core'
import type { FC } from 'react'
import DataTableSortIcon from '~/src/components/DataTable/SortModal/DataTableSortIcon'
import type SortOption from '~/src/types/DataTable/SortOption'

interface MiningEquitiesHeaderProps {
  sortOptions: SortOption[]
  onSortApply: (value: SortingState) => void
}

const MiningEquitiesHeader: FC<MiningEquitiesHeaderProps> = ({
  onSortApply,
  sortOptions,
}: MiningEquitiesHeaderProps) => {
  return (
    <div className="flex w-full items-start justify-between gap-2">
      <div className="flex flex-col gap-2">
        <h1 className="mt-1.5 font-['Lato'] text-3xl font-bold leading-none text-neutral-900">
          Mining Equities
        </h1>
      </div>
      <div className="flex sm:hidden">
        <DataTableSortIcon
          sortOptions={sortOptions}
          onSortApply={onSortApply}
          title="Sort"
        />
      </div>
    </div>
  )
}

export default MiningEquitiesHeader
