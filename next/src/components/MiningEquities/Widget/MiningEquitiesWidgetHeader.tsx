import type { FC } from 'react'

interface MiningEquitiesWidgetHeaderProps {
  totalCompanies: number
}

const MiningEquitiesWidgetHeader: FC<MiningEquitiesWidgetHeaderProps> = ({
  totalCompanies,
}: MiningEquitiesWidgetHeaderProps) => {
  return (
    <div className="flex w-full items-start justify-between gap-2">
      <div className="flex flex-col gap-2">
        <h3 className="mt-1.5 font-['Lato'] text-2xl font-bold leading-none text-neutral-900">
          Top 5 Performing Mining Equities
        </h3>
        <p>Among the {totalCompanies} listed on our North-American coverage</p>
      </div>
    </div>
  )
}

export default MiningEquitiesWidgetHeader
