import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
  Transition,
} from '@headlessui/react'
import clsx from 'clsx'
import { type FC, Fragment, useEffect } from 'react'
import { IoChevronDownSharp } from 'react-icons/io5'
import { useDispatch, useSelector } from 'react-redux'
import {
  loadCurrencyFromStorage,
  setCurrency,
  setTemporaryCurrency,
} from '~/src/features/store/currencySlice'
import type { AppDispatch, RootState } from '~/src/features/store/store'
import { useListboxWidthSync } from '~/src/hooks/useListboxWidthSync'
import styles from '~/src/styles/select-box.module.scss'
import { CURRENCY, type Currency } from '~/src/utils/currencies'

/**
 * Props for the CurrencySelect component.
 *
 * @interface CurrencySelectProps
 * @property {boolean} hideNames - Whether to hide the names of the currencies
 * @property {boolean} hideFlags - Whether to hide the flags of the currencies
 * @property {string} classNamesListbox - The class names for the listbox
 * @property {string} classNamesItemListbox - The class names for the item listbox
 * @property {string} classNamesIconListbox - The class names for the icon listbox
 * @property {Currency} forceCurrency - The currency to be forced
 * @constructor
 */
export interface CurrencySelectProps {
  hideNames?: boolean
  hideFlags?: boolean
  classNamesListbox?: string
  classNamesItemListbox?: string
  classNamesIconListbox?: string
  forceCurrency?: Currency
}

/**
 * CurrencySelect component
 * This component is used to display a dropdown menu of currencies
 *
 * @param {boolean} hideNames - Whether to hide the names of the currencies
 * @param {boolean} hideFlags - Whether to hide the flags of the currencies
 * @param {string} classNamesListbox - The class names for the listbox
 * @param {string} classNamesItemListbox - The class names for the item listbox
 * @param {string} classNamesIconListbox - The class names for the icon listbox
 * @param {Currency} forceCurrency - The currency to be forced
 * @returns {JSX.Element} - The rendered component
 * @constructor
 */
const CurrencySelect: FC<CurrencySelectProps> = ({
  hideNames = true,
  hideFlags,
  classNamesListbox,
  classNamesItemListbox,
  classNamesIconListbox,
  forceCurrency,
}: CurrencySelectProps) => {
  // Use the dispatch to update the currency state
  const dispatch = useDispatch<AppDispatch>()

  // Use the useListboxWidthSync hook to set the width of the listbox
  const { buttonRef, buttonWidth } = useListboxWidthSync<HTMLDivElement>()

  // Get the selected currency value from the state or the forceCurrency prop
  const value = useSelector((state: RootState) =>
    forceCurrency
      ? state.currency.temporaryCurrency
      : state.currency.selectedCurrency,
  )

  // If the forceCurrency prop is set, set the temporary currency
  useEffect(() => {
    if (forceCurrency) {
      dispatch(setTemporaryCurrency(forceCurrency))
    } else {
      dispatch(loadCurrencyFromStorage())
    }
  }, [dispatch, forceCurrency])

  /**
   * Handle the currency change event
   *
   * @param {string} selectedCurrency - The selected currency value
   */
  const handleCurrencyChange = (selectedCurrency: string) => {
    // Get the selected currency value
    const selectedValue = CURRENCY[selectedCurrency as keyof typeof CURRENCY]

    // If the forceCurrency is set, set the temporary currency
    if (forceCurrency) {
      dispatch(setTemporaryCurrency(selectedValue))
    } else {
      dispatch(setCurrency(selectedValue))
    }
  }

  if (!value) return null

  return (
    <Listbox value={value.symbol} onChange={handleCurrencyChange}>
      <div
        className={clsx(
          'relative inline-block w-full',
          'rounded-sm border border-ktc-gray/20',
          classNamesListbox,
        )}
        ref={buttonRef}
      >
        <ListboxButton
          className={clsx(
            'h-7 w-full px-2 flex items-center justify-between gap-1',
            classNamesItemListbox,
          )}
        >
          <span>
            {!hideFlags && (
              <img
                src={value?.image}
                alt={value?.name}
                className={styles.flag}
              />
            )}
            <span className="block truncate">{value.symbol}</span>
          </span>
          <span className={clsx('pl-1', classNamesIconListbox)}>
            <IoChevronDownSharp />
          </span>
        </ListboxButton>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <ListboxOptions
            style={{ width: buttonWidth }}
            anchor="bottom end"
            className={clsx(
              'absolute z-[2000] mt-1 h-auto py-1 w-auto',
              'min-w-max rounded-md bg-white text-base shadow-lg',
              'ring-1 ring-black/20 focus:outline-none sm:text-sm',
            )}
          >
            {Object.values(CURRENCY).map((currencyValue) => (
              <ListboxOption
                key={currencyValue.symbol}
                value={currencyValue.symbol}
                className={clsx(
                  'relative block cursor-pointer select-none px-4 py-2',
                  'data-[focus]:bg-blue-200/30 data-[focus]:text-blue-800 text-gray-900',
                )}
              >
                <div className="flex flex-nowrap items-center gap-2">
                  <img
                    src={currencyValue?.image}
                    alt={currencyValue?.name}
                    className={styles.flag}
                  />
                  <span className={clsx('block basis-12 font-semibold')}>
                    {currencyValue.symbol}
                  </span>
                  {!hideNames && (
                    <span
                      className={clsx(
                        'block basis-5/6 whitespace-nowrap opacity-70',
                      )}
                    >
                      {currencyValue.name}
                    </span>
                  )}
                </div>
              </ListboxOption>
            ))}
          </ListboxOptions>
        </Transition>
      </div>
    </Listbox>
  )
}

export default CurrencySelect
