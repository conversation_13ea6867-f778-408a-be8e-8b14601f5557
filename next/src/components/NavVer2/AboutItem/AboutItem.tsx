import * as Navigation from '../Composables'
import AboutMenu from './AboutMenu'

const AboutItem = ({ value, onNodeUpdate }) => {
  return (
    <Navigation.Item value={value}>
      <Navigation.Trigger value={value} onNodeUpdate={onNodeUpdate}>
        <div className="whitespace-nowrap text-sm font-bold leading-5 text-white">
          <span className="group-hover:underline">{value}</span>
        </div>
      </Navigation.Trigger>
      <Navigation.Content>
        <AboutMenu />
      </Navigation.Content>
    </Navigation.Item>
  )
}

export default AboutItem
