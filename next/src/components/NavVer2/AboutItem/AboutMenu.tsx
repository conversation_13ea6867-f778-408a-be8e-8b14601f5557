import type { SectionItems } from '~/src/types'
import * as Navigation from '../Composables'
import SectionList from '../SectionList/SectionList'

export const abouts: SectionItems[] = [
  {
    name: 'About Kitco',
    href: 'https://corp.kitco.com/',
    isExternalLink: true,
  },
  {
    name: 'Precious Metal Division',
    href: 'https://corp.kitco.com/pmd.html',
    isExternalLink: true,
  },
  {
    name: 'Refining',
    href: 'https://corp.kitco.com/refining.html',
    isExternalLink: true,
  },
  {
    name: 'Media Center',
    href: 'https://corp.kitco.com/media.html',
    isExternalLink: true,
  },
  {
    name: 'Careers',
    href: 'https://corp.kitco.com/career.html',
    isExternalLink: true,
  },
  {
    name: 'Contact Us',
    href: 'https://corp.kitco.com/contact-us.html',
    isExternalLink: true,
  },
  {
    name: 'Feedback',
    href: 'https://corp.kitco.com/contact-us.html#contact',
    isExternalLink: true,
  },
]

function AboutMenu() {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList
          title="About Kitco"
          titleUrl="https://corp.kitco.com/"
          isExternalLink
        />
        <SectionList
          title="Precious Metal Division"
          titleUrl="https://corp.kitco.com/pmd.html"
          isExternalLink
        />
        <SectionList
          title="Refining"
          titleUrl="https://corp.kitco.com/refining.html"
          isExternalLink
        />
        <SectionList
          title="Media Center"
          titleUrl="https://corp.kitco.com/media.html"
          isExternalLink
        />
        <SectionList
          title="Careers"
          titleUrl="https://corp.kitco.com/career.html"
          isExternalLink
        />
        <SectionList
          title="Contact Us"
          titleUrl="https://corp.kitco.com/contact-us.html"
          isExternalLink
        />
        <SectionList
          title="Feedback"
          titleUrl="https://corp.kitco.com/contact-us.html#contact"
          isExternalLink
        />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default AboutMenu
