import Link from 'next/link'
import * as Navigation from './../Composables'
import QuotesMenu from './QuotesMenu'

const QuotesItem = ({ value, onNodeUpdate }) => {
  return (
    <Navigation.Item value={value}>
      <Navigation.Trigger value={value} onNodeUpdate={onNodeUpdate}>
        <Link
          href="/price/precious-metals"
          className="whitespace-nowrap text-sm font-bold leading-5 text-white"
        >
          <span className="group-hover:underline">{value}</span>
          <p className="text-left text-xs leading-[15px] desktop:text-center">
            All Metal Quotes
          </p>
        </Link>
      </Navigation.Trigger>
      <Navigation.Content>
        <QuotesMenu />
      </Navigation.Content>
    </Navigation.Item>
  )
}

export default QuotesItem
