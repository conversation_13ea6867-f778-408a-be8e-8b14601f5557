import Link from 'next/link'
import type { FC } from 'react'
import type { Icons, SectionItems } from '~/src/types'
import Icon from '../../Icon/Icon'
import KitcoIcon from '../../Icon/KitcoIcon'
import StorageIcon from '../../Icon/StorageIcon'
import styles from './SectionList.module.scss'

/**
 * Props for the SectionList component.
 *
 * @param {Icons | 'kitco' | 'storage'} icon - The icon to display.
 * @param {string} iconColor - The color of the icon.
 * @param {string} iconSrc - The source of the icon.
 * @param {boolean} isExternalLink - Whether the link is external.
 * @param {SectionItems[]} items - The items to display in the list.
 * @param {string} title - The title of the section.
 * @param {string} titleUrl - The URL of the section title.
 */
interface SectionListProps {
  icon?: Icons | 'kitco' | 'storage'
  iconColor?: string
  iconSrc?: string
  isExternalLink?: boolean
  items?: SectionItems[]
  title: string
  titleUrl?: string
}

/**
 * SectionList component.
 *
 * @param {Icons | 'kitco' | 'storage'} icon - The icon to display.
 * @param {string} iconColor - The color of the icon.
 * @param {string} iconSrc - The source of the icon.
 * @param {boolean} isExternalLink - Whether the link is external.
 * @param {SectionItems[]} items - The items to display in the list.
 * @param {string} title - The title of the section.
 * @param {string} titleUrl - The URL of the section title.
 * @constructor
 */
const SectionList: FC<SectionListProps> = ({
  icon,
  iconColor,
  iconSrc,
  items,
  title,
  titleUrl,
  isExternalLink = false,
}: SectionListProps) => {
  // Default color for the icon
  const defaultColor = '#f9c432'

  return (
    <div className={styles.iconTitleContainer}>
      {!icon && <div />}

      {icon === 'kitco' && (
        <div className={styles.iconContainer}>
          <KitcoIcon color={!iconColor ? null : iconColor} src={iconSrc} />
        </div>
      )}

      {icon === 'storage' && (
        <div className={styles.iconContainer}>
          <StorageIcon color={!iconColor ? defaultColor : iconColor} />
        </div>
      )}

      {icon !== 'kitco' && icon !== 'storage' && icon !== undefined && (
        <div className={styles.iconContainer}>
          <Icon
            icon={icon}
            size="18px"
            color={!iconColor ? defaultColor : iconColor}
          />
        </div>
      )}

      <ul>
        <li>
          {!titleUrl ? (
            <h4 className="font-mulish mb-1 pt-1 text-sm font-bold leading-5 text-white opacity-90">
              {title}
            </h4>
          ) : (
            <Link href={titleUrl} target={isExternalLink ? '_blank' : '_self'}>
              <h4 className="font-mulish mb-1 pt-1 text-sm font-bold leading-5 text-white hover:underline">
                {title}
              </h4>
            </Link>
          )}
        </li>

        {!items && <div />}

        {items?.map((link) => (
          <li
            key={link.name}
            className="font-mulish text-sm leading-5 text-[#ededed] hover:text-white"
          >
            {!link.as ? (
              <a
                className={styles.item}
                href={link.href}
                target={link?.isExternalLink ? '_blank' : '_self'}
              >
                {link.name}
              </a>
            ) : (
              <Link
                as={link.as}
                className={styles.item}
                href={link.href}
                target={link?.isExternalLink ? '_blank' : '_self'}
              >
                {link.name}
              </Link>
            )}
          </li>
        ))}
      </ul>
    </div>
  )
}

export default SectionList
