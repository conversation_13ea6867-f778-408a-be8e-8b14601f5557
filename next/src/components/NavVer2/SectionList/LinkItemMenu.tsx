import Link from 'next/link'
import type { FC } from 'react'
import Icon from '~/src/components/Icon/Icon'
import type { Icons } from '~/src/types'
import styles from './SectionList.module.scss'

/**
 * Props for the LinkItemMenu component.
 *
 * @param {string} title - The title of the link.
 * @param {Icons} icon - The icon to display.
 * @param {string} iconColor - The color of the icon.
 * @param {string} link - The URL of the link.
 * @param {boolean} isExternalLink - Whether the link is external.
 */
interface LinkItemMenuProps {
  icon?: Icons
  iconColor?: string
  isExternalLink?: boolean
  link?: string
  title: string
}

/**
 * LinkItemMenu component.
 *
 * @param {string} title - The title of the link.
 * @param {Icons} icon - The icon to display.
 * @param {string} iconColor - The color of the icon.
 * @param {string} link - The URL of the link.
 * @param {boolean} isExternalLink - Whether the link is external.
 * @constructor
 */
export const LinkItemMenu: FC<LinkItemMenuProps> = ({
  icon,
  iconColor,
  isExternalLink,
  link,
  title,
}: LinkItemMenuProps) => {
  const defaultColor = '#f9c432'
  return (
    <div className={styles.iconTitleContainer}>
      <Link
        href={link}
        target={isExternalLink ? '_blank' : '_self'}
        className="flex items-center"
      >
        <div className={styles.iconContainer}>
          <Icon
            icon={icon}
            size="18px"
            color={!iconColor ? defaultColor : iconColor}
          />
        </div>
        <h4 className="font-mulish mb-1 break-words pt-1 text-sm font-bold leading-5 text-white hover:underline">
          {title}
        </h4>
      </Link>
    </div>
  )
}
