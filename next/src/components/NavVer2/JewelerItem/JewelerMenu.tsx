import type { SectionItems } from '~/src/types'
import * as Navigation from '../Composables'
import SectionList from '../SectionList/SectionListTwo'

export const jeweler: SectionItems[] = [
  {
    name: 'ScrapIt!',
    href: '/jeweler-resources',
    isExternalLink: false,
  },
  {
    name: 'Melting Points',
    href: '/jeweler-table/melting-point',
    isExternalLink: false,
  },
  {
    name: 'Weight Conversion',
    href: '/jeweler-table/weight-conversion',
    isExternalLink: false,
  },
  {
    name: 'Gauge Conversion',
    href: '/jeweler-table/gauge-conversion',
    isExternalLink: false,
  },
  {
    name: 'Weight Comparison',
    href: '/jeweler-table/weight-comparison',
    isExternalLink: false,
  },
  {
    name: 'Refining Services',
    href: 'https://online.kitco.com/refining',
    isExternalLink: true,
  },
]

function JewelerMenu() {
  return (
    <Navigation.SubMenuTwoGrid>
      <Navigation.SubMenuTwoColumn>
        <SectionList title="ScrapIt!" titleUrl="/jeweler-resources" />
        <SectionList
          title="Melting Points"
          titleUrl="/jeweler-table/melting-point"
        />
        <SectionList
          title="Weight Conversion"
          titleUrl="/jeweler-table/weight-conversion"
        />
        <SectionList
          title="Gauge Conversion"
          titleUrl="/jeweler-table/gauge-conversion"
        />
        <SectionList
          title="Weight Comparison"
          titleUrl="/jeweler-table/weight-comparison"
        />
        <SectionList
          title="Refining Services"
          titleUrl="https://online.kitco.com/refining"
          isExternalLink
        />
      </Navigation.SubMenuTwoColumn>
    </Navigation.SubMenuTwoGrid>
  )
}

export default JewelerMenu
