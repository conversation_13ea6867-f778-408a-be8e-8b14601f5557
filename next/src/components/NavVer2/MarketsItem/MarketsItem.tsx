import Link from 'next/link'
import * as Navigation from './../Composables'
import MarketsMenu from './MarketsMenu'

const MarketsItem = ({ value, onNodeUpdate }) => {
  return (
    <Navigation.Item value={value}>
      <Navigation.Trigger value={value} onNodeUpdate={onNodeUpdate}>
        <Link
          href="/markets"
          className="whitespace-nowrap text-sm font-bold leading-5 text-white"
        >
          <span className="group-hover:underline">{value}</span>
        </Link>
      </Navigation.Trigger>
      <Navigation.Content>
        <MarketsMenu />
      </Navigation.Content>
    </Navigation.Item>
  )
}

export default MarketsItem
