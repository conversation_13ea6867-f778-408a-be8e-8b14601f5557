import Link from 'next/link'
import * as Navigation from './../Composables'
import MiningMenu from './MiningMenu'

const MiningItem = ({ value, onNodeUpdate }) => {
  return (
    <Navigation.Item value={value}>
      <Navigation.Trigger value={value} onNodeUpdate={onNodeUpdate}>
        <Link
          className="whitespace-nowrap text-sm font-bold leading-5 text-white"
          href="/news/category/mining"
        >
          <span className="group-hover:underline">{value}</span>
        </Link>
      </Navigation.Trigger>
      <Navigation.Content>
        <MiningMenu />
      </Navigation.Content>
    </Navigation.Item>
  )
}

export default MiningItem
