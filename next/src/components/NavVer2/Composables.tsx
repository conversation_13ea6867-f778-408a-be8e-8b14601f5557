import * as Rad from '@radix-ui/react-navigation-menu'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { AiOutlinePlus } from 'react-icons/ai'
import { IoMdClose } from 'react-icons/io'
import useScreenSize from '~/src/utils/useScreenSize'

export const preventHover = (event: any) => {
  const e = event as Event
  if (window.innerWidth < 1271) e.preventDefault()
}

function Item(props: { children: React.ReactNode; value: string }) {
  return (
    <Rad.Item
      className={clsx(
        'block h-auto w-full px-0 py-1',
        'desktop:px-0 desktop:py-0',
      )}
      value={props.value}
    >
      {props.children}
    </Rad.Item>
  )
}

function Trigger(props: {
  children: React.ReactNode
  value: string
  onNodeUpdate: any
}) {
  const [elementTrigger, setElementTrigger] = useState(null)
  const [expanded, setExpanded] = useState(false)
  const MENU_ITEMS_NOLINK = ['About']
  const { isDesktopForNavBar } = useScreenSize()

  useEffect(() => {
    if (elementTrigger) {
      const ariaExpanded = elementTrigger.getAttribute('aria-expanded')
      setExpanded(ariaExpanded === 'true')
    }
  })

  // Exception for external links
  const onClickTrigger = (e) => {
    if (props.value !== 'BUY/SELL GOLD & SILVER' && isDesktopForNavBar) {
      return e.preventDefault()
    }
  }

  return (
    <Rad.Trigger
      onClick={onClickTrigger}
      onPointerMove={preventHover}
      onPointerLeave={preventHover}
      ref={(node) => {
        setElementTrigger(node)
        props.onNodeUpdate(node, props.value)
      }}
      className={clsx(
        MENU_ITEMS_NOLINK.includes(props.value) ? 'cursor-default' : '',
        'block w-full text-left',
        'flex place-content-center items-center justify-between desktop:justify-center desktop:px-4',
        'group desktop:h-full ',
      )}
    >
      {props.children}
      <span className={clsx('mr-4 opacity-50 desktop:hidden')}>
        {!expanded ? (
          <AiOutlinePlus color="white" size="16px" />
        ) : (
          <IoMdClose color="white" size="16px" />
        )}
      </span>
    </Rad.Trigger>
  )
}

function Content(props: { children: React.ReactNode }) {
  const r = useRouter()
  return (
    <div className={clsx('relative block h-auto')}>
      <Rad.Content
        onPointerLeave={preventHover}
        className={clsx('relative top-0', 'flex justify-center')}
      >
        <div
          className={clsx(
            'mr-auto px-2 py-0 desktop:mr-0 desktop:px-4 desktop:py-4',
            r.pathname.includes('/news/video')
              ? 'bg-[#0F181D]'
              : 'bg-ktc-black',
          )}
        >
          {props.children}
        </div>
      </Rad.Content>
    </div>
  )
}

const SubMenuGrid = (props: {
  children: React.ReactNode
  isDifference?: boolean
}) => (
  <div
    className={clsx(
      'grid grid-cols-3 divide-x divide-white/10 desktop:block desktop:divide-x-0',
      props.isDifference ? 'desktop:!grid' : '',
    )}
  >
    {props.children}
  </div>
)

const SubMenuTwoGrid = (props: {
  children: React.ReactNode
  isDifference?: boolean
}) => (
  <div
    className={clsx(
      'grid grid-cols-1 divide-x divide-white/10 desktop:block desktop:divide-x-0',
      props.isDifference ? 'desktop:!grid' : '',
    )}
  >
    {props.children}
  </div>
)
const SubMenuColumn = (props: { children: React.ReactNode }) => (
  <div className="w-1/3 min-w-[240px] pl-4 first:pl-0 desktop:w-full desktop:min-w-fit desktop:pl-0">
    {props.children}
  </div>
)
const SubMenuTwoColumn = (props: { children: React.ReactNode }) => (
  <div className="w-2/3 min-w-[240px] pl-4 first:pl-0 desktop:w-full desktop:min-w-fit desktop:pl-0">
    {props.children}
  </div>
)

export {
  Content,
  Item,
  SubMenuColumn,
  SubMenuGrid,
  SubMenuTwoColumn,
  SubMenuTwoGrid,
  Trigger,
}
