// import VideoNewsSidebar from "~/src/components/VideoNewsSidebar/VideoNewsSidebar";
import { Suspense } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import BlockHeader from '~/src/components/BlockHeader/BlockHeader'
import * as VideoTeaser from '~/src/components/VideoTeaser/VideoTeaser'
import { vcms } from '~/src/lib/vcms-factory.lib'
import { Query } from '../Query/Query'
import VideoNewsSidebarLoading from '../VideoNewsSidebar/VideoNewsSidebarLoading'
import styles from './video-news-outter-shell.module.scss'

const fetcher = vcms.feed({
  variables: { latest: true },
  options: {},
})

export const VideoNewsOutter = () => {
  return (
    <div>
      <BlockHeader title="Video News" href="/news/video" />
      <div className={styles.wrapperBorder}>
        <ErrorBoundary fallback={<p>error loading</p>}>
          <Suspense fallback={<VideoNewsSidebarLoading />}>
            <Query fetcher={fetcher}>
              {(queryResult) => {
                const v = queryResult?.data?.VideoConsumerFeed?.latest
                return (
                  <ul className="grid grid-cols-1 gap-3 md:grid-cols-3">
                    {v?.slice(0, 6)?.map((x) => (
                      <li key={x.id}>
                        <VideoTeaser.CtxProvider
                          isFetching={queryResult.isFetching}
                          node={x}
                        >
                          <div className="flex flex-col gap-1">
                            <VideoTeaser.A>
                              <VideoTeaser.Img height={400} width={400} />
                              <VideoTeaser.PlayButton />
                            </VideoTeaser.A>
                            <div className="block">
                              <VideoTeaser.A>
                                <VideoTeaser.Title className="text-kitco-black" />
                                <VideoTeaser.Timestamp className="text-black" />
                              </VideoTeaser.A>
                            </div>
                          </div>
                        </VideoTeaser.CtxProvider>
                      </li>
                    ))}
                  </ul>
                )
              }}
            </Query>
          </Suspense>
        </ErrorBoundary>
      </div>
    </div>
  )
}
