import React, { Suspense, useState } from 'react'
import ListItemOneLine from '~/src/components-news/ArticleListItems/ListItemOneLine/ListItemOneLine'
import Tabs from '~/src/components/LatestNewsBlock/Tabs/Tabs'
import ArticleMoreButton from '~/src/components/article-more-button/article-more-button.component'
import type { CommentaryTeaserFragmentFragment } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import { COMMENTARIES_TABS, OpinionType } from '~/src/types/index'
import { ErrBoundary } from '../ErrBoundary/ErrBoundary'
import { Query } from '../Query/Query'

export const CommentariesFeed = () => {
  const [currentTab, setCurrentTab] = useState(COMMENTARIES_TABS.COMMENTARIES)
  return (
    <div>
      <header className="flex items-end justify-between">
        <Tabs
          currentTab={currentTab}
          setCurrentTab={setCurrentTab}
          listTab={Object.values(COMMENTARIES_TABS)}
        />
      </header>
      {currentTab === COMMENTARIES_TABS.COMMENTARIES && (
        <NewsHomepageCommentaries />
      )}
      {currentTab === COMMENTARIES_TABS.KITCO && (
        <NewsHomepageCommentaries opinionType={OpinionType.KITCO} />
      )}
      {currentTab === COMMENTARIES_TABS.EXCLUSIVE && (
        <NewsHomepageCommentaries opinionType={OpinionType.EXCLUSIVE} />
      )}
      {currentTab === COMMENTARIES_TABS.CORNER && (
        <NewsHomepageCommentaries opinionType={OpinionType.CORNER} />
      )}

      <footer>
        <ArticleMoreButton title="More Commentaries" href="/opinion" />
      </footer>
    </div>
  )
}

export const NewsHomepageCommentaries = ({
  opinionType,
}: {
  opinionType?: OpinionType
}) => {
  const fetcher = opinionType
    ? news.commentaryListFilterCommentaries({
        variables: {
          limit: 15,
          offset: 0,
          opinionType: opinionType as any, // TODO: ross added any instead of handling undefined
        },
        options: {
          enabled: true,
        },
      })
    : news.nodeListQueueCommentaries({
        variables: {
          limit: 15,
          offset: 0,
        },
        options: {
          enabled: true,
        },
      })

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data: dataCommentaries, refetch, isFetching }) => {
            React.useEffect(() => {
              refetch()
            }, [opinionType, refetch])

            return (
              <div>
                <div className="mb-3">
                  {isFetching ? (
                    <p>Loading...</p>
                  ) : dataCommentaries?.commentaries?.items?.length > 0 ? (
                    dataCommentaries?.commentaries?.items?.map(
                      (x: CommentaryTeaserFragmentFragment, idx: number) => (
                        <ListItemOneLine
                          isOdd={Boolean(idx % 2)}
                          isBold={dataCommentaries?.ids.includes(x.id)}
                          tag={{}}
                          title={x.title}
                          teaserHeadline={x?.teaserHeadline}
                          source={!x.author?.name ? '' : x.author.name}
                          date={x.createdAt}
                          url={x.urlAlias}
                          key={x.id}
                          opinionType={x?.opinionType}
                        />
                      ),
                    )
                  ) : (
                    <p className="text-center">No data</p>
                  )}
                </div>
              </div>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}
