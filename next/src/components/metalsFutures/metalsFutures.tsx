import clsx from 'clsx'
import Link from 'next/link'
import type React from 'react'
import { BiSolidRightArrow } from 'react-icons/bi'
import styles from './metalsFutures.module.scss'

const MetalsFutures: React.FC = () => {
  return (
    <Link
      href="/markets/metals"
      className={clsx(
        'relative cursor-pointer border border-ktc-borders bg-gray-800 p-1 text-center hover:bg-[#003871]',
        'flex items-center justify-center gap-1',
        styles.container,
      )}
    >
      <h2 className="text-base font-semibold capitalize text-white">
        Metals Futures
      </h2>
      <BiSolidRightArrow className="ml-2 text-white" />
    </Link>
  )
}

MetalsFutures.propTypes = {}

export default MetalsFutures
