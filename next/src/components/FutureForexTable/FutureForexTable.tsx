import Link from 'next/link'
import Table from '~/src/components/Table/Table'
import type { BarchartQuote } from '~/src/generated'
import colorize from '~/src/utils/colorize'
import cs from '~/src/utils/cs'
import dates from '~/src/utils/dates'
import SkeletonTable from '../SkeletonTable/SkeletonTable'
import styles from './FutureForexTable.module.scss'

interface Props {
  title: string
  data: Array<BarchartQuote | any>
  showMore?: boolean
}

const FutureForexTable = ({ data, title, showMore = true }: Props) => {
  const { format } = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  })

  const { format: formatForex } = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })

  const symbolSanitizer = (symbol: string): string => {
    const splitter = symbol.split('')
    if (splitter.includes('$') || splitter.includes('^')) {
      return symbol.substring(1)
    }
    return symbol
  }

  const itemStyles = (idx: number): string => {
    if (idx % 2) {
      return styles.item
    }
    return cs([styles.item, styles.altBg])
  }

  return (
    <Table title={title + ' — ' + dates.dayTime()}>
      <ul>
        <li className={cs([styles.item, styles.titles])}>
          <p className="hidden md:block lg:block">Symbol</p>
          <p>Name</p>
          <p className="text-right">Last</p>
          <p className="text-right">Change</p>
        </li>
        {!data?.length ? (
          <Loading />
        ) : (
          <>
            {data?.map((x, idx) => (
              <li className={itemStyles(idx)} key={x.symbol}>
                <Link
                  href={
                    title === 'Forex'
                      ? '/price/forex/[symbol]'
                      : '/markets/futures/[symbol]'
                  }
                  as={
                    title === 'Forex'
                      ? `/price/forex/${symbolSanitizer(x?.symbol)}`
                      : `/markets/futures/${x.symbol}_${x?.category}_${x?.exchange}`
                  }
                >
                  <>
                    <div className="hidden md:block lg:block">
                      <p className="font-medium">
                        {symbolSanitizer(x?.symbol)}
                      </p>
                    </div>
                    <p className="mt-1 text-xs">{x?.name}</p>
                    <p className="text-right font-medium">
                      {title === 'Forex'
                        ? formatForex(x.lastPrice)
                        : format(x.lastPrice)}
                    </p>
                    <p
                      className={cs([colorize(x.percentChange), 'text-right'])}
                    >
                      {x.percentChange}&#37;
                    </p>{' '}
                  </>
                </Link>
              </li>
            ))}
          </>
        )}
        {showMore && (
          <li className={styles.itemMoreLink}>
            <div className="flex-shrink-1 mx-auto flex max-w-xs justify-center">
              <Link className="moreLink" href={`/markets/futures`}>
                More&nbsp;futures&nbsp;+
              </Link>
            </div>
          </li>
        )}
      </ul>
    </Table>
  )
}

export default FutureForexTable

const Loading = () => {
  return (
    <>
      {[1, 2, 3, 4, 5].map((x) => (
        <li key={x} className={cs([styles.item, styles.loading])}>
          <div className={styles.skeleton}>
            <SkeletonTable />
            <div className="hidden md:flex">
              <SkeletonTable />
            </div>
            <div className="flex justify-end">
              <SkeletonTable />
            </div>
            <div className="flex justify-end">
              <SkeletonTable />
            </div>
          </div>
        </li>
      ))}
    </>
  )
}
