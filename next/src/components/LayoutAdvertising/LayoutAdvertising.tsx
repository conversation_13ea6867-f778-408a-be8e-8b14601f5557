import Head from 'next/head'
import { ErrBoundary } from '../ErrBoundary/ErrBoundary'

interface Props {
  children?: React.ReactNode
  title: string
}
const LayoutAdvertising = ({ children, title }: Props) => {
  return (
    <>
      <Head>
        <title>{title}</title>
      </Head>
      <main>
        <ErrBoundary>{children}</ErrBoundary>
      </main>
    </>
  )
}
export default LayoutAdvertising
