import dynamic from 'next/dynamic'
import type React from 'react'

const AdvancedRealTimeChart = dynamic(
  async () =>
    await import('react-ts-tradingview-widgets').then(
      (x) => x.AdvancedRealTimeChart,
    ),
  {
    ssr: false,
  },
)

export const TradingViewChartWidget: React.FC<{ symbol: string }> = (props) => (
  <AdvancedRealTimeChart
    // autosize={true}
    timezone="America/New_York"
    height="490"
    width="100%"
    symbol={props.symbol}
    hide_side_toolbar={true}
    save_image={false}
    // @ts-ignore
    customer="kitco"
  />
)
