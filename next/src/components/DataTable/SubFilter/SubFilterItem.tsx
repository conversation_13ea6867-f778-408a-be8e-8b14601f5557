import clsx from 'clsx'
import type { FC } from 'react'

/**
 * Sub filter item props
 *
 * @param {string} name - The name of the sub filter item
 * @param {boolean} selected - The selected state of the sub filter item
 * @param {() => void} onSelect - The on select function of the sub filter item
 * @param {boolean} first - Is the first sub filter item
 * @param {boolean} last - Is the last sub filter item
 */
interface SubFilterItemProps {
  name: string
  selected?: boolean
  onSelect?: () => void
  first?: boolean
  last?: boolean
}

/**
 * Sub filter item component for the data table
 *
 * @param name
 * @param selected
 * @param onSelect
 * @param first
 * @param last
 * @constructor
 */
const SubFilterItem: FC<SubFilterItemProps> = ({
  name,
  selected = false,
  onSelect,
  first = false,
  last = false,
}: SubFilterItemProps) => {
  return (
    <button
      type="button"
      className={clsx(
        'flex flex-1 h-10 items-center justify-center gap-2 px-4 py-3 border-0',
        selected ? 'bg-stone-50' : '',
        first ? 'rounded-l-xl' : '',
        last ? 'rounded-r-xl' : 'border-r border-slate-200',
        !!first && !!last ? '' : '',
      )}
      onClick={onSelect}
    >
      <span
        className={clsx(
          "font-['Mulish'] text-xs text-neutral-900 text-nowrap",
          selected ? 'font-bold leading-3' : 'font-normal leading-none',
        )}
      >
        {name}
      </span>
    </button>
  )
}

export default SubFilterItem
