import type { FC, ReactNode } from 'react'
import { CiSearch } from 'react-icons/ci'
import { IoIosClose } from 'react-icons/io'

/**
 * Props for the DataTableSearch component
 *
 * @prop {string} searchTerm - The search term
 * @prop {(value: string) => void} onSearchChange - The search change handler
 * @prop {string} resultText - The result text (e.g. "10 items")
 * @prop {ReactNode} children - The children for showing additional filters
 */
interface DataTableSearchProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  resultText: string
  children?: ReactNode
}

/**
 * Search component for the data table
 */
const DataTableSearch: FC<DataTableSearchProps> = ({
  searchTerm,
  onSearchChange,
  resultText,
  children,
}) => {
  return (
    <div className="my-4 px-2 md:p-0 flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-2">
      <div className="flex flex-col w-full md:w-auto md:flex-row items-start md:items-center gap-4 md:gap-2">
        <div className="flex h-10 min-w-80 w-full md:w-auto items-center rounded-xl border border-gray-300">
          <CiSearch size={20} className="mx-2 text-gray-500" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="Search..."
            className="flex-grow outline-none"
          />
          {searchTerm && (
            <IoIosClose
              size={20}
              className="mx-2 cursor-pointer text-black"
              onClick={() => onSearchChange('')}
            />
          )}
        </div>

        {children && (
          <div className="mx-2 w-full md:w-auto order-first md:order-none">
            {children}
          </div>
        )}
      </div>

      {resultText && (
        <div className="items-center justify-start gap-2 justify-self-end flex">
          <div className="font-['Mulish'] text-sm font-bold leading-none text-neutral-900">
            Results
          </div>
          <div className="flex items-center justify-start rounded-md border border-slate-200 bg-white px-1.5 pb-1 pt-0.5">
            <div className="text-center font-['Mulish'] text-xs font-normal leading-none text-neutral-900">
              {resultText}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DataTableSearch
