import type { SortDirection, SortingState } from '@tanstack/table-core'
import { clsx } from 'clsx'
import type React from 'react'
import { useState } from 'react'
import { GoArrowDown, GoArrowUp } from 'react-icons/go'
import Modal from '~/src/components/Modal/Modal'
import type SortOption from '~/src/types/DataTable/SortOption'

interface SortModalProps {
  isOpen: boolean
  onApply: (value: SortingState) => void
  onRequestClose: () => void
  sortOptions: SortOption[]
  title: string
}

const SortModal: React.FC<SortModalProps> = ({
  isOpen,
  onApply,
  onRequestClose,
  sortOptions,
  title,
}) => {
  const [selectedOption, setSelectedOption] = useState<SortOption | null>(null)
  const [sortOrder, setSortOrder] = useState<SortDirection>('asc')

  /**
   * Handle the apply button click
   */
  const handleApply = () => {
    if (selectedOption) {
      onApply([{ desc: sortOrder === 'desc', id: selectedOption.value }])
      onRequestClose()
    }
  }

  /**
   * Handle the option selection
   *
   * @param option
   */
  const handleSelectOption = (option: SortOption) => {
    setSelectedOption(option)
    handleOrderChange(option)
  }

  /**
   * Handle the order change
   */
  const handleOrderChange = (option = null) => {
    // If the option is not the selected option, return and do not toggle the order
    if (option && selectedOption?.value !== option.value) {
      return
    }

    // Otherwise, toggle the order
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
  }

  /**
   * Handle the reset button click
   */
  const handleReset = () => {
    setSelectedOption(null)
    setSortOrder('asc')
  }

  return (
    <Modal opened={isOpen} onClose={onRequestClose} className="bg-white">
      <div className="text-lg font-bold border-b border-gray-200 pb-2">
        {title}
      </div>
      <div>
        <ul>
          {sortOptions.map((option) => (
            <li
              key={option.value}
              className={clsx(
                'flex justify-between items-center p-2',
                selectedOption?.value === option.value ? 'bg-neutral-100' : '',
              )}
              onClick={() => handleSelectOption(option)}
              onKeyDown={() => handleSelectOption(option)}
            >
              <div className="flex">
                <div className="min-w-6 flex items-center justify-center">
                  {selectedOption?.value === option.value && (
                    <span>
                      {sortOrder === 'asc' ? <GoArrowUp /> : <GoArrowDown />}
                    </span>
                  )}
                </div>
                <span>{option.label}</span>
              </div>
              <div>
                {selectedOption?.value === option.value
                  ? sortOrder === 'asc'
                    ? 'Ascending'
                    : 'Descending'
                  : ''}
              </div>
            </li>
          ))}
        </ul>
        <div className="flex justify-between mt-4">
          <button
            type="button"
            className="p-2 border border-gray-400 rounded"
            onClick={handleReset}
            onKeyDown={handleReset}
          >
            Reset
          </button>
          <button
            type="button"
            className="p-2 bg-black text-white rounded"
            onClick={handleApply}
            onKeyDown={handleApply}
          >
            Apply
          </button>
        </div>
      </div>
    </Modal>
  )
}

export default SortModal
