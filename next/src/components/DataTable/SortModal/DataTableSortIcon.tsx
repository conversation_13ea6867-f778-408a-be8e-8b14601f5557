import type { SortingState } from '@tanstack/table-core'
import { useState } from 'react'
import { BsSliders } from 'react-icons/bs'
import DataTableSortModal from '~/src/components/DataTable/SortModal/DataTableSortModal'
import type SortOption from '~/src/types/DataTable/SortOption'

interface DataTableSortIconProps {
  onSortApply: (value: SortingState) => void
  sortOptions: SortOption[]
  title: string
}

const DataTableSortIcon = ({
  onSortApply,
  sortOptions,
  title,
}: DataTableSortIconProps) => {
  const [isSortModalOpen, setIsSortModalOpen] = useState(false)

  const handleOpenModal = () => {
    setIsSortModalOpen(true)
  }
  return (
    <>
      <button
        type="button"
        onClick={handleOpenModal}
        onKeyDown={handleOpenModal}
        className="border-[#383838] border-2 rounded-md p-1.5 text-[#383838]
        hover:bg-[#5E5E5E] hover:text-white hover:border-[#5E5E5E]
        active:bg-[#060606] active:text-white active:border-[#060606]
        focus:outline-none focus:ring-0"
      >
        <BsSliders size={24} />
      </button>
      <DataTableSortModal
        isOpen={isSortModalOpen}
        onRequestClose={() => setIsSortModalOpen(false)}
        onApply={onSortApply}
        sortOptions={sortOptions}
        title={title}
      />
    </>
  )
}

export default DataTableSortIcon
