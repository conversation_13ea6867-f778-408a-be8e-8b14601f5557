import Link from 'next/link'
import type { FC } from 'react'
import type { Tag } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import cs from '~/src/utils/cs'

export const TrendingTags: FC = () => {
  const { data } = kitcoQuery(
    news.trendingTags({
      options: { enabled: true },
    }),
  )
  return (
    <div className="bg-kitco-black pb-10 pt-[30px]">
      <h4
        className={cs(['pb-4 text-center text-2xl uppercase'])}
        style={{ color: 'white' }}
      >
        <span className="text-[21px] leading-[27px]">Trending Tags</span>
      </h4>
      <div className="mx-auto flex max-w-[1240px] flex-wrap justify-center gap-2 px-10 lg:px-0">
        {data?.trendingTags?.map((x: Tag) => <TagItem key={x.id} tag={x} />)}
      </div>
    </div>
  )
}

const TagItem: FC<{ tag: Tag }> = ({ tag }) => {
  return (
    <Link
      className="rounded border border-white border-opacity-40 px-3.5 py-px leading-7"
      href={tag.urlAlias}
    >
      <span className="text-[12px] text-white">{tag.name}</span>
    </Link>
  )
}
