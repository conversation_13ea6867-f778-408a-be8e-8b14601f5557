import Image from 'next/image'
import Link from 'next/link'
import type { FC } from 'react'
import type { NewsTopContributorsQuery } from '~/src/generated'
import styles from './contributors-sidebar.module.scss'

export const ContributorsSidebar: FC<{ data: NewsTopContributorsQuery }> = ({
  data,
}) => {
  return (
    <div className={styles.contributorsSidebar}>
      <div>
        <h2>Contributors</h2>
        <ul>
          {data?.topContributors?.slice(0, 10)?.map((reporter) => (
            <li key={reporter.id}>
              <Link href={reporter.urlAlias}>
                <div className={styles.imgWrapperCircle}>
                  <Image
                    className="h-[30px] w-[30px] bg-[#f7f7f7] object-contain"
                    src={reporter.imageUrl ?? '/default-avatar.svg'}
                    alt={reporter.name}
                    width={30}
                    height={30}
                    sizes="(max-width: 767px) 30px, 60px"
                  />
                </div>
                {reporter.name}
                <span>&gt;</span>
              </Link>
            </li>
          ))}
        </ul>
        {/* TODO: Handle link contributors */}
        {/* <Link className={styles.showMore} href="/">
          + Show More Contributors
        </Link> */}
      </div>
    </div>
  )
}
