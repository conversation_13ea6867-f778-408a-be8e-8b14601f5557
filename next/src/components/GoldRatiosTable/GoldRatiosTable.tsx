import type { FC } from 'react'
import type { CalculatedRatios } from '~/src/components-metals/GoldRatiosCell/GoldRatiosCell'
import BlockHeader from '~/src/components/BlockHeader/BlockHeader'
import cs from '~/src/utils/cs'
import SkeletonTable from '../SkeletonTable/SkeletonTable'
import styles from './GoldRatiosTable.module.scss'

interface Props {
  data: CalculatedRatios
}

const GoldRatiosTable: FC<Props> = ({ data }) => {
  const dataAsArray = [
    data?.silver,
    data?.platinum,
    data?.palladium,
    data?.xau,
    data?.hui,
    data?.spx,
    data?.dowi,
    data?.crudeOil,
  ]

  return (
    <div className={styles.wrapper}>
      <BlockHeader title={'Gold Ratios'} />

      <div className={styles.gridify}>
        <p></p>
        <p>Silver</p>
        <p>Platinum</p>
        <p>Palladium</p>
        <p>XAU</p>
        <p>HUI</p>
        <p>S&amp;P 500</p>
        <p>DJIA</p>
        <p>Crude <PERSON></p>
      </div>

      <ul>
        <li>
          <div className={styles.gridify}>
            <span className={cs([styles.fatTextBois, styles.isEven])}>
              GOLD
            </span>
            {dataAsArray &&
              dataAsArray.slice(0, 5).map((x, idx) => (
                <span key={idx} className={!(idx % 2) ? '' : styles.isEven}>
                  {!x ? <SkeletonTable /> : x.toFixed(2)}
                </span>
              ))}
            {dataAsArray &&
              dataAsArray.slice(5).map((x, idx) => (
                <span key={idx} className={idx % 2 ? '' : styles.isEven}>
                  {!x ? (
                    <SkeletonTable />
                  ) : idx !== 2 ? (
                    x.toFixed(4)
                  ) : (
                    x.toFixed(2)
                  )}
                </span>
              ))}
          </div>
        </li>
      </ul>
    </div>
  )
}

export default GoldRatiosTable
