.wrapper {
  position: relative;
  display: block;
  margin: 2em 0;
}

.title {
  padding: 4px 0;
  background-color: #373737;
  color: white;
  display: grid;
  place-items: center;
  font-size: 18px;
}

.flexSpaceBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border: solid 1px #cccccc;
  border-top: 0;
  padding: 10px 0;
}

.gridify {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  place-items: center;

  & p {
    height: 52px;
    border-right: solid 1px #cccccc;
    border-bottom: solid 1px #cccccc;
    width: 100%;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
  }

  & p:first-of-type {
    border-left: solid 1px #cccccc;
  }

  & span {
    font-size: 12px;
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: solid 1px #cccccc;
    border-bottom: solid 1px #cccccc;
  }

  & span:first-of-type {
    display: flex;
    justify-content: center;
    align-items: bottom;
    border-left: solid 1px #cccccc;

    & svg {
      margin-top: 2px;
      margin-right: 4px;
    }
  }
}

ul.listify {
  & li:nth-last-of-type(2n) {
    background-color: #f0f0f0;
  }
}

.fatTextBois {
  font-weight: 600;
}

.isEven {
  background-color: #f0f0f0;
}
