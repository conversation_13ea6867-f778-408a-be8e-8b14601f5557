import type { FC } from 'react'
import type { CalculatedRatios } from '~/src/components-metals/GoldRatiosCell/GoldRatiosCell'
import Table from '~/src/components/Table/Table'
import cs from '~/src/utils/cs'
import SkeletonTable from '../SkeletonTable/SkeletonTable'
import styles from './GoldRatiosTableMobile.module.scss'

interface Props {
  data: CalculatedRatios
}

const GoldRatiosTableMobile: FC<Props> = ({ data }) => {
  const dataAsArray = [
    data?.silver,
    data?.platinum,
    data?.palladium,
    data?.xau,
    data?.hui,
    data?.spx,
    data?.dowi,
    data?.crudeOil,
  ]

  return (
    <div className={styles.wrapper}>
      <Table title="Gold Ratios">
        <div className={cs([styles.grid, styles.titles])}>
          <div></div>
          <h6>Silver</h6>
          <h6>Platinum</h6>
          <h6>Palladium</h6>
        </div>
        <div className={cs([styles.grid, styles.values])}>
          <h6 className={styles.goldText}>Gold</h6>
          {dataAsArray &&
            Object.values(dataAsArray)
              .slice(0, 3)
              .map((x, idx) => (
                <span key={idx}>{!x ? <SkeletonTable /> : x.toFixed(2)}</span>
              ))}
        </div>
      </Table>
    </div>
  )
}

export default GoldRatiosTableMobile
