import { Suspense } from 'react'
import * as VideoTeaser from '~/src/components/VideoTeaser/VideoTeaser'
import ArticleMoreButton from '~/src/components/article-more-button/article-more-button.component'
import { vcms } from '~/src/lib/vcms-factory.lib'
import { ErrBoundary } from '../ErrBoundary/ErrBoundary'
import { Query } from '../Query/Query'
import VideoNewsSidebarLoading from './VideoNewsSidebarLoading'

const fetcher = vcms.feed({ variables: { latest: true } })

const VideoNewsSidebar = () => {
  return (
    <ErrBoundary>
      <Suspense fallback={<VideoNewsSidebarLoading />}>
        <Query fetcher={fetcher}>
          {(queryResult) => {
            const v = queryResult?.data?.VideoConsumerFeed?.latest
            return (
              <div className="flex flex-col gap-2">
                {v?.slice(0, 6)?.map((x) => (
                  <VideoTeaser.CtxProvider node={x} key={x.id}>
                    <VideoTeaser.Img width={400} height={340} />
                    <VideoTeaser.A>
                      <VideoTeaser.Title />
                      <VideoTeaser.Timestamp className="text-black" />
                    </VideoTeaser.A>
                  </VideoTeaser.CtxProvider>
                ))}

                <ArticleMoreButton title={'More Videos'} href="/news/video" />
              </div>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export default VideoNewsSidebar
