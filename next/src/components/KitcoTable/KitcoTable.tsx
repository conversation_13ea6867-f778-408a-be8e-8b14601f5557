import type { FC } from 'react'
import Table from '~/src/components/Table/Table'
import cs from '~/src/utils/cs'
import dates from '~/src/utils/dates'
import isNegative from '~/src/utils/isNegative'
import { pf } from '~/src/utils/priceFormatter'
import SkeletonTable from '../SkeletonTable/SkeletonTable'
import styles from './KitcoTable.module.scss'

interface KitcoTableProps {
  title: string
  data: any
}

const Titles = () => (
  <li className={cs([styles.item, styles.titles])}>
    <p className="md:block lg:block">Metal</p>
    <p className="text-right md:block lg:block">Bid</p>
    <p className="hidden text-right md:block lg:block">Ask</p>
    <p className="text-right md:block lg:block">Change</p>
    <p className="hidden text-right md:block lg:block">High</p>
    <p className="hidden text-right md:block lg:block">Low</p>
  </li>
)

const KitcoTable: FC<KitcoTableProps> = ({ data, title }: KitcoTableProps) => {
  const colorize = (n: number) => {
    if (isNegative(n)) {
      return cs([styles.colorRed, styles.bold])
    }
    return cs([styles.colorGreen, styles.bold])
  }

  const howManyLoaders = [1, 2, 3, 4, 5]

  function renderLoading() {
    return (
      <>
        {howManyLoaders.map((x) => (
          <li key={x} className={cs([styles.item, styles.loading])}>
            <SkeletonTable />
            <div className="hidden justify-end md:flex">
              <SkeletonTable />
            </div>
            <div className="hidden justify-end md:flex">
              <SkeletonTable />
            </div>
            <div className="flex justify-end">
              <SkeletonTable />
            </div>
            <div className="hidden justify-end md:flex">
              <SkeletonTable />
            </div>
            <div className="flex justify-end">
              <SkeletonTable />
            </div>
          </li>
        ))}
      </>
    )
  }

  return (
    <Table title={`${title} — ${dates.dayTime()}`}>
      <ul>
        <Titles />
        {!data.length || data[0] === undefined
          ? renderLoading()
          : data
              .map((item) => ({
                ...item,
                name: item.name
                  ? item.name
                  : item.symbol === 'UR'
                    ? 'Uranium'
                    : '',
              }))
              .map((x, idx: number) => (
                <li
                  className={
                    !(idx % 2) ? styles.item : cs([styles.item, styles.altBg])
                  }
                  key={x?.symbol}
                >
                  <div className={styles.itemRow}>
                    <div className="md:block lg:block">
                      <p className="font-semibold">{x?.name}</p>
                    </div>
                    <p className="text-right font-semibold">
                      {pf(x?.results[0]?.bid)}
                    </p>
                    <p className="hidden text-right font-semibold md:block lg:block">
                      {pf(x?.results[0]?.ask)}
                    </p>
                    <div className="md:block lg:block">
                      <p
                        className={cs([
                          colorize(x?.results?.[0].change),
                          'text-right',
                        ])}
                      >
                        {x?.results[0].change.toFixed(2)}
                        &nbsp;
                        {'('}
                        {x?.results[0].changePercentage?.toFixed(2)}
                        &#37;{')'}
                      </p>
                    </div>
                    <p className="hidden text-right font-semibold md:block lg:block">
                      {pf(x?.results[0]?.high)}
                    </p>
                    <p className="hidden text-right font-semibold md:block lg:block">
                      {pf(x?.results[0]?.low)}
                    </p>
                  </div>
                </li>
              ))}
      </ul>
    </Table>
  )
}

export default KitcoTable
