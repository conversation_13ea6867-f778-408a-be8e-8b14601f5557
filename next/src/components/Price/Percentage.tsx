import clsx from 'clsx'
import type { FC } from 'react'
import { BiCaretDown, BiCaretUp } from 'react-icons/bi'
import isZero from '~/src/utils/Math/isZero'

/**
 * Price component props
 */
interface PercentageProps {
  percentage: string
  percentageVal: number
}

/**
 * Component for displaying percentage
 *
 * @param percentage : string Formatted price as string
 * @param percentageVal : number Price value
 * @constructor
 */
const Percentage: FC<PercentageProps> = ({
  percentage,
  percentageVal,
}: PercentageProps) => {
  const percentageIsZero = isZero(percentageVal)

  return (
    <div className="font-['Mulish'] text-xs font-bold leading-3">
      <div
        className={clsx(
          'flex whitespace-nowrap rounded-2xl px-2 py-1.5',
          percentageIsZero
            ? 'bg-gray-50 text-neutral-900'
            : percentageVal > 0
              ? 'bg-green-50 text-green-600'
              : 'bg-red-50 text-red-600',
        )}
      >
        {percentageIsZero ? (
          <></>
        ) : percentageVal > 0 ? (
          <BiCaretUp />
        ) : (
          <BiCaretDown />
        )}
        {percentage} %
      </div>
    </div>
  )
}

export default Percentage
