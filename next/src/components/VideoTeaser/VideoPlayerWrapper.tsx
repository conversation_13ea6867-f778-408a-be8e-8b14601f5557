/*
 * this file exists so that we can dynamically import the video player component
 * into VideoTeaserCtx
 * */
import { Suspense } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import VideoPlayer from '~/src/components-news/VideoPlayer/VideoPlayer.component'
import { vcms } from '~/src/lib/vcms-factory.lib'
import { Query } from '../Query/Query'
import { useVideoTeaserCtx } from './VideoTeaser'

const VideoPlayerWrapper: React.FC = () => {
  const { node } = useVideoTeaserCtx()

  const fetchById = vcms.videoById({
    variables: { id: node?.videoId },
    options: {
      enabled: !!node?.videoId,
      retry: 3,
      retryDelay: 500,
    },
  })

  return (
    <ErrorBoundary fallback={<p>Error loading</p>}>
      <Suspense
        fallback={
          <div className="animate-loading aspect-video h-full w-full bg-white/10" />
        }
      >
        <Query fetcher={fetchById}>
          {(res) => (
            <div className="aspect-video">
              <VideoPlayer
                videoNode={{
                  startTime: node?.startTime,
                  endTime: node?.endTime,
                  snippetUuid: node?.uuid,
                  thumbnailUuid: node?.thumbnailUuid,
                  assetType: 'video',
                  assetUuid: res?.data?.VideoConsumerVideoById?.uuid,
                }}
              />
            </div>
          )}
        </Query>
      </Suspense>
    </ErrorBoundary>
  )
}

export default VideoPlayerWrapper
