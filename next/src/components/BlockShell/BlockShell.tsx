import type React from 'react'
import BlockHeader from '~/src/components/BlockHeader/BlockHeader'

function BlockShell({
  title,
  children,
  href = null,
}: {
  title: string
  children: React.ReactNode
  href?: string
}) {
  return (
    <div className="border border-ktc-borders">
      {title && <BlockHeader title={title} href={href} />}
      {children}
    </div>
  )
}

export default BlockShell
