import { Suspense } from 'react'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { Query } from '~/src/components/Query/Query'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'

type CategoryUnion = 'cryptocurrencies' | 'commodities'

export const LatestNewsSidebar: React.FC<{ category: CategoryUnion }> = ({
  category,
}) => {
  const fetcher = news.newsByCategoryGeneric({
    variables: {
      urlAlias: `/news/category/${category}`,
      limit: 5,
      offset: 0,
    },
  })
  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data }) => {
            return (
              <div className="flex flex-col">
                <h2 className="border-b border-ktc-borders pb-2.5 text-[20px] uppercase">
                  <span>Latest News</span>
                </h2>
                <div className="flex flex-grow flex-col">
                  {data?.nodeListByCategory?.items
                    ?.slice(0, 5)
                    .map((x: ArticleTeaserFragmentFragment) => {
                      return (
                        <div className="mt-5 flex" key={x.id}>
                          <TeaserTextOnly
                            key={x?.id}
                            node={x}
                            hideSummary={true}
                            size={'sm'}
                          />
                        </div>
                      )
                    })}
                </div>
              </div>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}
