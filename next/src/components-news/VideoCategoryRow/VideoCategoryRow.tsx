import { useMemo } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { Query } from '~/src/components/Query/Query'
import { vcms } from '~/src/lib/vcms-factory.lib'
import { Playlist } from '../Playlist/Playlist.component'
import { VideoPlaylistTeaser } from '../VideoPlaylistTeaser/VideoPlaylistTeaser'

export const VideoCategoryRow: React.FC<{ id: number }> = ({ id }) => {
  const memoizedFetcher = useMemo(() => {
    return vcms.categoryById({ variables: { id } })
  }, [id])

  return (
    <ErrorBoundary fallback={<div>Something went wrong</div>}>
      <Query fetcher={memoizedFetcher}>
        {(res) => {
          return (
            <Playlist.Row>
              {!res?.data?.VideoConsumerCategoryById?.edges?.snippets
                ?.length ? (
                <p className="text-2xl text-white">No results found</p>
              ) : (
                res?.data?.VideoConsumerCategoryById?.edges?.snippets
                  ?.slice(0, 4)
                  ?.map((x) => (
                    <VideoPlaylistTeaser
                      isFetching={res.isLoading || res.isFetching}
                      node={x}
                      key={x.id}
                    />
                  ))
              )}
            </Playlist.Row>
          )
        }}
      </Query>
    </ErrorBoundary>
  )
}
