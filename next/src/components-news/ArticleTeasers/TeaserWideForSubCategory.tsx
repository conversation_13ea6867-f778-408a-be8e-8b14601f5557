import Link from 'next/link'
import { type FC, Fragment } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import type { TeaserWideProps } from '~/src/components-news/ArticleTeasers/TeaserWide'
import { aspectRatioCSS } from '~/src/components-news/ArticleTeasers/aspectRatioCSS'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import cs from '~/src/utils/cs'

export const TeaserWideForSubCategory: FC<TeaserWideProps> = ({
  node,
  size,
  aspectRatio,
  hideCategory,
  classTitle,
}) => {
  const sizeCSS = {
    sm: '',
    md: '',
    lg: 'text-[20px] leading-[130%] line-clamp-2',
    xl: 'text-[34px] leading-[115%] line-clamp-4',
  }

  const imgContainerCSS = {
    sm: '',
    md: '',
    lg: 'w-[150px] relative',
    xl: 'w-[410px] pr-2.5',
  }

  const bodyContainerCSS = {
    sm: '',
    md: '',
    lg: 'block pl-5 w-[calc(100%_-_150px)] mt-[-1px]',
    xl: 'block pl-2.5 w-[calc(100%_-_410px)]',
  }

  const summaryCSS = {
    sm: '',
    md: '',
    lg: 'text-[14px] leading-5 line-clamp-2',
    xl: 'text-[18px] leading-6 line-clamp-4',
  }

  const maxHeightCSS = {
    sm: '',
    md: '',
    lg: 'max-h-[100px] h-[100px]',
    xl: 'max-h-[250px]',
  }

  return (
    <div className="mb-[30px] flex w-full last:mb-0 md:mb-10">
      <Link className={imgContainerCSS[size]} href={node?.urlAlias ?? '/'}>
        <ImageMS
          src={
            node?.teaserImage?.detail?.default?.srcset ??
            node?.image?.detail?.default?.srcset ??
            node?.legacyThumbnailImageUrl
          }
          hasLegacyThumbnailImageUrl={!!node?.legacyThumbnailImageUrl}
          alt={`${node?.title} teaser image`}
          priority={true}
          width={600}
          height={340}
          service="icms"
          className={cs([
            aspectRatioCSS[aspectRatio],
            maxHeightCSS[size],
            'block rounded-lg',
            'w-full object-cover',
          ])}
        />
      </Link>
      <div className={bodyContainerCSS[size]}>
        {!hideCategory ? (
          <>
            <CategoryLink
              urlAlias={node?.category?.urlAlias}
              text={node?.category?.name}
            />
            <div className="h-1 bg-transparent" />
          </>
        ) : null}
        <Link href={node?.urlAlias || '/'}>
          <>
            <h3 className={cs([sizeCSS[size], classTitle, 'text-kitco-black'])}>
              {node?.teaserHeadline ?? node?.title}
            </h3>
            <div className="h-2" />
            {(size === 'lg' || size === 'xl') && (
              <Fragment>
                <div
                  className={cs(['summary', summaryCSS[size]])}
                  dangerouslySetInnerHTML={{ __html: node?.teaserSnippet }}
                />
                <Spacer className="h-2" />
              </Fragment>
            )}
            <DateStamp stamp={node?.createdAt} />
          </>
        </Link>
      </div>
    </div>
  )
}
