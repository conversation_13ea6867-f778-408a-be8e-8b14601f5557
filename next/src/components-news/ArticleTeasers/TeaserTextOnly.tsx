import Link from 'next/link'
import type { FC } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import type { TeaserCardProps } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { Spacer } from '~/src/components/spacer/spacer.component'
import UTMGenerator from '~/src/hooks/Global/UTMGenerator'
import cs from '~/src/utils/cs'

export interface TeaserTextOnlyProps extends TeaserCardProps {
  lineClamp?: string
}

export const TeaserTextOnly: FC<TeaserTextOnlyProps> = ({
  node,
  size,
  hideCategory,
  hideSummary,
  utmParams = null,
}) => {
  // If the node is not found, return an empty fragment
  if (!node) return <></>

  // Define the size CSS classes
  const sizeCSS = {
    sm: 'text-[16px] leading-[130%]',
    md: 'text-[20px] leading-[130%]',
    lg: 'text-[24px] leading-[130%]',
    xl: 'text-[24px] leading-[130%]',
  }

  // Generate the article link with UTM parameters if they exist
  const articleLink = utmParams
    ? UTMGenerator(node?.urlAlias ?? '/', utmParams)
    : (node?.urlAlias ?? '/')

  // Generate the category link with UTM parameters if they exist
  const categoryLink = utmParams
    ? UTMGenerator(node?.category?.urlAlias ?? '/', utmParams)
    : node?.category?.urlAlias

  return (
    <div className="w-full">
      {!hideCategory && (
        <>
          <CategoryLink urlAlias={categoryLink} text={node?.category?.name} />
          <div className="h-1 bg-transparent" />
        </>
      )}
      <Link href={articleLink}>
        <>
          <h3 className={cs([sizeCSS[size], 'text-kitco-black'])}>
            {node?.teaserHeadline ?? node?.title}
          </h3>
          {!hideSummary ? (
            <>
              <Spacer className="h-2" />
              <div
                className="summary"
                dangerouslySetInnerHTML={{ __html: node?.teaserSnippet }}
              />
            </>
          ) : null}
          <Spacer className="h-2" />
          <DateStamp stamp={node?.createdAt} />
        </>
      </Link>
    </div>
  )
}
