@import '../../../styles/vars';

.flexWrapper,
.articleDataRight {
  display: flex;

  &:last-child {
    margin-bottom: 70px;
  }

  @media only screen and (max-width: 768px) {
    height: auto;
    border-bottom: thin solid #e0e0e0;
    padding-bottom: 10px;

    &:last-child {
      border-bottom: none;
      margin-bottom: 40px;
    }
  }
}

.imageTeaser {
  max-height: fit-content;
}

.articleContentContainer {
  margin-left: 0;
  padding-right: 2em;
  color: #373737;
  width: 100%;

  @media only screen and (max-width: 768px) {
    padding-right: unset;
  }

  & a {
    color: #373737;
  }

  & a:hover > h3 {
    text-decoration: underline;
  }

  &.contentRight {
    margin-left: 40px;
    padding-right: 0;
  }
}

.articleTitle {
  margin: 0.2em 0;
}

.articleDescription {
  color: #777777;
  & p {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.author {
  margin-top: 1em;
  color: #373737;
  font-size: 0.925em;
}

.date {
  color: #777777;
  font-size: 0.925em;
}

.imageContainer {
  height: 100%;
  width: 260px;

  & img {
    max-height: 100%;
    object-fit: cover;
    object-position: center;
  }

  @media only screen and (max-width: 768px) {
    display: none;
  }
}

//loading
.titleLoading {
  @include loadingState;
  width: 90%;
  height: 19px;
  margin-bottom: 0.2em;
}

.summaryLoading {
  @include loadingState;
  width: 60%;
  height: 17px;
  margin-bottom: 1em;
}

.authorLoading {
  @include loadingState;
  width: 20%;
  height: 15px;
  margin-bottom: 0.2em;
}

.dateLoading {
  @include loadingState;
  width: 15%;
  height: 15px;
  margin-bottom: 0.2em;
}

.imageContainerLoading {
  @include loadingState;
  height: 100%;
  width: 260px;
  background-color: $light-grey;

  @media only screen and (max-width: 768px) {
    display: none;
  }
}

.articleDataRight {
  a {
    font-family: 'Mulish', sans-serif;
  }

  h3 {
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    color: #373737;
  }
}
