@import '../../../styles/vars';

div.item {
  position: relative;
  padding: 10px 0;

  & a {
    font-weight: 500;
    color: navy;
    text-decoration: none;
    white-space: wrap;
    max-width: 100vw;
    font-family: Lato, sans-serif;
    font-weight: 700;
    color: #373737;
  }

  & div.div-font {
    font-family: Mulish, sans-serif;
    .source {
      font-weight: 700;
      padding-right: 5px;
    }
    .date {
      font-weight: 400;
      padding-left: 5px;
    }
  }

  @media only screen and (max-width: 768px) {
    width: 100%;
    white-space: normal;
    -webkit-box-orient: vertical;
    text-overflow: clip;
  }
}

div.itemLoading {
  padding: 10px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -webkit-box-orient: horizontal;

  & p {
    height: 17px;
    background-color: $light-grey;
    width: 100%;
    border-radius: 2px;
  }

  & h5 {
    margin-top: 5px;
    height: 17px;
    background-color: $light-grey;
    width: 30%;
    border-radius: 2px;
  }
}

.title {
  max-width: 100%;
}
