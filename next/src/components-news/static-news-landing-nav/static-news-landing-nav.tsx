import Link from 'next/link'
import { useState } from 'react'
import { IoIosArrowDropdown, IoIosArrowDropdownCircle } from 'react-icons/io'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import fontStyles from '~/src/styles/news-typefaces.module.scss'
import cs from '~/src/utils/cs'
import css from './static-news-landing-nav.module.scss'

export default function StaticNewsLandingNavigation() {
  const { data } = kitcoQuery(news.newsCategoriesTree())
  return (
    <div className={css.wrapper}>
      <ul className="mb-[60px] flex justify-between">
        {data?.categoriesTree?.map(
          (x) =>
            x.status && (
              <li key={x.id}>
                <Link
                  className="text-3xl uppercase text-kitco-black"
                  href={x.urlAlias}
                >
                  {x.name}
                </Link>
              </li>
            ),
        )}
      </ul>
    </div>
  )
}

export function StaticNewsLandingNavigationMobile() {
  const [isOpen, setIsOpen] = useState(false)
  const { data } = kitcoQuery(news.newsCategoriesTree({}))

  return (
    <div className={cs([css.wrapper, 'mb-4'])}>
      <div className="flex items-center justify-between">
        <h1 className="text-[24px] uppercase md:text-[48px]">news</h1>
        <button
          type="button"
          className="flex items-center"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className={cs([fontStyles.titles, 'mr-2'])}>Sections</span>
          {!isOpen ? (
            <IoIosArrowDropdown size={18} />
          ) : (
            <IoIosArrowDropdownCircle size={18} />
          )}
        </button>
      </div>
      {isOpen && (
        <ul className="absolute left-0 z-50 w-full bg-kitco-black p-4">
          {data?.categoriesTree?.map((x) => (
            <>
              {!x.status ? null : (
                <li
                  key={x.id}
                  className="relative border-b border-neutral-600 px-4 py-2 last:border-b-0"
                >
                  <Link
                    href={x.urlAlias}
                    className="text-[16px] text-white"
                    onClick={() => setIsOpen(false)}
                  >
                    <span>{x.name}</span>
                  </Link>
                  <ul className="flex list-disc flex-col">
                    {x?.children?.length > 0 &&
                      x?.children?.map((y) => (
                        <>
                          {!y.status ? null : (
                            <li key={`${x.id}${x.name}`}>
                              <Link
                                href={y.urlAlias}
                                className="pl-6 text-white"
                                onClick={() => setIsOpen(false)}
                              >
                                <span>{y?.name}</span>
                              </Link>
                            </li>
                          )}
                        </>
                      ))}
                  </ul>
                </li>
              )}
            </>
          ))}
        </ul>
      )}
    </div>
  )
}
