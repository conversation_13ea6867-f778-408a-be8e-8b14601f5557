import { clsx } from 'clsx'
import * as Teaser from '~/src/components-news/Teasers/Teasers'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'

export const FeaturedArticle: React.FC<{
  data: ArticleTeaserFragmentFragment
}> = ({ data }) => (
  <Teaser.CtxProvider
    node={data}
    className={clsx('mb-10 flex flex-col md:flex-row')}
  >
    <Teaser.TImage
      width={600}
      height={340}
      className={clsx('w-full md:w-[400px] md:min-w-[400px]')}
    />
    <div className="block pl-0 md:pl-5">
      <Teaser.Category className="mt-4 md:mb-1 md:mt-0" />
      <div className="flex flex-col">
        <Teaser.TitleLink
          className={clsx(
            'text-[24px] leading-[130%]',
            'line-clamp-3 lg:text-[34px] lg:leading-[115%]',
          )}
        />
        <Teaser.Summary className="summary my-2 text-[16px] leading-[135%] md:text-[18px] md:leading-6" />
        <Teaser.DateTime />
      </div>
    </div>
  </Teaser.CtxProvider>
)
