import { type FC, Fragment } from 'react'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserCardMobile } from '~/src/components-news/ArticleTeasers/TeaserCardMobile'
import { NoRelatedArticlesShowLatestNews } from '~/src/components-news/RelatedNews/NoRelatedArticlesShowLatestNews'
import type {
  ArticleTeaserFragmentFragment,
  NewsByCategoryGenericQuery,
} from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

export const RelatedArticles: FC<{
  currentNodeCategory: string
  currentNodeId: number
  listUrlAliasInQueue?: string[]
}> = ({ currentNodeCategory, currentNodeId, listUrlAliasInQueue }) => {
  const { data } = kitcoQuery(
    news.newsByCategoryGeneric({
      variables: {
        limit: 8,
        offset: 0,
        includeRelatedCategories: false,
        urlAlias: currentNodeCategory,
      },
      options: {
        enabled: true,
        // transform items returned by query
        select: (d: NewsByCategoryGenericQuery): NewsByCategoryGenericQuery => {
          const filterItems: any = d?.nodeListByCategory?.items
            // filter commentaries
            // ?.filter((x) => x?.__typename !== "Commentary")
            // filter current node
            ?.filter(
              (x: any) => Object.keys(x).length !== 0 && x.id !== currentNodeId,
            )
            ?.filter(
              (x: any) =>
                Object.keys(x).length !== 0 &&
                (listUrlAliasInQueue?.length > 0
                  ? !listUrlAliasInQueue.includes(x.urlAlias)
                  : true),
            )
            ?.map((x) => {
              if (x.__typename === 'Commentary') {
                return {
                  ...x,
                  category: {
                    urlAlias: '/opinion',
                    name: 'Opinions',
                  },
                }
              }
              return x
            })
            .slice(0, 3)

          return {
            ...d,
            nodeListByCategory: {
              ...d?.nodeListByCategory,
              items: filterItems,
            },
          }
        },
      },
    }),
  )

  const articles = data?.nodeListByCategory?.items ?? []

  return (
    <aside>
      <h3 className="font-mulish mb-5 border-b border-ktc-borders pb-2.5 text-2xl">
        Related Articles
      </h3>
      {articles.length === 0 ? (
        <NoRelatedArticlesShowLatestNews
          currentNodeId={currentNodeId}
          classTeaserCard="grid gap-10 grid-cols-1 md:grid-cols-3 grid-row-3 md:grid-row-1"
        />
      ) : (
        <div className="grid-row-3 md:grid-row-1 grid grid-cols-1 gap-10 md:grid-cols-3">
          {articles?.map((article: ArticleTeaserFragmentFragment) => {
            return (
              <Fragment key={article.id}>
                <TeaserCard
                  node={article}
                  size="sm"
                  classWrapper="hidden md:block"
                />
                <TeaserCardMobile
                  node={article}
                  size="sm"
                  classWrapper="md:hidden"
                />
              </Fragment>
            )
          })}
        </div>
      )}
    </aside>
  )
}
