import { type FC, useCallback } from 'react'
import type { NewsTopContributorsQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { ReporterOrContributorItem } from '../ReporterOrContributorItem/ReporterOrContributorItem'
import styles from './ContributorsCell.module.scss'

const ContributorsCell: FC = () => {
  const { data } = kitcoQuery(
    news.topContributors({
      options: {
        enabled: true,
        select: useCallback((d: NewsTopContributorsQuery) => {
          return {
            ...d,
            topContributors: [
              ...d.topContributors.filter((x) => x.hidden === false),
            ],
          }
        }, []),
      },
    }),
  )

  return (
    <div className={styles.contributorsSidebar}>
      <div>
        <h2>Contributors</h2>
        <ul>
          {data?.topContributors
            ?.slice(0, 10)
            ?.map((reporter) => (
              <ReporterOrContributorItem item={reporter} key={reporter.id} />
            ))}
        </ul>
        {/* TODO: Handle link contributors */}
        {/* <Link className={styles.showMore} href="/">
          + Show More Contributors
        </Link> */}
      </div>
    </div>
  )
}

export default ContributorsCell
