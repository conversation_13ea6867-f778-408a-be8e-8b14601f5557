import type { FC } from 'react'
import type { ArticleProps } from '~/src/components-news/Article/ArticleProps'
import { listAuthorStr } from '~/src/components-news/Article/ListAuthorStr'
import CommentDrawer from '~/src/components/Comment/CommentDrawer'
import SocialsSideBar from '~/src/components/socials/SocialsSideBar'

export const SideBarArticle: FC<ArticleProps> = ({ articleData }) => {
  return (
    <>
      <div
        className="sticky top-[110px] m-0 flex hidden flex-col justify-start self-start border-0
        p-0 pr-4 align-baseline text-black md:flex md:items-center
        md:gap-16"
      >
        <aside
          className="m-0 flex w-full flex-col items-center justify-start
          gap-8 border-0 p-0 align-baseline"
        >
          <SocialsSideBar
            hidePrint={true}
            listAuthorStr={listAuthorStr(articleData)}
          >
            <CommentDrawer
              className=""
              category="news"
              storyID={articleData.id}
              elementID="ArticleCommentBoxDesktop"
            />
          </SocialsSideBar>
        </aside>
      </div>
    </>
  )
}
