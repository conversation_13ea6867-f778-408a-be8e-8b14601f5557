import type { FC } from 'react'
import type { ArticleProps } from '~/src/components-news/Article/ArticleProps'
import ArticleAudioPlayer from '~/src/components-news/ArticleAudioPlayer/ArticleAudioPlayer'
import { Spacer } from '~/src/components/spacer/spacer.component'

export const AudioPlayer: FC<{
  articleData: ArticleProps['articleData']
}> = ({ articleData }) => {
  if (!articleData?.audioTts?.assetUuid) return

  return (
    <div className="w-1/2 md:w-full ">
      <Spacer className="md:h-[30px]" />
      <ArticleAudioPlayer assetSnippetUuid={articleData?.audioTts?.assetUuid} />
    </div>
  )
}
