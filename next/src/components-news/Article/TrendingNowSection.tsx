import type { FC } from 'react'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

export const TrendingNowSection: FC = () => {
  const { data } = kitcoQuery(news.newsTrending({ variables: { limit: 10 } }))

  return (
    <div className="flex flex-col">
      <h3
        className={
          'border-b border-ktc-borders pb-2.5 text-[20px] leading-[26px]'
        }
      >
        <span>Trending News</span>
      </h3>
      <div className="flex flex-grow flex-col justify-between">
        {data?.nodeListTrending
          ?.slice(0, 5)
          .map((x: ArticleTeaserFragmentFragment) => {
            return (
              <div className="mt-5 flex" key={x.id}>
                <TeaserTextOnly
                  key={x?.id}
                  node={x}
                  hideSummary={true}
                  size={'sm'}
                  lineClamp="line-clamp-2"
                />
              </div>
            )
          })}
      </div>
    </div>
  )
}
