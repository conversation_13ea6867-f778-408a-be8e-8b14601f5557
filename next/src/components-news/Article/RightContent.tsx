import Script from 'next/script'
import { type FC, useEffect } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { FeaturedMedia } from '~/src/components-news/Article/ArticleFeaturedMedia.component'
import type { ArticleProps } from '~/src/components-news/Article/ArticleProps'
import { BodyBlock } from '~/src/components-news/Article/BodyBlock'
import { BulletNews } from '~/src/components-news/Article/BulletNews'
import { Disclaimer } from '~/src/components-news/Article/Disclaimer'
import { LowerShareBlock } from '~/src/components-news/Article/LowerShareBlock'
import { SideBarArticle } from '~/src/components-news/Article/SideBarArticle'
import { Tags } from '~/src/components-news/Article/Tags'
import { TrendingNowSection } from '~/src/components-news/Article/TrendingNowSection'
import AuthorDetails from '~/src/components-news/AuthorDetails/AuthorDetails'
import BuySellButton from '~/src/components/BuySellButton/BuySellButton'
import { Spacer } from '~/src/components/spacer/spacer.component'

declare global {
  interface Window {
    dianomiReloadContext: any
  }
}

export const RightContent: FC<ArticleProps> = ({ articleData, counter }) => {
  // We have 2 pairs of ads that need to be duplicated, but several of the right rail unit ID's already exist, so we need to make sure
  // we don't catch the wrong ad unit. Essentially we can't just increase by 1.
  const getPairs = (counter: number) => {
    if (counter < 0) {
      return ['1', '2']
    }

    if (counter > 3) {
      return 0
    }

    const pairs = [
      ['5', '6'],
      ['7', '8'],
      ['9', '10'],
      ['11', '12'],
    ]

    return pairs[counter]
  }

  const first = getPairs(counter)[0]
  const second = getPairs(counter)[1]

  useEffect(() => {
    // The dianomi script only loads once, this calls their API to ensure that when another ad unit is added, it gets filled.
    if (window.dianomiReloadContext) {
      window.dianomiReloadContext()
    }
  }, [])

  return (
    <div className="inline w-[calc(100%_-_190px)] pl-10 md:flex md:flex-row-reverse md:gap-4 ">
      <article>
        <div className="block justify-items-end gap-10 lg:flex lg:grid-cols-3">
          <div className="col-span-1 lg:col-span-2 lg:w-[calc(100%_-_300px_-_40px)]">
            <BulletNews summaryBullets={articleData?.summaryBullets} />
            <FeaturedMedia articleData={articleData} />
            <BodyBlock
              body={articleData?.bodyWithEmbeddedMedia}
              exitsPresentationImage={
                !!articleData?.image?.detail?.default?.srcset
              }
              counter={counter}
            />
            <Spacer className="h-6" />
            <AuthorDetails
              authorData={articleData?.author}
              supportingAuthors={articleData?.supportingAuthors}
            />
            {articleData?.tags?.length ? (
              <>
                <Spacer className="h-6" />
                <Tags data={articleData?.tags} />
              </>
            ) : (
              <></>
            )}
            <Spacer className="h-6" />
            <LowerShareBlock articleData={articleData} />
            <Spacer className="h-6 md:hidden" />
            <Disclaimer />
            {counter !== -1 && (
              <>
                <div
                  className="dianomi_context mt-6 min-h-[702px] w-full tablet:min-h-[400px]"
                  data-dianomi-context-id="246"
                />
                <Script
                  src="https://www.dianomi.com/js/contextfeed.js"
                  id="dianomi_context_script"
                />
              </>
            )}
          </div>
          <aside className="hidden w-[300px] lg:col-span-1 lg:block">
            {counter <= 3 && (
              <AdvertisingSlot
                id={`right-rail-${first}`}
                className={'mx-auto mb-10 min-h-[250px] w-[300px]'}
              />
            )}
            <BuySellButton
              width={240}
              containerClassName="py-5 flex justify-center"
              buttonClassName="text-[14px]"
            />
            <div className="rounded-md border border-ktc-borders p-5">
              <TrendingNowSection />
            </div>
            {counter <= 3 && (
              <AdvertisingSlot
                id={`right-rail-${second}`}
                className={
                  'sticky top-[100px] mx-auto mt-10 mb-[200px] min-h-[600px] w-[300px]'
                }
              />
            )}
          </aside>
        </div>
      </article>
      <SideBarArticle articleData={articleData} />
    </div>
  )
}
