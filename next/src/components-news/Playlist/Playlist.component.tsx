import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import type { VideoSnippetFragmentFragment } from '~/src/generated'
import { teaserTimestamp } from '~/src/utils/teaser-timestamp'
import { CategoryTeaserLink } from '../CategoryTeaserLink/CategoryTeaserLink.component'

const Title = ({ children }: { children: React.ReactNode }) => (
  <h3 className="py-8 text-xl uppercase">{children}</h3>
)

const Row = ({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) => (
  <div
    className={clsx(
      'grid grid-cols-1 gap-8 md:grid-cols-4 md:gap-x-4 md:gap-y-[50px]',
      !className ? undefined : className,
    )}
  >
    {children}
  </div>
)

function TeaserItem({
  video,
  hidePlaylistLink,
}: {
  video: VideoSnippetFragmentFragment
  hidePlaylistLink?: boolean
}) {
  return (
    <div className="relative flex flex-col">
      <div className="relative mb-2 flex aspect-video w-full">
        <Link href={video?.frontendPath || '/news/video'} className="w-full">
          <ImageMS
            src={`${video?.uuid}/${video?.thumbnailUuid}.jpeg`}
            alt={`${video?.headline} teaser image`}
            priority={true}
            width={400}
            height={340}
            service="vcms"
            className="relative aspect-video h-full w-full rounded-md"
          />
          <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
            <Image
              width={40}
              height={40}
              src={'/bi_play-circle-fill.svg'}
              alt="circle-play-icon"
            />
          </div>
        </Link>
      </div>
      {hidePlaylistLink || !video?.category ? null : (
        <CategoryTeaserLink
          urlAlias={`/news/video${video?.category?.urlAlias}`}
          text={video?.category?.name}
        />
      )}

      <Link href={video?.frontendPath || '/news/video'} className="relative">
        <h3 className="py-[5px] text-base !text-white">{video?.headline}</h3>
        <time
          className="text-xs text-white opacity-60"
          dateTime={teaserTimestamp(video?.createdAt)}
        >
          {teaserTimestamp(video?.createdAt)}
        </time>
      </Link>
    </div>
  )
}

function TeaserItemLoading() {
  return (
    <div className="relative flex flex-col">
      <div className="relative mb-2 flex aspect-video w-full">
        <span className="animate-loading block h-full w-full" />
      </div>
      <span className="animate-loading mt-4 block h-4 w-24" />

      <span className="animate-loading my-4 block h-6 w-48" />
    </div>
  )
}

export const Playlist = {
  Title,
  Row,
  TeaserItem,
  TeaserItemLoading,
}
