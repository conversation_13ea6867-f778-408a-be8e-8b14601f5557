/**
 * Firebase error messages for the different error codes
 * An user-friendly error message is provided for each error code.
 */
const errorMessages = {
  'auth/account-exists-with-different-credential':
    'An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.',
  'auth/admin-restricted-operation':
    'This action is restricted. Please contact support if you think this is a mistake.',
  'auth/already-initialized': 'The service has already been set up.',
  'auth/app-not-authorized':
    'This application is not authorized to connect. Please contact support.',
  'auth/app-not-installed': 'The application is not installed on your device.',
  'auth/argument-error':
    'There was an error with the information provided. Please check and try again.',
  'auth/cancelled-popup-request':
    'The sign-in window was closed before completing sign-in. Please try again.',
  'auth/captcha-check-failed': 'CAPTCHA verification failed. Please try again.',
  'auth/claims-too-large':
    'The provided information is too large. Please reduce the size and try again.',
  'auth/code-expired':
    'The verification code has expired. Please request a new code.',
  'auth/cordova-not-ready':
    'The application is not ready. Please close and reopen the application.',
  'auth/cors-unsupported': 'This action is not supported by your browser.',
  'auth/credential-already-in-use':
    'This account is already in use with a different sign-in method.',
  'auth/custom-token-mismatch':
    'Sign-in attempt using a different method than originally used.',
  'auth/dependent-sdk-initialized-before-auth':
    'An error occurred during initialization. Please restart the application.',
  'auth/dynamic-link-not-activated':
    'Dynamic Links are not activated. Please enable them in the Firebase console.',
  'auth/email-already-exists':
    'This email is already in use. Please use a different email or sign in.',
  'auth/email-change-needs-verification':
    'Email changes require verification. Please verify your new email address.',
  'auth/emulator-config-failed':
    'An error occurred with the emulator configuration.',
  'auth/expired-action-code':
    'The action code has expired. Please request a new one.',
  'auth/id-token-expired': 'Your session has expired. Please sign in again.',
  'auth/id-token-revoked':
    'Your session has been revoked. Please sign in again.',
  'auth/insufficient-permission':
    'You do not have permission to perform this action. Please check your user status or contact support.',
  'auth/internal-error':
    'An unexpected error occurred. Please try again later or contact support if the issue persists.',
  'auth/invalid-action-code':
    'The action code is invalid. Please check the code and try again.',
  'auth/invalid-api-key':
    'Your API key is invalid. Please check you have the correct key.',
  'auth/invalid-app-credential': 'The app credentials are invalid.',
  'auth/invalid-app-id': 'The app ID is invalid.',
  'auth/invalid-argument':
    'Some information provided was not valid. Please check your input and try again.',
  'auth/invalid-auth-event':
    'An error occurred during authentication. Please try again.',
  'auth/invalid-cert-hash':
    'An error occurred with certificate verification. Please contact support.',
  'auth/invalid-claims':
    'Some provided information is invalid. Please check and try again.',
  'auth/invalid-continue-uri':
    'The URL provided is not valid. Please check and try again.',
  'auth/invalid-cordova-configuration':
    'There is a problem with the app configuration.',
  'auth/invalid-creation-time':
    'The creation time is not valid. Please check and try again.',
  'auth/invalid-credential':
    'The credentials provided are not valid. Please check and try again.',
  'auth/invalid-custom-token': 'There was an error with the sign-in method.',
  'auth/invalid-disabled-field': 'The disabled field value is not valid.',
  'auth/invalid-display-name':
    'The display name provided is not valid. Please ensure it is a non-empty string.',
  'auth/invalid-dynamic-link-domain':
    'The dynamic link domain is not properly configured.',
  'auth/invalid-email':
    'The email address provided is not valid. Please check and try again.',
  'auth/invalid-email-verified': 'The email verification status is not valid.',
  'auth/invalid-emulator-scheme': 'There is a problem with the emulator setup.',
  'auth/invalid-hash-algorithm':
    'An internal error occurred. Please try again later.',
  'auth/invalid-hash-block-size':
    'An internal error occurred. Please try again later.',
  'auth/invalid-hash-derived-key-length':
    'An internal error occurred. Please try again later.',
  'auth/invalid-hash-key':
    'An internal error occurred. Please try again later.',
  'auth/invalid-hash-memory-cost':
    'An internal error occurred. Please try again later.',
  'auth/invalid-hash-parallelization':
    'An internal error occurred. Please try again later.',
  'auth/invalid-hash-rounds':
    'An internal error occurred. Please try again later.',
  'auth/invalid-hash-salt-separator':
    'An internal error occurred. Please try again later.',
  'auth/invalid-id-token':
    'Your sign-in session is not valid. Please sign in again.',
  'auth/invalid-last-sign-in-time': 'The last sign-in time is not valid.',
  'auth/invalid-message-payload': 'The message payload is invalid.',
  'auth/invalid-multi-factor-session':
    'The multi-factor session is invalid. Please try again.',
  'auth/invalid-oauth-client-id': 'The OAuth client ID is invalid.',
  'auth/invalid-oauth-provider': 'The sign-in provider is invalid.',
  'auth/invalid-oauth-responsetype':
    'An internal error occurred. Please try again later.',
  'auth/invalid-page-token': 'The page token is not valid.',
  'auth/invalid-password':
    'The password is not valid. Passwords must be at least 6 characters long.',
  'auth/invalid-password-hash':
    'An internal error occurred. Please try again later.',
  'auth/invalid-password-salt':
    'An internal error occurred. Please try again later.',
  'auth/invalid-persistence-type':
    'An internal error occurred. Please try again later.',
  'auth/invalid-phone-number':
    'The phone number is invalid. Please enter a valid number.',
  'auth/invalid-photo-url': 'The photo URL is invalid.',
  'auth/invalid-provider-data': 'The provider data is not valid.',
  'auth/invalid-provider-id': 'The provider ID is invalid.',
  'auth/invalid-recaptcha-action': 'The reCAPTCHA action is not valid.',
  'auth/invalid-recaptcha-token': 'The reCAPTCHA token is not valid.',
  'auth/invalid-recaptcha-version': 'The reCAPTCHA version is not supported.',
  'auth/invalid-req-type': 'The request type is invalid.',
  'auth/invalid-session-cookie-duration': 'The session duration is not valid.',
  'auth/invalid-uid': 'The user ID is not valid.',
  'auth/invalid-user-import': 'There was a problem importing the user.',
  'auth/invalid-user-token':
    'Your session is no longer valid. Please sign in again.',
  'auth/invalid-verification-code': 'The verification code is incorrect.',
  'auth/invalid-verification-id':
    'The verification process did not complete. Please try again.',
  'auth/maximum-second-factor-count-exceeded':
    'You have reached the maximum number of verification methods allowed.',
  'auth/maximum-user-count-exceeded':
    'The maximum number of users has been reached.',
  'auth/missing-android-pkg-name': 'The Android package name is required.',
  'auth/missing-client-type': 'The client type is missing.',
  'auth/missing-continue-uri': 'The continue URL is missing.',
  'auth/missing-hash-algorithm': 'The hashing algorithm is missing.',
  'auth/missing-iframe-start': 'An error occurred. Please try again.',
  'auth/missing-ios-bundle-id': 'The iOS bundle ID is required.',
  'auth/missing-oauth-client-secret': 'The OAuth client secret is required.',
  'auth/missing-or-invalid-nonce':
    'The request is missing or has an invalid nonce.',
  'auth/missing-recaptcha-token': 'The reCAPTCHA token is missing.',
  'auth/missing-recaptcha-version': 'The reCAPTCHA version is missing.',
  'auth/missing-uid': 'The user ID is missing.',
  'auth/network-request-failed':
    'A network error (such as timeout, interrupted connection, or unreachable host) has occurred.',
  'auth/no-auth-event': 'An internal error has occurred.',
  'auth/no-such-provider':
    'The requested sign-in method is not available for this account.',
  'auth/null-user': 'An error occurred. Please try again.',
  'auth/operation-not-allowed':
    'This sign-in method is disabled. Please contact support or try a different sign-in method.',
  'auth/operation-not-supported-in-this-environment':
    'This operation is not supported in your current environment.',
  'auth/phone-number-already-exists':
    'The phone number is already in use. Please use a different phone number.',
  'auth/popup-blocked':
    'The sign-in window was blocked by the browser. Please allow pop-ups and try again.',
  'auth/popup-closed-by-user':
    'The sign-in window was closed before completing sign-in. Please try again.',
  'auth/project-not-found':
    'No project was found with the provided details. Please check your project configuration.',
  'auth/provider-already-linked':
    'This account is already linked to the requested sign-in method.',
  'auth/quota-exceeded':
    'You have exceeded your quota. Please try again later.',
  'auth/recaptcha-not-enabled':
    'reCAPTCHA verification is required but not enabled in the Firebase console.',
  'auth/redirect-cancelled-by-user':
    'The sign-in process was cancelled. Please try again.',
  'auth/redirect-operation-pending':
    'A sign-in operation is already in progress. Please complete it before starting a new one.',
  'auth/rejected-credential':
    'The sign-in attempt was rejected. Please try a different method.',
  'auth/requires-recent-login':
    'This operation requires recent authentication. Please sign in again and retry.',
  'auth/reserved-claims':
    'Some information provided is not allowed. Please check and try again.',
  'auth/second-factor-already-in-use':
    'This verification method is already in use. Please choose a different method.',
  'auth/session-cookie-expired':
    'Your session has expired. Please sign in again.',
  'auth/session-cookie-revoked':
    'Your session has been revoked. Please sign in again.',
  'auth/tenant-id-mismatch':
    'There was an error with your sign-in session. Please sign in again.',
  'auth/timeout': 'The request timed out. Please try again.',
  'auth/too-many-requests':
    'We have detected too many requests from your device. Please wait a while then try again.',
  'auth/uid-already-exists':
    'The user ID is already in use. Please provide a different ID.',
  'auth/unauthorized-continue-uri':
    'The continue URL is not authorized. Please check your project settings.',
  'auth/unauthorized-domain':
    'This domain is not authorized for OAuth operations.',
  'auth/unsupported-persistence-type':
    'The current environment does not support the selected persistence type.',
  'auth/unsupported-tenant-operation':
    'This operation is not supported in a multi-tenant context.',
  'auth/unverified-email':
    'Your email address is unverified. Please verify your email and try again.',
  'auth/user-cancelled': 'The user cancelled the sign-in process.',
  'auth/user-disabled':
    'The user account has been disabled. Please contact support.',
  'auth/user-mismatch':
    'The supplied credentials do not correspond to the previously signed in user.',
  'auth/user-not-found': 'No user found with the provided information.',
  'auth/user-signed-out': '',
  'auth/user-token-expired': 'Your session has expired. Please sign in again.',
  'auth/weak-password':
    'The password is too weak. Please choose a stronger password.',
  'auth/web-storage-unsupported':
    'This browser is not supported or third-party cookies and data may be disabled.',
  'auth/wrong-password':
    'The password is incorrect or the user does not have a password.',
}

/**
 * Firebase error messages
 * This function returns the error message for the provided error code.
 * If the error code is not found, it returns the default error message.
 *
 * @param errorCode
 * @param defaultError
 */
const firebaseError = (
  errorCode: string,
  defaultError = 'An unexpected error occurred. Please try again later.',
) => {
  // Return the error message if it exists, otherwise return the default error message
  return errorMessages[errorCode] || defaultError
}

export default firebaseError
