import type { UserRecord } from 'firebase-admin/auth'
import { sanitizeEmail } from '~/src/features/auth/sanitize'
import admin from './config'

/**
 * Get the user data by the given email.
 * Throws an error if the user does not exist.
 *
 * @param email
 */
export const getUserByEmail = async (email: string): Promise<UserRecord> => {
  try {
    return await admin.auth().getUserByEmail(sanitizeEmail(email))
  } catch (error) {
    console.error('Error fetching user data:', error)
    return null
  }
}

/**
 * Verify the ID token of a user.
 *
 * @param {string} idToken Token ID
 * @returns {Promise<admin.auth.DecodedIdToken>} Promise with the decoded token
 */
export async function getUserFromIdToken(idToken: string) {
  try {
    // Verifica el token ID con Firebase Admin
    return await admin.auth().verifyIdToken(idToken)
  } catch (error) {
    console.error('Error verifying Firebase ID token:', error)
    throw error
  }
}

/**
 * Revokes all refresh tokens for the given user.
 *
 * @param uid
 */
export const revokeUserTokens = async (uid: string) => {
  await admin.auth().revokeRefreshTokens(uid)
  // const user = await admin.auth().getUser(uid)
  // const timestamp = new Date(user.tokensValidAfterTime).getTime() / 1000
  return true
}

/**
 * Verifies the given token and returns the user credentials.
 * This function is used to verify the token on the server side
 * using Firebase Admin SDK.
 *
 * @param {string} token
 * @returns {Promise<admin.auth.UserRecord>} User record
 */
export const verifyToken = async (
  token: string,
): Promise<admin.auth.UserRecord> => {
  try {
    const decodedToken = await admin.auth().verifyIdToken(token)
    return await admin.auth().getUser(decodedToken.uid)
  } catch (error) {
    console.error('Firebase Service: Token verification failed:', {
      message: error.message,
      code: error.code,
    })
    throw new Error('Token verification failed')
  }
}
