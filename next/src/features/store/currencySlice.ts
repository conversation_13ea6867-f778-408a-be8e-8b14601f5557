import { type PayloadAction, createSlice } from '@reduxjs/toolkit'
import { CURRENCY, type Currency } from '~/src/utils/currencies'
import safeParseJSON from '~/src/utils/safeParseJSON'

/**
 * Interface representing the state structure for currency management.
 */
interface CurrencyState {
  // The currently selected currency.
  selectedCurrency: Currency

  // A temporary currency that might be used for specific transactions or display.
  temporaryCurrency: Currency | null
}

/**
 * Initial state for the currency slice.
 * `selectedCurrency` defaults to USD, and `temporaryCurrency` defaults to CNY.
 */
const initialState: CurrencyState = {
  // Default selected currency
  selectedCurrency: CURRENCY.USD,

  // Default temporary currency
  temporaryCurrency: CURRENCY.CNY,
}

/**
 * Currency slice that handles the state management of selected and temporary currencies.
 * This slice includes actions for setting the currency, setting a temporary currency,
 * and loading the currency from local storage.
 */
const currencySlice = createSlice({
  // Name of the slice
  name: 'currency',
  // Initial state defined above
  initialState,

  reducers: {
    /**
     * Sets the selected currency and persists it in local storage.
     * @param {CurrencyState} state - The current state of the currency slice.
     * @param {PayloadAction<Currency>} action - The action containing the new selected currency.
     */
    setCurrency(state: CurrencyState, action: PayloadAction<Currency>) {
      state.selectedCurrency = action.payload // Update the state with the new currency
      localStorage.setItem('selectedCurrency', JSON.stringify(action.payload)) // Persist the selected currency to localStorage
    },
    /**
     * Sets a temporary currency that may be used temporarily within the application.
     * This currency is not persisted in local storage.
     * @param {CurrencyState} state - The current state of the currency slice.
     * @param {PayloadAction<Currency>} action - The action containing the new temporary currency.
     */
    setTemporaryCurrency(
      state: CurrencyState,
      action: PayloadAction<Currency>,
    ) {
      state.temporaryCurrency = action.payload // Update the state with the new temporary currency
    },
    /**
     * Loads the selected currency from local storage, if available.
     * If a valid currency is found, it is set as the selected currency.
     * @param {CurrencyState} state - The current state of the currency slice.
     */
    loadCurrencyFromStorage(state: CurrencyState) {
      // Retrieve the currency from localStorage
      const storedCurrency = localStorage.getItem('selectedCurrency')

      // Safely parse the retrieved JSON
      const parsedCurrency = safeParseJSON(storedCurrency)

      if (parsedCurrency) {
        // Update the state with the parsed currency
        state.selectedCurrency = parsedCurrency
      }
    },
  },
})

// Export actions for use in components or other parts of the application
export const { setCurrency, setTemporaryCurrency, loadCurrencyFromStorage } =
  currencySlice.actions

// Export the reducer to be included in the store
export default currencySlice.reducer
