/**
 * Handles the SurveyMonkey script content.
 *
 * If the script content matches the SurveyMonkey script, it will return an
 * iframe with the script content for avoid conflicts with the current page.
 *
 * @param scriptContent
 * @param isURL
 */
export const handleSurveyMonkeyScript = (
  scriptContent: string,
  isURL: boolean,
) => {
  // SurveyMonkey script regex
  const surveyMonkeyScriptRegex =
    /t\.SMCX=t\.SMCX\|\|\[\],e\.getElementById\(n\)\|\|.*c\.src="([^"]+)"/i

  // If the script content matches the SurveyMonkey script, return the iframe content
  if (surveyMonkeyScriptRegex.test(scriptContent)) {
    return {
      serviceHTML: createIframe(scriptContent),
      serviceScriptInline: '',
      serviceScriptURL: '',
    }
  }

  // If the script content doesn't match the SurveyMonkey script, return original content
  return {
    serviceHTML: '',
    serviceScriptInline: !isURL ? scriptContent : '',
    serviceScriptURL: isURL ? scriptContent : '',
  }
}

/**
 * Creates the iframe content with the SurveyMonkey script.
 *
 * @param scriptURL
 */
const createIframeContent = (scriptURL: string) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Survey Monkey Script</title>
      <script>${scriptURL.trim()}</script>
    </head>
    <body style='margin: 0;'>
      <div id='smcx-sdk'></div>
    </body>
    </html>
  `
}

/**
 * Encodes the HTML content.
 *
 * @param str
 */
const encodeHTML = (str: string) => {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
    .replace(/\n/g, '')
}

/**
 * Creates the iframe with the SurveyMonkey script content.
 *
 * @param scriptContent
 */
const createIframe = (scriptContent: string) => {
  const iframeContent = encodeHTML(createIframeContent(scriptContent))

  return `
    <iframe
      srcdoc="${iframeContent}"
      style="width: 100%; height: 93vh; border: none;"
    ></iframe>
  `.replace(/\n/g, '') // Remove newlines to avoid breaking the srcdoc
}
