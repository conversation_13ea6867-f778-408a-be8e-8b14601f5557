import dayjs from 'dayjs'
// @ts-ignore
import { gql, request } from 'graphql-request'
import type {
  GetCryptoHistoryQuery,
  GetMetalHistoryQuery,
} from '~/src/generated'

const BASE_URL = process.env.NEXT_PUBLIC_GW_GQL_URI

interface FetcherArgs {
  symbol: string
  activeBarTime: number
  groupBy: any
}

const METALS_RANGE_QUERY = gql`
  query GetMetalHistory(
    $currency: String!
    $startTime: Int!
    $endTime: Int!
    $groupBy: String!
    $limit: Int!
    $offset: Int!
    $symbol: String!
  ) {
    GetMetalHistoryV3(
      currency: $currency
      startTime: $startTime
      endTime: $endTime
      groupBy: $groupBy
      limit: $limit
      offset: $offset
      symbol: $symbol
    ) {
      symbol
      results {
        timestamp
        high
        low
        open
        close
      }
    }
  }
`

function groupByProcessor(groupBy: string): string {
  switch (groupBy) {
    case '1D':
      return '1d'
    case '60':
      return '1h'
    case '120':
      return '2h'
    case '180':
      return '3h'
    case '240':
      return '4h'
    default:
      return `${groupBy}m`
  }
}

export const getMetalBars = async ({
  symbol,
  activeBarTime,
  groupBy,
}: FetcherArgs) => {
  const resolution = groupByProcessor(groupBy)

  const variables = {
    currency: 'USD',
    symbol,
    startTime: dayjs().subtract(1000, 'day').unix(),
    endTime: activeBarTime,
    groupBy: resolution,
    offset: 0,
    limit: 600,
  }

  return request(BASE_URL, METALS_RANGE_QUERY, variables).then(
    (data: GetMetalHistoryQuery) => {
      const results = data.GetMetalHistoryV3.results

      return results.map((x, idx: number) => {
        const lastBarIdx = results.length - 1
        const milliseconds = JSON.parse(`${x.timestamp}000`)

        return {
          time: milliseconds,
          close: x.close,
          open: x.open,
          high: x.high,
          low: x.low,
          volume: 0,
          isLastBar: lastBarIdx === idx,
          isBarClosed: lastBarIdx !== idx,
        }
      })
    },
  )
}

const CRYPTO_RANGE_QUERY = gql`
  query GetCryptoHistory(
    $symbol: String!
    $currency: String!
    $startTime: Int!
    $endTime: Int
    $groupBy: String!
    $limit: Int
    $offset: Int
  ) {
    GetCryptoHistoryV3(
      symbol: $symbol
      currency: $currency
      startTime: $startTime
      endTime: $endTime
      groupBy: $groupBy
      limit: $limit
      offset: $offset
    ) {
      results {
        high
        low
        open
        close
        timestamp
      }
    }
  }
`
export const getCryptoBars = async ({
  symbol,
  activeBarTime,
}: // groupBy,
FetcherArgs) => {
  // const resolution = groupByProcessor(groupBy)
  const variables = {
    currency: 'USD',
    symbol,
    startTime: dayjs().subtract(1000, 'day').unix(),
    endTime: activeBarTime,
    groupBy: 'day',
    offset: 0,
    limit: 600,
  }

  return request(BASE_URL, CRYPTO_RANGE_QUERY, variables).then(
    (data: GetCryptoHistoryQuery) => {
      const bars = data.GetCryptoHistoryV3[0].results.map((x, idx: number) => {
        const lastBarIdx = data.GetCryptoHistoryV3[0].results.length - 1
        const milliseconds = JSON.parse(`${x.timestamp}000`)

        return {
          time: milliseconds,
          close: x.close,
          open: x.open,
          high: x.high,
          low: x.low,
          volume: 0,
          isLastBar: lastBarIdx === idx,
          isBarClosed: lastBarIdx !== idx,
        }
      })

      return bars
    },
  )
}
