import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import type { FC } from 'react'
import { TeaserTextOnlyWithAuthor } from '~/src/components-news/ArticleTeasers/TeaserTextOnlyWithAuthor'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { GenericNewsListWithAuthor } from '~/src/components/generic-news-list/generic-news-list.component'
import { NewsCategoryTitle } from '~/src/components/news-category/news-category.component'
import type { Commentary, NewsTopContributorsQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import cs from '~/src/utils/cs'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'

const OpinionsLandingDesktop: FC<any> = () => {
  const { params, incrementParams } = useParams(10)
  const { data } = kitcoQuery(
    news.newsCommentaries({
      variables: { ...params },
      options: {
        enabled: true,
      },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.commentaries?.items,
    incrementParams,
    total: data?.commentaries?.total,
  })

  const fetched = items as Commentary[]

  const { data: contributors } = kitcoQuery(news.topContributors())

  // Fetch only the first one
  const heroOpinion = fetched?.[0]

  // Fetch the next 3
  const topSectionOpinions = fetched?.slice(1, 4)

  return (
    <LayoutNewsLanding title="Opinion">
      <ContentWrapper>
        <NewsCategoryTitle />
      </ContentWrapper>

      <div>
        <ContentWrapper>
          <FirstSectionDesktop
            heroOpinion={heroOpinion}
            topSectionOpinions={topSectionOpinions}
          />
        </ContentWrapper>

        <ContentWrapper>
          <TopContributors contributors={contributors} />
        </ContentWrapper>

        <ContentWrapper>
          <GenericNewsListWithAuthor data={fetched?.slice(4)} />
          <div ref={ref}>{loading && <div>Loading...</div>}</div>
        </ContentWrapper>
      </div>
    </LayoutNewsLanding>
  )
}

export default OpinionsLandingDesktop

const ContentWrapper: FC<{
  children: any
  className?: string
}> = ({ children, className }) => (
  <div className={cs([className, 'mx-auto w-[1240px]'])}>{children}</div>
)

const FirstSectionDesktop: FC<{
  heroOpinion: Commentary
  topSectionOpinions: Commentary[]
}> = ({ heroOpinion, topSectionOpinions }) => {
  return (
    <div className="flex flex-col border-b border-ktc-borders pb-10 md:pb-0 lg:flex-row lg:pb-10">
      <div className="w-full border-0 border-ktc-borders md:border-b md:pb-[40px] lg:w-[53.4%] lg:border-0 lg:pb-0 lg:pr-[40px]">
        <div className="mb-2 overflow-hidden">
          <Link href={heroOpinion?.urlAlias ?? '/'}>
            <ImageMS
              src={
                heroOpinion?.image?.detail?.default?.srcset ??
                heroOpinion?.legacyThumbnailImageUrl
              }
              hasLegacyThumbnailImageUrl={
                !!heroOpinion?.legacyThumbnailImageUrl
              }
              alt={`${heroOpinion?.title} teaser image`}
              priority={true}
              width={1202}
              height={676}
              service="icms"
              className={clsx(
                'w-full',
                'relative',
                'mb-2.5 aspect-video rounded-lg object-cover',
              )}
            />
          </Link>
        </div>
        <TeaserTextOnlyWithAuthor
          node={heroOpinion as any}
          size="xl"
          hideCategory={true}
          hideSummary={false}
        />
      </div>
      <div className="mt-10 flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-[calc(100%_-_53.4%_+_40px)] lg:border-l lg:pl-[40px]">
        <div className="flex flex-col gap-10">
          {topSectionOpinions.map((x) => (
            <TeaserTextOnlyWithAuthor
              node={x as any}
              size="md"
              hideCategory={true}
              hideSummary={false}
              key={x?.id}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

const TopContributors: FC<{ contributors: NewsTopContributorsQuery }> = ({
  contributors,
}) => {
  return (
    <>
      <h2 className="font-mulish my-5 text-[21px] uppercase leading-[27px]">
        Top Contributors
      </h2>

      <div className="flex gap-5 border-b border-b-ktc-borders pb-6">
        {!contributors ? (
          <h2>loading</h2>
        ) : (
          <>
            {contributors?.topContributors?.map((x) => (
              <Link
                className="flex w-full max-w-[120px] flex-col items-center"
                key={x.id}
                href={x.urlAlias ?? '#'}
              >
                <>
                  <div className="mx-auto text-center">
                    <Image
                      src={x.imageUrl ?? '/default-avatar.svg'}
                      alt={`Photo of ${x.name}`}
                      width={80}
                      height={80}
                      layout="fixed"
                      className="rounded-full"
                    />
                  </div>
                  <div className="mt-1 text-center text-sm font-normal italic leading-[18px] text-black">
                    {x.name}
                  </div>
                </>
              </Link>
            ))}
          </>
        )}
      </div>
    </>
  )
}
