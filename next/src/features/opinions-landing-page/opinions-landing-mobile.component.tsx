import clsx from 'clsx'
import Link from 'next/link'
import { type FC, Fragment } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserTextOnlyWithAuthor } from '~/src/components-news/ArticleTeasers/TeaserTextOnlyWithAuthor'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { NewsCategoryTitle } from '~/src/components/news-category/news-category.component'
import { NewsContentWrapper } from '~/src/components/news-content-wrapper/news-content-wrapper.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { Commentary } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'

export const OpinionsLandingMobile: FC<any> = () => {
  const { params, incrementParams } = useParams(10)
  const { data } = kitcoQuery(
    news.newsCommentaries({
      variables: { ...params },
      options: { enabled: true },
    }),
  )
  const { ref, items, loading } = useInfinite({
    items: data?.commentaries?.items,
    incrementParams,
    total: data?.commentaries?.total,
  })

  const fetched = items as Commentary[]

  // Fetch 4 element
  const topOpinions = fetched?.slice(0, 4)

  // Fetch the rest Todo: get more results from the API
  const allOpinions = fetched?.slice(4)

  return (
    <LayoutNewsLanding title="Opinion">
      <NewsContentWrapper>
        <NewsCategoryTitle />
      </NewsContentWrapper>

      <NewsContentWrapper>
        <Spacer className="h-2.5" />
        <FirstSectionMobile topOpinions={topOpinions} />
      </NewsContentWrapper>
      <NewsContentWrapper>
        <SecondSectionMobile
          allOpinions={allOpinions}
          refScroll={ref}
          loading={loading}
        />
      </NewsContentWrapper>
    </LayoutNewsLanding>
  )
}

const FirstSectionMobile: FC<{ topOpinions: Commentary[] }> = ({
  topOpinions,
}) => {
  return (
    <div className="flex flex-col border-t border-t-ktc-borders pt-5">
      <div className="flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-1/2 lg:border-l lg:pl-[40px]">
        <div className="block">
          {topOpinions.map((x) => (
            <div
              className="mb-5 flex gap-5 border-b border-b-ktc-borders pb-5"
              key={x.id}
            >
              <div className="w-[120px] flex-initial">
                <Link href={x?.urlAlias ?? '/'}>
                  <ImageMS
                    src={
                      x?.image?.detail?.default?.srcset ??
                      x?.legacyThumbnailImageUrl
                    }
                    hasLegacyThumbnailImageUrl={!!x?.legacyThumbnailImageUrl}
                    alt={`${x?.title} teaser image`}
                    priority={true}
                    width={400}
                    height={340}
                    service="icms"
                    className={clsx(
                      'w-full',
                      'relative',
                      'mb-2.5 aspect-[4/3] rounded-lg',
                    )}
                  />
                </Link>
              </div>
              <div className="w-[calc(100%_-_140px)] flex-initial">
                <TeaserTextOnlyWithAuthor
                  node={x as any}
                  size="sm"
                  hideCategory={true}
                  hideSummary={true}
                  classTitle="mt-[-3px]"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
      <AdvertisingSlot
        id={'banner-0'}
        className={'mx-auto h-[250px] w-[300px] bg-red-400 no-print'}
        viewportsEnabled={{
          mobile: true,
          tablet: false,
          desktop: false,
        }}
      />
    </div>
  )
}

const SecondSectionMobile: FC<{
  allOpinions: Commentary[]
  refScroll: (node?: Element) => void
  loading: boolean
}> = ({ allOpinions, refScroll, loading }) => {
  let adCounter = 1

  function advertInjector(idx: number) {
    if (idx === 4 || ((idx - 4) % 5 === 0 && idx !== 0)) {
      adCounter++
      return true
    }
  }

  return (
    <div className="flex flex-col pt-5">
      {!allOpinions ? (
        <h2>loading</h2>
      ) : (
        <>
          {allOpinions.map((x, idx) => (
            <Fragment key={idx}>
              <div
                className="mb-5 flex gap-5 border-t border-t-ktc-borders pt-5"
                key={x.id}
              >
                <div className="w-[120px] flex-initial">
                  <Link href={x?.urlAlias ?? '/'}>
                    <ImageMS
                      src={
                        x?.image?.detail?.default?.srcset ??
                        x?.legacyThumbnailImageUrl
                      }
                      hasLegacyThumbnailImageUrl={!!x?.legacyThumbnailImageUrl}
                      alt={`${x?.title} teaser image`}
                      priority={true}
                      width={400}
                      height={340}
                      service="icms"
                      className={clsx(
                        'w-full',
                        'relative',
                        'mb-2.5 aspect-[4/3] rounded-lg',
                      )}
                    />
                  </Link>
                </div>
                <div className="w-[calc(100%_-_140px)] flex-initial">
                  <TeaserTextOnlyWithAuthor
                    node={x as any}
                    size="sm"
                    hideCategory={true}
                    hideSummary={true}
                    classTitle="mt-[-3px]"
                  />
                </div>
              </div>
              {advertInjector(idx) && (
                <AdvertisingSlot
                  id={`banner-${adCounter}`}
                  className={'mx-auto mb-5 h-[250px] w-[300px] bg-red-400'}
                  viewportsEnabled={{
                    mobile: true,
                    tablet: false,
                    desktop: false,
                  }}
                />
              )}
            </Fragment>
          ))}
        </>
      )}
      <div ref={refScroll}>{loading && <div>Loading...</div>}</div>
    </div>
  )
}
