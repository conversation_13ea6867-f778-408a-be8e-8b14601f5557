import { demandConfig, sizeDefaults } from './demandConfig'

export const rightRailArticle3_2 = {
  id: 'right-rail-8',
  path: '/21841313772,22554256/kitco/right_rail_two',
  sizes: sizeDefaults.mediumRectangle,
  sizeMappingName: 'rightRailMd',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            { minViewPort: [0, 0], sizes: [[300, 250]] },
            { minViewPort: [768, 0], sizes: [[300, 250]] },
            {
              minViewPort: [1270, 0],
              sizes: [
                [300, 250],
                [300, 600],
              ],
            },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091241',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091242',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'criteo',
          params: {
            networkId: demandConfig.criteo.networkId,
          },
        },
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1802106',
          },
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '756857',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '1200271',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560691',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'p7dvw4qXVgdFmobZh0iVrAIg',
          },
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '976284508',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: '5cde3e0066834a1e7ab0',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'minutemedia',
          params: {
            org: demandConfig.minutemedia.org,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'nativo',
          params: {
            placementId: 1467945,
          },
        },
      ],
    },
  ],
}
