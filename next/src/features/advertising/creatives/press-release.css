.wrapper {
  text-decoration: none;
  color: unset;
}

.pr-release-n {
  display: flex;
}

.section-one {
  flex: 0 0 150px;
}

.section-two {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  flex: 1 1 auto;
}

.logo {
  display: block;
  width: 100%;
}

.pr-text {
  flex: 0 1 100%;
  padding: 0 5px;
  text-align: center;
  font-weight: bold;
}

.stock {
  display: none;
}

.cta-button {
  padding: 2px 10px;
  border-radius: 3px;
  background: rgb(34 197 94);
  color: white;
  text-decoration: none;
}

@media (min-width: 730px) {
  .stock {
    display: block;
    flex: 0 1 auto;
    color: rgb(34 197 94);
  }

  .cta-button {
    flex: 0 1 auto;
  }

  .section-two {
    justify-content: space-around;
  }

  .pr-text {
    flex: 0 1 50%;
  }
}
