import { type ZxcvbnResult, zxcvbn, zxcvbnOptions } from '@zxcvbn-ts/core'
import * as zxcvbnCommonPackage from '@zxcvbn-ts/language-common'
import * as zxcvbnEnPackage from '@zxcvbn-ts/language-en'
import validator from 'validator'
import { sanitizeEmail } from '~/src/features/auth/sanitize'
import {
  checkUsernameExists,
  checkUsernameLinkedToEmail,
} from '~/src/features/auth/username'
import {
  checkUsernameExists as checkUsernameExistsInDiscourse,
  getUserByEmail,
} from '~/src/services/discourse/api'

/**
 * Validator class for validating user input
 */

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
class Validator {
  // Set up zxcvbn options
  static zxcvbnSettings = {
    translations: zxcvbnEnPackage.translations,
    graphs: zxcvbnCommonPackage.adjacencyGraphs,
    dictionary: {
      ...zxcvbnCommonPackage.dictionary,
      ...zxcvbnEnPackage.dictionary,
    },
  }

  /**
   * Validate display name to contain only letters and spaces, ensuring proper
   * names like "Test User" or "John Doe".
   *
   * @param displayName The display name to validate.
   * @returns true if the display name is valid, false otherwise.
   */
  static validateDisplayName(displayName: string) {
    if (!displayName) return false

    // Only letters and spaces are allowed in display names
    const validPattern = /^[a-zA-Z0-9-._\s]+$/

    return (
      validator.isLength(displayName, { min: 3, max: 60 }) &&
      validPattern.test(displayName)
    )
  }

  /**
   * Validate username
   *
   * @param username
   */
  static validateUsername(username: string) {
    if (!username) return false

    // Allow only alphanumeric characters, underscores and hyphens
    const validPattern = /^[a-zA-Z0-9-._]+$/

    return (
      validator.isLength(username, { min: 3, max: 60 }) &&
      validPattern.test(username)
    )
  }

  /**
   * Check if username exists
   *
   * @param username
   * @param email
   */
  static async usernameExists(username: string, email: string = null) {
    const existInDB = await checkUsernameExists(username)

    if (existInDB) {
      if (email) {
        const isLinked = await checkUsernameLinkedToEmail(username, email)
        // If the username is linked to the email, return false
        // Username exist but the user can use it
        if (isLinked) {
          return false
        }
      }

      return true
    }

    if (email) {
      const emailExistInDiscourse = await getUserByEmail(email)

      // If the email exists in Discourse and the username is the same, return false
      if (
        emailExistInDiscourse &&
        emailExistInDiscourse.username === username
      ) {
        return false
      }
    }

    // Only check if the username exists in Discourse
    return await checkUsernameExistsInDiscourse(username)
  }

  /**
   * Validate email
   *
   * @param email
   */
  static validateEmail(email: string) {
    return validator.isEmail(sanitizeEmail(email))
  }

  /**
   * Validate password - This function can also check the strength of the password
   *
   * @param password - The password to validate
   * @param checkStrength - Whether to check the strength of the password
   */
  static validatePassword(
    password: string,
    checkStrength = false,
  ): {
    errors: string[]
    passwordStrength: ZxcvbnResult
  } {
    // Array to hold errors
    const errors = []

    // Check if password is at least 6 characters long
    if (!validator.isLength(password, { min: 6 })) {
      errors.push('Password must be at least 6 characters long.')
    }

    // Password strength
    let passwordStrength: ZxcvbnResult

    // Check password strength
    if (checkStrength) {
      zxcvbnOptions.setOptions(Validator.zxcvbnSettings)

      passwordStrength = zxcvbn(password)
    }

    return {
      errors: errors,
      passwordStrength: passwordStrength,
    }
  }

  /**
   * Check if passwords match
   *
   * @param password
   * @param confirmPassword
   */
  static passwordsMatch(password: string, confirmPassword: string) {
    return password === confirmPassword
  }
}

export default Validator
