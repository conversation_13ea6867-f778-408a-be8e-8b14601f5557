import type { UserData } from '~/src/components/Auth/Types/UserData'

/**
 * Sanitizes the display name by converting it to a clean format with only letters and spaces.
 * It also ensures that it removes extra spaces and does not allow multiple consecutive spaces.
 * @param displayName - The display name to sanitize.
 * @returns The sanitized display name.
 */
export function sanitizeDisplayName(displayName: string): string {
  if (!displayName) return displayName

  // Remove any character that is not a letter or space
  let sanitized = displayName.replace(/[^a-zA-Z0-9-._\s]/g, '')

  // Replace multiple spaces with a single space
  sanitized = sanitized.replace(/\s+/g, ' ')

  return sanitized
}

/**
 * Sanitizes the email by converting it to lowercase and encoding special characters.
 *
 * RFC 5322 compliant
 *
 * @param email - The email to sanitize.
 * @returns The sanitized email.
 */
export function sanitizeEmail(email: string): string {
  // Return early if the email is empty
  if (!email) return email

  // Convert email to lowercase to ensure uniformity
  let sanitized = email.toLowerCase().trim()

  // Encode characters that are not allowed in email addresses but are common in user input
  sanitized = encodeURIComponent(sanitized)

  // Decode back the allowed special characters which are: @ . _ - ! # $ % & ' * + / = ? ^ ` { | } ~
  sanitized = sanitized
    .replace(/%40/g, '@')
    .replace(/%2E/g, '.')
    .replace(/%5F/g, '_')
    .replace(/%2D/g, '-')
    .replace(/%21/g, '!')
    .replace(/%23/g, '#')
    .replace(/%24/g, '$')
    .replace(/%25/g, '%')
    .replace(/%26/g, '&')
    .replace(/%27/g, "'")
    .replace(/%2A/g, '*')
    .replace(/%2B/g, '+')
    .replace(/%2F/g, '/')
    .replace(/%3D/g, '=')
    .replace(/%3F/g, '?')
    .replace(/%5E/g, '^')
    .replace(/%60/g, '`')
    .replace(/%7B/g, '{')
    .replace(/%7C/g, '|')
    .replace(/%7D/g, '}')
    .replace(/%7E/g, '~')

  return sanitized
}

/**
 * Sanitizes the username by converting it to lowercase and removing unwanted characters.
 * @param username - The username to sanitize.
 * @returns An object containing the sanitized username in both original case and lowercase.
 */
export function sanitizeUsername(username: string): {
  original: string
  lowercase: string
} {
  // Only allow alphanumeric characters, dashes, dots, and underscores
  const validPattern = /[^a-zA-Z0-9-._]/g

  // Return early if the username is empty
  if (!username) return { original: username, lowercase: username }

  // Convert username to lowercase to ensure uniformity
  const lowercaseUsername = username.toLowerCase().trim()

  // Remove characters that do not match the pattern
  const sanitizedOriginal = username.replace(validPattern, '')
  const sanitizedLowercase = lowercaseUsername.replace(validPattern, '')

  return {
    original: sanitizedOriginal,
    lowercase: sanitizedLowercase,
  }
}

/**
 * Sanitizes the user data before saving it to the database.
 *
 * @param userData
 */
export function sanitizeUserData(userData: UserData): UserData {
  const { original, lowercase } = sanitizeUsername(
    userData.displayUsername ?? userData.username,
  )
  return {
    ...userData,
    username: lowercase,
    displayUsername: original,
  }
}
