import {
  FacebookAuthProvider,
  GoogleAuthProvider,
  signInWithPopup,
} from '@firebase/auth'
import type { UserCredential } from 'firebase/auth'
import { auth } from '~/src/services/firebase/config'
import { login } from '~/src/services/firebase/service'

class SignIn {
  /**
   * Sign in with Google
   *
   * @returns {Promise<boolean>}
   */
  static async signInWithGoogle(): Promise<UserCredential> {
    const provider = new GoogleAuthProvider()

    try {
      return await signInWithPopup(auth, provider)
    } catch (error) {
      console.error('Error trying to sign in with Google:', error)
      return null
    }
  }

  /**
   * Sign in with Facebook
   *
   * @returns {Promise<boolean>}
   */
  static async signInWithFacebook(): Promise<UserCredential> {
    const provider = new FacebookAuthProvider()

    try {
      return await signInWithPopup(auth, provider)
    } catch (error) {
      console.error('Error trying to sign in with Facebook:', error)
      return null
    }
  }

  /**
   * Sign in with email and password
   *
   * @param email string
   * @param password string
   * @returns {Promise<boolean>}
   */
  static async signInWithEmail(
    email: string,
    password: string,
  ): Promise<UserCredential> {
    return await login(email, password)
  }
}

export default SignIn
