/**
 * Check if a username exists
 *
 * @param username
 */
export async function checkUsernameExists(username: string): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/usernameExist', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username }),
    })

    if (!response.ok) {
      throw new Error('Error checking username existence')
    }

    const data = await response.json()

    return data?.exists === true
  } catch (error) {
    console.error('Failed to check username existence:', error)
    return false
  }
}

/**
 * Check if a username is linked to an email
 *
 * @param username
 * @param email
 */
export async function checkUsernameLinkedToEmail(
  username: string,
  email: string,
): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/usernameLinkedToEmail', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, email }),
    })

    if (!response.ok) {
      throw new Error('Error checking username existence')
    }

    return true
  } catch (error) {
    console.error('Failed to check username existence:', error)
    return false
  }
}
