import Link from 'next/link'
import { type FC, useCallback } from 'react'
import { TeaserCardForCategorySection } from '~/src/components-news/ArticleTeasers/TeaserCardForCategorySection'
import { TeaserTextOnlyNewPage } from '~/src/components-news/ArticleTeasers/TeaserTextOnlyNewPage'
import { ArticleMoreButtonNewsPages } from '~/src/components/article-more-button/article-more-button.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { NewsByCategoryGenericQuery } from '~/src/generated'
import type { NewsCategoriesValues } from '~/src/lib/news-categories.lib'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import useScreenSize from '~/src/utils/useScreenSize'

export const CategorySection: FC<{
  title: string
  urlAlias: NewsCategoriesValues
  offset?: number
}> = ({ title, urlAlias, offset = 0 }) => {
  const { isMobile } = useScreenSize()
  const { data } = kitcoQuery(
    news.newsByCategoryGeneric({
      variables: {
        limit: 3,
        offset: offset,
        urlAlias,
        includeRelatedCategories: false,
        includeEntityQueues: false,
      },
      options: {
        enabled: true,
        select: useCallback((d: NewsByCategoryGenericQuery) => {
          const short = d?.nodeListByCategory
          return {
            nodeListByCategory: {
              ...short,
              items: short?.items?.filter(
                (x) => x?.__typename !== 'Commentary',
              ),
            },
          }
        }, []),
      },
    }),
  )
  return (
    <div>
      <h2>
        <HeaderSectionCategory title={title} urlAlias={urlAlias} />
      </h2>
      <div className="divide-y divide-ktc-borders">
        {data?.nodeListByCategory?.items?.map((x: any, idx: number) => (
          <div className="py-[18px] last:pb-0 md:relative" key={x.id}>
            {idx === 0 ? (
              <TeaserCardForCategorySection
                size="md"
                node={x}
                key={x.id}
                hideCategory={true}
              />
            ) : (
              <TeaserTextOnlyNewPage
                size="md"
                node={x}
                key={x.id}
                hideCategory={true}
                hideSummary={true}
              />
            )}
          </div>
        ))}
      </div>
      {isMobile && (
        <ArticleMoreButtonNewsPages label="See More" href={urlAlias} />
      )}
      <Spacer className="h-10" />
    </div>
  )
}

const HeaderSectionCategory = ({ title, urlAlias }) => {
  return (
    <Link href={urlAlias}>
      <span className="mt-[-5px] inline-block text-[21px] text-xl font-bold uppercase leading-[27px] text-[#373737] hover:underline">
        {title}
      </span>
    </Link>
  )
}
