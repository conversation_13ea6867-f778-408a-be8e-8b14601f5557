import { createXMLSitemap } from '~/src/features/sitemaps/generator'
import { fetchArticles, fetchVideos } from '~/src/features/sitemaps/queries'
import { saveFileToGCS } from '~/src/services/google-cloud/storage'

/**
 * Create and store a sitemap file in Google Cloud Storage.
 *
 * @param type
 * @param filename
 * @constructor
 */
export async function StoreSitemap(
  type: string,
  filename: string,
): Promise<boolean> {
  try {
    // Generate the sitemap XML content.
    const xmlContent = createXMLSitemap(
      type === 'video' ? await fetchVideos() : await fetchArticles(type),
      type,
    )

    // Save the sitemap to Google Cloud Storage.
    await saveFileToGCS(
      xmlContent,
      `sitemaps/${filename}`,
      'application/xml',
      true,
    )

    return true
  } catch (error) {
    console.error('Failed to generate or upload sitemap:', error)
    return false
  }
}
