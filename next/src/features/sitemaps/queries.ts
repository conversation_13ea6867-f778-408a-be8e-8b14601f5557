import type {
  Author,
  BasicPage,
  Category,
  Commentary,
  LeadGen,
  NewsArticle,
  OffTheWire,
  Sponsored,
  Tag,
  VideoSnippet,
} from '~/src/generated'
import { sitemap } from '~/src/lib/sitemap'
import { kitcoQueryClient } from '~/src/services/database/kitcoQuery'
import { ssrQueries } from '~/src/utils/ssr-wrappers'

const baseURL = process.env.NEXT_PUBLIC_URL

/**
 * Generate a URL object with the given base URL and path.
 *
 * @param path
 * @param date
 */
export function generateUrlObject(
  path: string,
  date: string | number = null,
): { loc: string; lastmod: string } {
  if (!path) {
    return null
  }

  let parsedDate = null

  try {
    if (date) {
      if (typeof date === 'string') {
        if (!Number.isNaN(Number(date))) {
          // Convert the string that contains a number to a number
          parsedDate = new Date(Number(date)).toISOString()
        } else {
          // If it's a string in ISO format
          parsedDate = new Date(date).toISOString()
        }
      }

      if (typeof date === 'number') {
        // Check if it is a timestamp in milliseconds
        if (date > 9999999999) {
          parsedDate = new Date(date).toISOString()
        } else {
          // It's a timestamp in seconds, convert to milliseconds
          parsedDate = new Date(date * 1000).toISOString()
        }
      }
    }
  } catch (error) {
    console.error('Failed to parse date:', date, error)
  }

  return {
    loc: `${baseURL}${path}`,
    lastmod: parsedDate,
  }
}

/**
 * Extract URLs and other sitemap data from the query data based on the query key.
 *
 * @param queryData
 * @param queryKey
 */
export function extractDataFromQuery(queryData, queryKey: string) {
  switch (queryKey) {
    case 'Sitemap.GetVideoSnippetByUuid':
      return {
        description: queryData?.VideoGetSnippet.description,
      }
    case 'Sitemap.GetAllVideos':
      return queryData?.VideoLatestVideos?.snippets?.map(
        (item: VideoSnippet) => {
          const url = generateUrlObject(
            item?.frontendPath,
            item?.video?.createdAt,
          )
          return {
            duration: item?.video?.duration,
            headline: item?.headline,
            tags: item?.video?.tagsPlainText,
            thumbnailUuid: item?.thumbnailUuid,
            uuid: item?.uuid,
            videoUuid: item?.video?.uuid,
            ...url,
          }
        },
      )
    case 'Sitemap.GetAllNewsArticles':
    case 'Sitemap.GetAllOffTheWire':
    case 'Sitemap.GetAllOpinions':
      return queryData?.nodeList?.items?.map(
        (
          item:
            | BasicPage
            | Commentary
            | LeadGen
            | NewsArticle
            | OffTheWire
            | Sponsored,
        ) => {
          return {
            title: item?.title,
            ...generateUrlObject(item?.urlAlias, item?.updatedAt),
          }
        },
      )
    case 'Sitemap.GetAllAuthors': {
      return queryData?.reporters?.map((item: Author) =>
        item?.hidden === false ? generateUrlObject(item?.urlAlias) : null,
      )
    }
    case 'Sitemap.GetAllTags': {
      return queryData?.trendingTags?.map((item: Tag) =>
        generateUrlObject(item?.urlAlias),
      )
    }
    case 'Sitemap.GetAllLeadGen': {
      return queryData?.queue?.items?.map(
        (
          item:
            | BasicPage
            | Commentary
            | LeadGen
            | NewsArticle
            | OffTheWire
            | Sponsored,
        ) => generateUrlObject(item?.urlAlias, item?.updatedAt),
      )
    }
    case 'Sitemap.GetAllCategories': {
      const urls = []
      const extractUrls = (categories: Category[]) => {
        for (const category of categories) {
          if (category?.urlAlias) {
            urls.push(generateUrlObject(category.urlAlias))
          }
          if (category?.children) {
            extractUrls(category.children)
          }
        }
      }
      extractUrls(queryData.categoriesTree)
      return urls
    }
    default:
      return []
  }
}

/**
 * Fetch all news articles based on the type and pagination.
 *
 * @param type
 * @param limit
 * @param offset
 */
export async function getAllNewsArticles(
  limit: number,
  offset: number,
  type: string,
) {
  // Create a new query client for each request
  const queryClient = kitcoQueryClient()

  // Variable to store the total number of results
  let total = 0

  // Fetch the articles
  const { dehydratedState } = await ssrQueries({
    queries: [sitemap[type]({ variables: { limit, offset } })],
    queryClient,
  })

  // Process each query result and append their URLs
  const items = dehydratedState.queries.flatMap((query) => {
    total = query?.state?.data?.nodeList?.items?.length
    return extractDataFromQuery(query.state.data, query.queryKey[0])
  })

  return {
    items,
    total,
  }
}

/**
 * Fetch all videos based on the pagination.
 *
 * @param limit
 * @param offset
 */
export async function getAllVideos(limit: number, offset: number) {
  // Create a new query client for each request
  const queryClient = kitcoQueryClient()

  // Variable to store the total number of results
  let total = 0

  const { dehydratedState } = await ssrQueries({
    queries: [sitemap.getAllVideos({ variables: { limit, offset } })],
    queryClient,
  })

  // Process each query result and append their URLs
  const items = dehydratedState.queries.flatMap((query) => {
    total = query?.state?.data?.VideoLatestVideos?.snippets?.length
    return extractDataFromQuery(query.state.data, query.queryKey[0])
  })

  return {
    items,
    total,
  }
}

/**
 * Fetch additional details for a video.
 *
 * @param {string} uuid
 */
export async function getVideoSnippet(uuid: string) {
  const queryClient = kitcoQueryClient()

  const { dehydratedState } = await ssrQueries({
    queries: [sitemap.getVideoSnippetByUuid({ variables: { uuid } })],
    queryClient,
  })

  return dehydratedState.queries.flatMap((query) => {
    return extractDataFromQuery(query.state.data, query.queryKey[0])
  })
}

/**
 * Filter items, checking for duplicates and empty locs.
 *
 * @param items
 */
function filterItems(items) {
  const seenLocs = new Set()

  return items.filter((item) => {
    if (!item?.loc) {
      return false
    }
    if (seenLocs.has(item.loc)) {
      return false
    }
    seenLocs.add(item.loc)
    return true
  })
}

/**
 * Generic function to fetch data and process it with pagination.
 *
 * @param fetchFunction
 * @param processItem
 * @param {number} initialLimit
 * @param id
 */
async function fetchData(
  fetchFunction,
  processItem,
  initialLimit: number,
  id = null,
) {
  // Pagination limit
  const limit = initialLimit

  // Pagination offset
  let offset = 0

  // Variable to store all items
  const allItems = []

  // Variable to control the loop
  let continueFetching = true

  // Variable to store the number of tries
  let tries = 0
  const maxTries = 5

  while (continueFetching) {
    // Fetch the data for the sitemap
    const { items, total } = await fetchFunction(limit, offset)

    // Process each item if needed
    if (items && items.length > 0) {
      for (const item of items) {
        const processed = await processItem(item)

        if (processed) {
          allItems.push(await processItem(item))
        }
      }
    }

    // If the total number of results is greater than 0, continue fetching
    if (total > 0) {
      offset += limit
      tries = 0
    } else {
      // If the total number of results is 0, try again
      if (tries < maxTries) {
        tries++
      } else {
        continueFetching = false
      }
    }

    console.log(
      'Processing Sitemap Fetching Data · Limit:',
      limit,
      'Offset:',
      offset,
      'Last iteration items:',
      total,
      'Total Items:',
      allItems.length,
      'Tries:',
      tries,
      'Processing ID:',
      id,
    )
  }

  return filterItems(allItems)
}

/**
 * Fetch all articles of a given type.
 *
 * @param type
 */
export async function fetchArticles(type: string) {
  return fetchData(
    (limit: number, offset: number) => getAllNewsArticles(limit, offset, type),
    (article) => article,
    100,
    type,
  )
}

/**
 * Fetch all videos.
 */
export async function fetchVideos() {
  return fetchData(
    (limit: number, offset: number) => getAllVideos(limit, offset),
    async (video) => {
      // Get video details
      const videoDetails = await getVideoSnippet(video?.uuid)

      return { ...video, ...videoDetails[0] }
    },
    49,
    'videos',
  )
}
