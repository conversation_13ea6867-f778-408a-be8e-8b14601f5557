import { encode as xmlEncode } from 'html-entities'
import { videoPoster } from '~/src/utils/videoImageString'

/**
 * Encodes URLs to ensure they are compliant with XML and URL standards.
 * @param {string} url - The URL to encode.
 */
function encodeURL(url: string) {
  try {
    // Create a new URL object which automatically handles some encoding.
    const urlObject = new URL(url)
    // Encode path segments individually to avoid encoding slashes and colons.
    urlObject.pathname = urlObject.pathname
      .split('/')
      .map((part) =>
        encodeURIComponent(part).replace(
          /[!'()*]/g,
          (c) => `%${c.charCodeAt(0).toString(16)}`,
        ),
      )
      .join('/')
    // Reconstruct the URL with the proper encoding only for pathname.
    return urlObject.toString()
  } catch {
    return url // Return the original URL if it fails to parse.
  }
}

/**
 * Escapes XML special characters in a string.
 * @param {string} str - The string to escape.
 */
function escapeXML(str: string) {
  return xmlEncode(str)
}

/**
 * Generate a sitemap XML from a list of URLs.
 *
 * @param items
 * @param {string} type
 */
export function createXMLSitemap(items: any[], type = 'default') {
  // Generate specific sitemap type for videos
  if (type === 'video') {
    return generateVideoSitemap(items)
  }

  // Generate specific sitemap type for news
  if (
    type === 'getAllNewsArticles' ||
    type === 'getAllOffTheWire' ||
    type === 'getAllOpinions'
  ) {
    return generateNewsSitemap(items)
  }

  // Generate generic site map
  return generateGenericSitemap(items)
}

/**
 * Generate a video sitemap XML from a list of video URLs.
 *
 * @param items
 */
function generateVideoSitemap(items) {
  return `<?xml version="1.0" encoding="UTF-8"?>
		<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
    				xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
    ${items
      .map((item) => {
        const videoURL = item?.videoUuid
          ? encodeURL(
              `${process.env.NEXT_PUBLIC_VCMS_BUCKET}/${item?.videoUuid}/video.mp4`,
            )
          : ''

        const videoThumb = encodeURL(
          videoPoster(item?.uuid, item?.thumbnailUuid),
        )

        let videoTags = ''

        if (item?.tags) {
          videoTags = item.tags
            .map((tag: string) => {
              return `					<video:tag><![CDATA[${escapeXML(tag)}]]></video:tag>\n`
            })
            .join('')
        }

        return `
      <url>
        <loc>${encodeURL(item.loc)}</loc>
				<video:video>
					<video:thumbnail_loc>${videoThumb}</video:thumbnail_loc>
          <video:title><![CDATA[${escapeXML(item?.headline)}]]></video:title>
          <video:description><![CDATA[${escapeXML(item?.description)}]]></video:description>
					<video:content_loc>${videoURL}</video:content_loc>
					<video:duration>${item?.duration}</video:duration>
					<video:publication_date>${item?.lastmod}</video:publication_date>
					<video:requires_subscription>no</video:requires_subscription>
					${videoTags}
				</video:video>
      </url>`
      })
      .join('')}
		</urlset>`
}

/**
 * Generate a news sitemap XML from a list of news URLs.
 *
 * @param items
 */
function generateNewsSitemap(items) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
    xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
    ${items
      .map(
        (item) => `
      <url>
        <loc>${encodeURL(item.loc)}</loc>
        <news:news>
          <news:publication>
            <news:name>KITCO</news:name>
            <news:language>en</news:language>
          </news:publication>
          <news:publication_date>${item?.lastmod}</news:publication_date>
          <news:title><![CDATA[${escapeXML(item?.title)}]]></news:title>
        </news:news>
      </url>
    `,
      )
      .join('')}
		</urlset>`
}

/**
 * Generate a generic sitemap XML from a list of URLs.
 *
 * @param items
 */
function generateGenericSitemap(items) {
  return `<?xml version="1.0" encoding="UTF-8"?>
  <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${items
      .map((item) => {
        const lastMod = item?.lastmod
          ? `<lastmod>${item.lastmod}</lastmod>`
          : null

        return `
      <url>
        <loc>${encodeURL(item.loc)}</loc>
        ${lastMod}
      </url>
    `
      })
      .join('')}
  </urlset>`
}
