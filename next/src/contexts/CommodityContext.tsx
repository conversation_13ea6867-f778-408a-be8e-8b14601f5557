import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react'
import type CommodityData from '~/src/types/DataTable/CommodityData'

interface CommodityContextType {
  selectedCommodity: CommodityData | null
  setSelectedCommodity: (commodity: CommodityData) => void
  rotateToNextCommodity: () => void
  commodities: CommodityData[]
  isAutoRotating: boolean
  toggleAutoRotate: () => void
}

const CommodityContext = createContext<CommodityContextType | undefined>(
  undefined,
)

export const CommodityProvider = ({
  children,
  initialCommodities = [],
}: {
  children: ReactNode
  initialCommodities: CommodityData[]
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isAutoRotating, setIsAutoRotating] = useState(true)
  const [commodities] = useState<CommodityData[]>(initialCommodities)
  const [rotationInterval, setRotationInterval] =
    useState<NodeJS.Timeout | null>(null)

  // Auto-rotation effect
  useEffect(() => {
    if (isAutoRotating) {
      const interval = setInterval(() => {
        rotateToNextCommodity()
      }, 10000) // Rotate every 10 seconds

      setRotationInterval(interval)
      return () => {
        if (interval) clearInterval(interval)
      }
    } else if (rotationInterval) {
      clearInterval(rotationInterval)
      setRotationInterval(null)
    }
  }, [isAutoRotating, commodities.length])

  const setSelectedCommodity = (commodity: CommodityData) => {
    const index = commodities.findIndex(
      (c) => c.commodity === commodity.commodity,
    )
    if (index >= 0) {
      setSelectedIndex(index)
      // Pause auto-rotation temporarily when user selects a commodity
      setIsAutoRotating(false)

      // Resume auto-rotation after a delay
      setTimeout(() => {
        setIsAutoRotating(true)
      }, 30000) // Resume after 30 seconds
    }
  }

  const rotateToNextCommodity = () => {
    setSelectedIndex((prevIndex) => (prevIndex + 1) % commodities.length)
  }

  const toggleAutoRotate = () => {
    setIsAutoRotating((prev) => !prev)
  }

  return (
    <CommodityContext.Provider
      value={{
        selectedCommodity: commodities[selectedIndex] || null,
        setSelectedCommodity,
        rotateToNextCommodity,
        commodities,
        isAutoRotating,
        toggleAutoRotate,
      }}
    >
      {children}
    </CommodityContext.Provider>
  )
}

export const useCommodity = () => {
  const context = useContext(CommodityContext)
  if (context === undefined) {
    throw new Error('useCommodity must be used within a CommodityProvider')
  }
  return context
}
