import { useEffect, useState } from 'react'
import type { BarchartsFuturesByExchangeQuery } from '~/src/generated'
import { markets } from '~/src/lib/markets-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

function dataProcessor(cme: BarchartsFuturesByExchangeQuery) {
  let data = []

  if (cme) {
    const hashMap = {}
    const results = cme?.GetBarchartFuturesByExchange?.results

    for (let i = 0; i < results.length; i++) {
      if (!Object.prototype.hasOwnProperty.call(hashMap, results[i].name)) {
        hashMap[results[i].name] = {
          ...results[i],
          exchange: 'CME',
          category: 'Softs',
        }
      }
    }

    Object.keys(hashMap).map((x) => {
      if (x === 'Lumber') {
        data = [...data, hashMap['Lumber']]
      }
    })
  }
  return data
}

const useFuturesSofts = () => {
  const { data: cme } = kitcoQuery(
    markets.futuresByExchange({
      variables: {
        category: 'Softs',
        exchange: 'CME',
      },
    }),
  )

  const [isLoading, setIsLoading] = useState(true)
  const [processedData, setProcessedData] = useState<any>([])

  useEffect(() => {
    const processData = async () => {
      if (cme) {
        const processedResult = await dataProcessor(cme)
        setProcessedData(processedResult)
        setIsLoading(false)
      }
    }

    processData()
  }, [cme])

  return [processedData, isLoading]
}

export default useFuturesSofts
