import type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import type {
  LatestNewsSidebarQuery,
  NewsByCategoryGenericQuery,
} from '~/src/generated'
import { newsSidebar } from '~/src/lib/NewsSidebar/Queries'
import kitcoQuery from '~/src/services/database/kitcoQuery'

export interface NormalizedArticle {
  id: number
  title: string
  urlAlias?: string | null
  updatedAt?: string | null
  legacyThumbnailImageUrl?: string | null
  category?: {
    name: string
    urlAlias: string
  } | null
  image?: string | null
}

/**
 * Fetch the latest news for sidebar
 *
 * @returns {LatestNewsSidebarQuery}
 * @param limit
 * @param category
 */
const useLatestNewsSidebar = (
  limit = 10,
  category = null,
): NormalizedArticle[] => {
  const [cachedData, setCachedData] = useState<NormalizedArticle[]>([])

  const { data } = getFromDB(limit, category)

  useEffect(() => {
    if (data && JSON.stringify(data) !== JSON.stringify(cachedData)) {
      setCachedData(normalizeData(data))
    }
  }, [data])

  return cachedData
}

/**
 * Get the query for fetching the latest news
 *
 * @param limit
 * @param category
 */
export const getQuery = (
  limit: number,
  category: string | null,
): UseQueryOptions<LatestNewsSidebarQuery | NewsByCategoryGenericQuery> => {
  // If category is provided, fetch news by category
  if (category) {
    return newsSidebar.latestNewsSidebarByCategory({
      variables: {
        urlAlias: `/news/category/${category}`,
        limit,
        offset: 0,
        includeRelatedCategories: true,
      },
      options: {
        enabled: true,
      },
    })
  }

  return newsSidebar.latestNewsSidebar({
    variables: {
      limit,
      offset: 0,
    },
    options: { enabled: true },
  })
}

/**
 * Fetch latest news from the database
 *
 * @param limit - The number of news to fetch
 * @param category
 * @returns {UseQueryResult<LatestNewsSidebarQuery|NewsByCategoryGenericQuery>}
 */
const getFromDB = (
  limit = 10,
  category: string | null = null,
): UseQueryResult<LatestNewsSidebarQuery | NewsByCategoryGenericQuery> => {
  // Fetch the latest news data
  return kitcoQuery(getQuery(limit, category))
}

/**
 * Normalize the data for use in the component
 *
 * @param data
 */
const normalizeData = (
  data: LatestNewsSidebarQuery | NewsByCategoryGenericQuery,
): NormalizedArticle[] => {
  if (!data) return []

  let items = []

  if ('queue' in data && data.queue?.items) {
    items = data.queue.items
  } else if ('nodeListByCategory' in data && data.nodeListByCategory?.items) {
    items = data.nodeListByCategory.items
  }

  if (!items) return []

  return items
    .map((item) => {
      return {
        id: item.id,
        title: item.title,
        urlAlias: item.urlAlias,
        updatedAt: item.updatedAt,
        legacyThumbnailImageUrl: item.legacyThumbnailImageUrl,
        category: item.category
          ? { name: item.category.name, urlAlias: item.category.urlAlias }
          : null,
        image:
          item.teaserImage?.detail?.default?.srcset ??
          item.image?.detail?.default?.srcset ??
          item.legacyThumbnailImageUrl ??
          null,
      }
    })
    .filter((item) => item !== null) as NormalizedArticle[]
}

export default useLatestNewsSidebar
