import commodityCategories from '~/src/data/GoldIndex/CommodityCategories'
import useGICryptoData from '~/src/hooks/GlobalIndex/useGICryptoData'
import { useGIEnergyData } from '~/src/hooks/GlobalIndex/useGIEnergyData'
import { useGIMetalsData } from '~/src/hooks/GlobalIndex/useGIMetalsData'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import { formatPercentage } from '~/src/utils/Prices/formatPercentage'
import { formatPrice } from '~/src/utils/Prices/formatPrice'

/**
 * Get the gold index data
 *
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 */
const useGlobalIndexData = (onlyPreciousMetals?: boolean) => {
  // Get the metal, energy, and crypto data
  const metalData = useGIMetalsData(onlyPreciousMetals)
  const energyData = onlyPreciousMetals ? null : useGIEnergyData()
  const cryptoData = onlyPreciousMetals ? null : useGICryptoData()

  return createTableData(metalData, energyData, cryptoData, onlyPreciousMetals)
}

/**
 * Create the table data
 *
 * @param metalData
 * @param energyData
 * @param cryptoData
 * @param onlyPreciousMetals
 */
function createTableData(
  metalData: CommodityData[],
  energyData: CommodityData,
  cryptoData: CommodityData[],
  onlyPreciousMetals: boolean,
): CommodityData[] {
  const tableData = generateTableData(
    metalData,
    energyData,
    cryptoData,
    onlyPreciousMetals,
  )

  return formatData(tableData)
}

/**
 * Formats an array of CommodityData objects by categorizing each commodity
 * and formatting specific nested properties.
 *
 * @param {CommodityData[]} data - The array of CommodityData objects to format.
 * @returns {CommodityData[]} - A new array of CommodityData objects with formatted values.
 */
function formatData(data: CommodityData[]): CommodityData[] {
  // Return an empty array if there is no data
  if (!data || data.length <= 0) return []

  return data.map((item: CommodityData) => {
    const category = categorizeCommodity(item.commodity)
    return {
      ...item,
      lastBid: {
        ...item.lastBid,
        bid: formatPrice({ value: item.lastBid.bidVal, category }),
      },
      changeDueToUSD: {
        ...item.changeDueToUSD,
        change: formatPrice({ value: item.changeDueToUSD.changeVal, category }),
        percentage: formatPercentage({
          value: item.changeDueToUSD.percentageVal,
        }),
      },
      changeDueToTrade: {
        ...item.changeDueToTrade,
        change: formatPrice({
          value: item.changeDueToTrade.changeVal,
          category,
        }),
        percentage: formatPercentage({
          value: item.changeDueToTrade.percentageVal,
        }),
      },
      totalChange: {
        ...item.totalChange,
        change: formatPrice({ value: item.totalChange.changeVal, category }),
        percentage: formatPercentage({ value: item.totalChange.percentageVal }),
      },
    }
  })
}

/**
 * Categorize the commodity based on the category
 *
 * @param {string} commodity - The commodity to categorize
 *
 * @returns {string} The category of the commodity
 */
function categorizeCommodity(commodity: string): string {
  for (const category in commodityCategories) {
    if (commodityCategories[category].includes(commodity)) {
      return category
    }
  }
  return null
}

/**
 * Generate the table data from the API data
 *
 * @param {CommodityData[]} metalData - The metal data
 * @param {CommodityData} energyData - The energy data
 * @param {CommodityData[]} cryptoData - The crypto data
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 *
 * @returns {CommodityData[]} The table data
 */
const generateTableData = (
  metalData: CommodityData[],
  energyData: CommodityData,
  cryptoData: CommodityData[],
  onlyPreciousMetals?: boolean,
): CommodityData[] => {
  // No need to process the data if there is only precious metals
  if (onlyPreciousMetals) {
    console.log('Only precious metals')
    return metalData
  }

  console.log('Not only precious metals')

  // Return an empty array if there is no data
  if (!metalData || metalData.length <= 0) {
    console.log('No metal data')
    return []
  }

  // Create a copy of the metal data to avoid mutating the original
  const result = [...metalData]

  // Add energy data if available
  if (energyData) {
    // Find the index of copper entry
    const copperIndex = result.findIndex(
      (entry) => entry.commodity === 'Copper',
    )

    // Insert the energy entry before the copper entry
    if (copperIndex !== -1) {
      result.splice(copperIndex, 0, energyData)
    } else {
      result.push(energyData)
    }
  }

  // Add crypto data if available
  if (cryptoData && cryptoData.length > 0) {
    console.log('Adding crypto data')
    result.push(...cryptoData)
  }

  return result
}

export { createTableData, useGlobalIndexData }
