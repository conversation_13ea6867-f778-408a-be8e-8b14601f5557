import { useEffect, useRef } from 'react'

interface TableHeader {
  getSize: () => number
  column: {
    getIsPinned: () => string | boolean
  }
}

export const useTableHeaderStyle = (header: TableHeader) => {
  const ref = useRef<HTMLTableHeaderCellElement>(null)

  useEffect(() => {
    if (ref.current && header?.getSize) {
      ref.current.style.setProperty('--header-size', `${header.getSize()}px`)
    }
  }, [header])

  return ref
}
