import { useCurrency } from '~/src/hooks/Currency/useCurrency'
import { metals } from '~/src/lib/metals-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

/**
 * Get the currency query
 */
export const getCurrencyQuery = () => {
  return metals.currencies({
    variables: {
      timestamp: timestamps.current(),
    },
  })
}

/**
 * Get the metal quote query
 * This function is used to fetch the metal quote data for a given symbol and currency
 *
 * @param {string} symbol - The symbol of the metal to fetch the quote for
 * @param {string} currency - The currency to convert the quote to
 * @returns {Query} The metal quote query
 */
export const getMetalQuoteQuery = (symbol: string, currency: string) => {
  return metals.metalQuote({
    variables: {
      symbol: symbol,
      currency: currency ?? 'USD',
      timestamp: timestamps.current(),
    },
  })
}

/**
 * Fetches the metal quotes and converts them to the selected currency
 *
 * @param symbol
 */
const useMetalQuoteCell = (symbol: string) => {
  // Retrieve the current currency using a custom hook
  const currency = useCurrency()

  // Fetch the metal quote data for the given symbol
  const { data, isLoading, error } = kitcoQuery(
    getMetalQuoteQuery(symbol, currency.symbol),
  )

  // Return an object containing the currency, converted data, loading status, and error information
  return {
    currency,
    data,
    isLoading,
    error,
  }
}

export default useMetalQuoteCell
