import type { SortingState } from '@tanstack/table-core'
import { useEffect, useState } from 'react'
import columns from '~/src/components/MiningEquities/DataTable/DataTableColumns'

/**
 * Hook to manage the sorting state
 *
 * @param initialSorting
 */
export const useSorting = (initialSorting: SortingState) => {
  // Internal sorting state
  const [internalSorting, setInternalSorting] = useState<SortingState>([])

  // Check if the initial sorting column exists
  const [sortColumnExists, setSortColumnExists] = useState<boolean>(true)

  useEffect(() => {
    // Check if the initial sorting is empty
    if (!initialSorting || initialSorting.length <= 0) {
      if (sortColumnExists !== false) {
        setSortColumnExists(false)
      }
      if (internalSorting.length > 0) {
        setInternalSorting([])
      }
      return
    }

    // Check if the initial sorting column exists
    const columnExists = columns.some((col) => col.id === initialSorting[0].id)

    // Update the state
    setSortColumnExists(columnExists)
    setInternalSorting(columnExists ? initialSorting : [])
  }, [initialSorting])

  return { internalSorting, sortColumnExists, setInternalSorting }
}
