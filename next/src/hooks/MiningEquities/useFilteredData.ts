import { useMemo, useState } from 'react'
import { useDebounce } from 'use-debounce'
import {
  filterDataByCategory,
  filterDataBySearch,
} from '~/src/hooks/MiningEquities/filterService'
import type MiningEquity from '~/src/types/DataTable/MiningEquity'

/**
 * Filter the data based on the active filter, sub filter, and search term.
 *
 * @param {MiningEquity[]} data - The data to filter.
 * @param {string} activeFilter - The active filter.
 * @param {string} activeSubFilter - The active sub filter.
 * @param {string} searchTerm - The search term.
 * @returns {Object} An object containing the filtered data and the search results.
 */
export const useFilteredData = (
  data: MiningEquity[],
  activeFilter: string,
  activeSubFilter: string,
  searchTerm: string,
): { filteredData: MiningEquity[]; searchResults: string } => {
  const [searchValue] = useDebounce(searchTerm, 300)
  const [searchResults, setSearchResults] = useState('')

  const filteredData = useMemo(() => {
    let filtered = filterDataByCategory(data, activeFilter)
    // Disabled for now
    //filtered = filterDataBySubCategory(filtered, activeSubFilter)
    filtered = filterDataBySearch(filtered, searchValue)

    setSearchResults(
      searchValue
        ? filtered.length
          ? filtered.length === 1
            ? `${filtered.length} Equity`
            : `${filtered.length} Equities`
          : '0 Equities'
        : '',
    )

    return filtered
  }, [data, activeFilter, activeSubFilter, searchValue])

  return { filteredData, searchResults }
}
