import type dayjs from 'dayjs'
import { metals } from '~/src/lib/metals-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

/**
 * Fetches the necessary data for the chart.
 *
 * @param {Object} args - The arguments for fetching the data.
 * @param {string} args.symbol - The metal symbol
 * @param {string} args.currency - The currency code
 * @param {dayjs.Dayjs} now - The current time
 *
 * @returns {Object} - Returns the fetched data for both nivoChartData and metalQuote.
 */
function useFetchChartData(
  args: { symbol: string; currency: string },
  now: dayjs.Dayjs,
) {
  // Create a query for historical data
  const nivoChartQuery = metals.nivoChartData({
    variables: {
      ...args,
      timestamp: now.unix(),
    },
  })

  // Fetch historical data from server
  return kitcoQuery(nivoChartQuery)
}

export default useFetchChartData
