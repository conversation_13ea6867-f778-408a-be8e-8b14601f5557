import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import type { AppDispatch, RootState } from '~/src/features/store/store'
import { initializeWeight, setWeight } from '~/src/features/store/weightSlice'
import type WeightType from '~/src/types/WeightSelect/WeightType'

const useWeight = (
  type: WeightType,
  defaultWeight: string = null,
  id = 'global',
) => {
  const dispatch = useDispatch<AppDispatch>()
  const weight = useSelector(
    (state: RootState) => state.weight[id]?.selectedWeight,
  )
  const savedWeight = useSelector(
    (state: RootState) => state.weight[id]?.savedWeight,
  )

  /**
   * Initialize weight (check if it's saved in localStorage)
   */
  useEffect(() => {
    dispatch(initializeWeight({ type, defaultWeight, id }))
  }, [dispatch, type, defaultWeight, id])

  /**
   * Set default weight if it's not saved
   */
  useEffect(() => {
    if (!savedWeight) {
      dispatch(setWeight({ type, label: defaultWeight, id }))
    }
  }, [dispatch, type, defaultWeight, id, savedWeight])

  return weight
}

export default useWeight
