import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(timezone)

export const HUB_TIMEZONES: Record<string, string> = {
  hk: 'Asia/Hong_Kong',
  mumbai: 'Asia/Kolkata',
  london: 'Europe/London',
  ny: 'America/New_York',
}

export const isBusinessDay = (d: dayjs.Dayjs): boolean =>
  d.day() !== 0 && d.day() !== 6

export const getFixTime = (hubKey: string, date: string): dayjs.Dayjs =>
  dayjs.tz(`${date} 10:30:00`, HUB_TIMEZONES[hubKey])

export const getCountdown = (
  hubKey: string,
  date: string,
  now: dayjs.Dayjs,
): { hours: number; minutes: number } => {
  const fixTime = getFixTime(hubKey, date)
  const diff = fixTime.diff(now, 'minute')

  if (diff <= 0) {
    return { hours: 0, minutes: 0 }
  }

  return {
    hours: Math.floor(diff / 60),
    minutes: diff % 60,
  }
}

export const isPriceAvailable = (
  hubKey: string,
  date: string,
  now: dayjs.Dayjs,
): boolean => {
  const tz = HUB_TIMEZONES[hubKey]
  const selected = dayjs.tz(date, tz)

  // Not available on weekends
  if (!isBusinessDay(selected)) return false

  // Past dates are always available (historical data)
  if (selected.isBefore(now.startOf('day'))) return true

  // For today, check if fix time has passed
  if (selected.isSame(now, 'day')) {
    const fixTime = getFixTime(hubKey, date)
    return now.isAfter(fixTime)
  }

  // Future dates are not available
  return false
}

// export const getHubGMTInfo = (hubKey: string): string => {
//   switch (hubKey) {
//     case 'ny':
//       return '(GMT-5/-4)'
//     case 'london':
//       return '(GMT+0/+1)'
//     case 'mumbai':
//       return '(GMT+5:30)'
//     case 'hk':
//       return '(GMT+8)'
//     default:
//       return ''
//   }
// }
