import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(timezone)

const HK_TZ = 'Asia/Hong_Kong'
const NY_TZ = 'America/New_York'

/** Default Morning Fix date for the picker */
export function getMorningFixDefaultDate(): string {
  const nowHK = dayjs().tz(HK_TZ)
  const isBizDay = nowHK.day() !== 0 && nowHK.day() !== 6
  const fixTime = nowHK
    .set('hour', 10)
    .set('minute', 30)
    .set('second', 0)
    .set('millisecond', 0)

  if (isBizDay && nowHK.isAfter(fixTime)) {
    // HK fix already available – use HK date
    return nowHK.format('YYYY-MM-DD')
  }

  // otherwise fall back to New‑York calendar date
  return dayjs().tz(NY_TZ).format('YYYY-MM-DD')
}
