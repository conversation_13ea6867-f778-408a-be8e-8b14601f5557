type Func = (uuid: string, thumbnailUuid: string) => string

const videoImageUrl: Func = (uuid, thumbUuid) => {
  return `${process.env.NEXT_PUBLIC_VCMS_BUCKET}/${uuid}/${thumbUuid}`
}

export const videoThumbnail: Func = (uuid, thumbUuid) => {
  return `${videoImageUrl(uuid, thumbUuid)}.jpeg`
}

export const videoPoster: Func = (uuid, thumbUuid) => {
  return `${videoImageUrl(uuid, thumbUuid)}_poster.jpeg`
}
