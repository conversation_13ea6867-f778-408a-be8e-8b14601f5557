import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(isBetween)

// The interval in seconds
const INTERVAL = 30

/**
 * Returns the current timestamp rounded to the nearest interval.
 * @param interval Interval in seconds to which the timestamp should be rounded.
 */
function currentTimestamp(interval: number = INTERVAL): number {
  const now = dayjs()

  // Get current seconds
  const seconds = now.second()

  // Calculate the nearest lower multiple of the interval
  const roundedSeconds = Math.floor(seconds / interval) * interval

  // Construct a new time with the rounded seconds
  const roundedTime = now.second(roundedSeconds).millisecond(0)

  // Return the Unix timestamp (in seconds)
  return roundedTime.unix()
}

// Timestamps for the last hour, day, week, month, year, and six months.
const current = () => currentTimestamp()
const oneHourAgo = () => currentTimestamp() - 3600
const oneDayAgo = () => currentTimestamp() - 86400
const oneWeekAgo = () => currentTimestamp() - 604800
const oneMonthAgo = () => currentTimestamp() - 2592000
const oneYearAgo = () => currentTimestamp() - 31536000
const sixMonthsAgo = () => currentTimestamp() - 15768000

// this function to adapt logic that Stock weekend in New York market
// is from 5pm Fri to 5pm Sun
const lastWeekend = () => {
  let now = dayjs().tz(process.env.NEXT_PUBLIC_TIMEZONE)
  // if now is Sunday before 5pm, subtract 1 to get last week instead of next week
  // we ran to a situation that dayjs start from Sun to Sat
  // so if we don't subtract in Sunday, it will be take the next weekend
  if (now.day() === 0) {
    const sunday5pm = now
      .clone()
      .set('hour', 17)
      .set('minute', 0)
      .set('second', 0)
      .set('millisecond', 0)

    if (now.isBefore(sunday5pm)) {
      now = now.subtract(1, 'day')
    }
  }

  const weekendStart = now.startOf('week').add(5, 'day').set('hour', 17)
  const weekendEnd = weekendStart.add(2, 'day').set('hour', 17)

  return {
    weekendStart: weekendStart,
    weekendEnd: weekendEnd,
    isWeekend: now.isBetween(weekendStart, weekendEnd, null, '[]'),
  }
}

// const refetchInterval = INTERVAL * 1000
// restrict weekend fetching
const refetchInterval = (): number | false => {
  return lastWeekend().isWeekend ? false : INTERVAL * 1000
}

export {
  current,
  lastWeekend,
  oneDayAgo,
  oneHourAgo,
  oneMonthAgo,
  oneWeekAgo,
  oneYearAgo,
  refetchInterval,
  sixMonthsAgo,
}
