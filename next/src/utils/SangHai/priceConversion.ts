import type { WeightHashEntry } from '~/src/types/WeightSelect/WeightHash'
import { convert } from '~/src/utils/price-conversion'
import { convert as convertGram } from '~/src/utils/price-gram-conversion'

/**
 * Render functions for OZ
 */
export const renderFunctions = {
  priceToOz: convert.priceToOz,
  priceToGram: convert.priceToGram,
  priceToTola: convert.priceToTola,
  priceToKilo: convert.priceToKilo,
  priceBMToPound: convert.priceBMToPound,
  priceBMToGram: convert.priceBMToGram,
  priceBMToKilo: convert.priceBMToKilo,
  priceBMToTon: convert.priceBMToTon,
  priceBMToTonne: convert.priceBMToTonne,
}

/**
 * Render functions for Gram
 */
export const renderFunctionsGram = {
  priceToOz: convertGram.priceToOz,
  priceToGram: convertGram.priceToGram,
  priceToTola: convertGram.priceToTola,
  priceToKilo: convertGram.priceToKilo,
}

/**
 * Return Render function
 *
 * @param weight
 * @param val
 * @param options
 */
export const renderFn = (
  weight: WeightHashEntry,
  val: number,
  options: { symbol?: string; precision?: number; defaultWeight?: string } = {
    symbol: '',
    precision: 2,
    defaultWeight: '',
  },
): string => {
  if (!weight) {
    return val
      ? val.toLocaleString('en-US', {
          minimumFractionDigits: options.precision,
          maximumFractionDigits: options.precision,
        })
      : ''
  }

  if (options.defaultWeight === 'GRAM') {
    return renderFunctionsGram[weight.renderFn](val, options)
  }
  return renderFunctions[weight.renderFn](val, options)
}
