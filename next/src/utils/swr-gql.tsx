import { type FC, type ReactNode, createContext } from 'react'

/**
 * Props for the DataCtx component.
 */
interface DataCtxProps {
  children: ReactNode | ReactNode[]
  ssrData?: any
}

// Create a context for holding the server-side rendered data.
export const DataCtx = createContext<{ ssrData?: any } | null>(null)

/**
 * A provider component for the DataCtx context.
 *
 * @param {DataCtxProps} props - The props for the component.
 */
export const SWRDataCtx: FC<DataCtxProps> = ({
  children,
  ssrData,
}: DataCtxProps) => (
  <DataCtx.Provider value={{ ssrData }}>{children}</DataCtx.Provider>
)
