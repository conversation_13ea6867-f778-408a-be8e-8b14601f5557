/**
 * Get the conversion rate of a currency to USD
 *
 * @param {string} symbol - The currency symbol
 * @param {Record<string, any>} currencies - The currencies data
 *
 * @returns {number} - The conversion rate
 */
const getConversionRate = (
  symbol: string,
  currencies: Record<string, any>,
): number => {
  if (symbol === 'USD' || !currencies) return 1
  return currencies[symbol].results[0].ctousd
}

export default getConversionRate
