import { titleCase as externalTitleCase } from 'title-case'

/**
 * Capitalizes acronyms and applies title case to a given string.
 *
 * This function first identifies acronyms in the input string (e.g., `G.e.t.t.`)
 * and transforms them into fully capitalized acronyms (e.g., `G.E.T.T.`).
 * After processing acronyms, the `titleCase` function is applied to the rest of the string
 * to ensure proper title casing.
 *
 * @param {string} input - The input string to transform.
 * @param {Object} [options] - The options for title case transformation.
 * @param {string | string[]} [options.locale] - The locale to use for case transformations.
 * @param {boolean} [options.sentenceCase] - Whether to only capitalize the first word of each sentence.
 * @param {Set<string>} [options.sentenceTerminators] - Custom sentence terminators for sentence case.
 * @param {Set<string>} [options.smallWords] - Set of small words to avoid capitalizing (e.g., "and", "the").
 * @param {Set<string>} [options.titleTerminators] - Custom terminators for title case.
 * @param {Set<string>} [options.wordSeparators] - Characters treated as word separators for capitalization.
 * @returns {string} - The transformed string with acronyms and title case applied.
 */
export function titleCase(input: string, options: any = {}): string {
  /**
   * Regex to detect acronyms in the string.
   * Matches acronyms like "G.e.t.t." or "A.B.C." at any position in the string.
   */
  // @ts-ignore - Regexp is valid
  const ACRONYM_REGEX = /(^|[^\p{L}])(?:\p{L}\.){2,}(?=$|[^\p{L}])/gu

  const lowercase = input.toLowerCase()

  // Step 1: Capitalize acronyms
  const processedInput = lowercase.replace(ACRONYM_REGEX, (match) =>
    match.toUpperCase(),
  )

  // Step 2: Apply title case
  return externalTitleCase(processedInput, options)
}
