// Conversion utilities for Morning Fix component

export type UnitOfMeasure = 'OZ' | 'Gram' | 'TOLA' | 'Kilo'
export type Currency =
  | 'USD'
  | 'AUD'
  | 'CAD'
  | 'EUR'
  | 'GBP'
  | 'JPY'
  | 'CHF'
  | 'CNY'
  | 'HKD'
  | 'BRL'
  | 'INR'
  | 'MXN'
  | 'RUB'
  | 'ZAR'

// Conversion rates from 1 Troy Ounce
export const UNIT_CONVERSIONS: Record<UnitOfMeasure, number> = {
  OZ: 1, // 1 Troy Ounce (base unit)
  Gram: 31.1035, // 31.1035 Gram
  TOLA: 2.6666646776086, // 2.6666646776086 Tola
  Kilo: 0.0311035, // 0.0311035 Kilo
}

// Currency options for dropdown
export const CURRENCY_OPTIONS: Currency[] = [
  'USD',
  'AUD',
  'CAD',
  'EUR',
  'GBP',
  'JPY',
  'CHF',
  'CNY',
  'HKD',
  'BRL',
  'INR',
  'MXN',
  'RUB',
  'ZAR',
]

// Unit of measure options for dropdown
export const UOM_OPTIONS: UnitOfMeasure[] = ['OZ', 'Gram', 'TOLA', 'Kilo']

/**
 * Convert price from Troy Ounce to specified unit
 * @param priceInOz - Price per Troy Ounce
 * @param targetUnit - Target unit of measure
 * @returns Converted price
 */
export function convertPrice(
  priceInOz: number,
  targetUnit: UnitOfMeasure,
): number {
  if (!Number.isFinite(priceInOz) || priceInOz <= 0) return priceInOz

  const conversionRate = UNIT_CONVERSIONS[targetUnit]
  if (!conversionRate || conversionRate <= 0) return priceInOz

  return priceInOz / conversionRate
}

/**
 * Format price with consistent 2 decimal places and thousand separators
 * @param price - Price to format
 * @param _unit - Unit of measure (unused, kept for API compatibility)
 * @returns Formatted price string with thousand separators
 */
export function formatPrice(price: number, _unit: UnitOfMeasure): string {
  if (!Number.isFinite(price)) return '-'

  // Always use 2 decimal places for consistency
  const formattedPrice = price.toFixed(2)

  // Add thousand separators
  return formattedPrice.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * Get display label for unit of measure
 * @param unit - Unit of measure
 * @returns Display label
 */
export function getUnitLabel(unit: UnitOfMeasure): string {
  const labels: Record<UnitOfMeasure, string> = {
    OZ: 'OZ',
    Gram: 'Gram',
    TOLA: 'TOLA',
    Kilo: 'Kilo',
  }
  return labels[unit]
}
