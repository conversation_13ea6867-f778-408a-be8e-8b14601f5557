import type { FC } from 'react'
import BarchartChartGrid from '~/src/components-markets/BarchartChartGrid/BarchartChartGrid'
import useFuturesMeats from '~/src/components-markets/FuturesCell/meatsDataCell'
import QuotesTable from '~/src/components/QuotesTable/QuotesTable'
import SkeletonChart from '~/src/components/SkeletonChart/SkeletonChart'
import FuturesCategoryPageWrapper from '~/src/components/futures-category-page-wrapper/futures-category-page-wrapper.component'
import { Barcharts } from '~/src/features/bar-charts/barcharts'

// chasing barcharts gcdn err. this page is good
// export async function getServerSideProps() {
//   const apollo = initializeApollo()
//
//   await apollo.query({
//     query: componentData.query,
//     variables: componentData.variables,
//   })
//
//   return addApolloState(apollo, {
//     props: {},
//   })
// }

const FuturesMeats: FC = () => {
  const [meats, isLoading] = useFuturesMeats()

  return (
    <FuturesCategoryPageWrapper category="Meats">
      <>
        {meats.length > 0 ? (
          <BarchartChartGrid columns={2}>
            <Barcharts
              symbol={meats[0].symbol}
              title={meats[0].name}
              href={`/markets/futures/${meats[0].symbol}`}
            />
            <div className="hidden md:block lg:block">
              <Barcharts
                symbol={meats[1].symbol}
                title={meats[1].name}
                href={`/markets/futures/${meats[1].symbol}`}
              />
            </div>
          </BarchartChartGrid>
        ) : (
          <>
            <div className="hidden md:block lg:block">
              <SkeletonChart columns={2} />
            </div>
            <div className="block md:hidden lg:hidden">
              <SkeletonChart columns={1} />
            </div>
          </>
        )}
        <div className="mb-8">
          <QuotesTable
            title={'Meats'}
            section={'futures'}
            data={meats}
            isLoading={isLoading}
          />
        </div>
      </>
    </FuturesCategoryPageWrapper>
  )
}

export default FuturesMeats
