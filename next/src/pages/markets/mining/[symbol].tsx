import type { GetServerSideProps } from 'next'
import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import Layout from '~/src/components/Layout/Layout'
import PageLayoutTwoColumns from '~/src/components/PageLayoutTwoColumns/PageLayoutTwoColumns'
import QuotesTable from '~/src/components/QuotesTable/QuotesTable'
import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import TabLinks from '~/src/components/TabLinks/TabLinks'
import MiningStocks from '~/src/data/Mining/MiningStocks'
import { Barcharts } from '~/src/features/bar-charts/barcharts'
import { markets } from '~/src/lib/markets-factory.lib'
import marketsMiningTabLinks from '~/src/lib/marketsMiningTabLinks'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import colorize from '~/src/utils/colorize'
import * as timestamps from '~/src/utils/timestamps'

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 's-maxage=600, stale-while-revalidate')
  const sector = MiningStocks.find((x) => x.sectorSymbol === ctx?.query?.symbol)
  return {
    props: {
      name: sector.name,
      symbol: sector.sectorSymbol,
      symbols: sector.symbols,
    },
  }
}

const MarketsMiningSymbol: FC<{
  name: string
  symbol: string
  symbols: string
}> = ({ name, symbol, symbols }) => {
  const { data, isFetching } = kitcoQuery(
    markets.barchartsQuotes({
      variables: {
        timestamp: timestamps.current(),
        symbols,
      },
    }),
  )
  const { data: sectorData } = kitcoQuery(
    markets.barchartsQuotes({
      variables: {
        timestamp: timestamps.current(),
        symbols: symbol,
      },
    }),
  )

  return (
    <Layout title="Mining Stocks">
      <PageLayoutTwoColumns>
        <main>
          <div className="mb-8">
            <TabLinks items={marketsMiningTabLinks} />
          </div>
          <div>
            {sectorData?.GetBarchartQuotes?.results.length ? (
              <div className="mb-8 grid sm:grid-cols-1 sm:gap-0 lg:grid-cols-2 lg:gap-6">
                <Barcharts
                  symbol={sectorData?.GetBarchartQuotes?.results[0].symbol}
                  title={sectorData?.GetBarchartQuotes?.results[0].name}
                  href={`/markets/mining/${sectorData?.GetBarchartQuotes?.results[0].symbol}`}
                />
                {sectorData?.GetBarchartQuotes?.results.map(
                  (x, idx: number) => (
                    <div key={idx}>
                      <h4 className="mb-2 text-base font-semibold">
                        Sector price today ({x.symbol})
                      </h4>
                      <ul className="divide-y">
                        <li className="flex justify-between p-2">
                          <p>High price today</p>
                          <span className="font-semibold">{x.high}</span>
                        </li>
                        <li className="flex justify-between p-2">
                          <p>Low price today</p>
                          <span className="font-semibold">{x.low}</span>
                        </li>
                        <li className="flex justify-between p-2">
                          <p>Last price today</p>
                          <span className="font-semibold">{x.lastPrice}</span>
                        </li>
                        <li className="flex justify-between p-2">
                          <p>Net change</p>
                          <span className={colorize(x.netChange)}>
                            {x.netChange.toFixed(2)}
                          </span>
                        </li>
                        <li className="flex justify-between p-2">
                          <p>Percent Change</p>
                          <span className={colorize(x.percentChange)}>
                            {x.percentChange}%
                          </span>
                        </li>
                        <li className="flex justify-between p-2">
                          <p>Volume</p>
                          <span className="font-semibold">{x.volume}</span>
                        </li>
                      </ul>
                    </div>
                  ),
                )}
              </div>
            ) : (
              <>
                <div className="mb-8 grid sm:grid-cols-1 sm:gap-0 lg:grid-cols-2 lg:gap-6">
                  <Barcharts
                    symbol={sectorData?.GetBarchartQuotes?.results[0].symbol}
                    title={sectorData?.GetBarchartQuotes?.results[0].name}
                    href={`/markets/mining/${sectorData?.GetBarchartQuotes?.results[0].symbol}`}
                  />
                  <div>
                    <h4 className="mb-2 text-base font-semibold">
                      Sector price today
                    </h4>
                    <ul className="divide-y">
                      <li className="flex justify-between p-2">
                        <p>High price today</p>
                        <SkeletonTable />
                      </li>
                      <li className="flex justify-between p-2">
                        <p>Low price today</p>
                        <SkeletonTable />
                      </li>
                      <li className="flex justify-between p-2">
                        <p>Last price today</p>
                        <SkeletonTable />
                      </li>
                      <li className="flex justify-between p-2">
                        <p>Net change</p>
                        <SkeletonTable />
                      </li>
                      <li className="flex justify-between p-2">
                        <p>Percent Change</p>
                        <SkeletonTable />
                      </li>
                      <li className="flex justify-between p-2">
                        <p>Volume</p>
                        <SkeletonTable />
                      </li>
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
          <QuotesTable
            title={name}
            section="stocks"
            data={data?.GetBarchartQuotes?.results}
            isLoading={isFetching}
          />
        </main>
        <aside>
          <AdvertisingSlot
            id={'right-rail-1'}
            className={
              'mx-auto mb-8 flex h-[250px] w-[300px] items-center justify-center no-print'
            }
          />
          <LatestNewsCell />
        </aside>
      </PageLayoutTwoColumns>
    </Layout>
  )
}

export default MarketsMiningSymbol
