import type { GetServerSideProps } from 'next'
import type { FC } from 'react'
import MarketPageIndicesCell from '~/src/components-markets/MarketPageIndicesCell/MarketPageIndicesCell'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import IndexDetailTitleBlock from '~/src/components/IndexDetailTitleBlock/IndexDetailTitleBlock'
import IndexHighLowTable from '~/src/components/IndexHighLowTable/IndexHighLowTable'
import Layout from '~/src/components/Layout/Layout'
import { Barcharts } from '~/src/features/bar-charts/barcharts'
import { markets } from '~/src/lib/markets-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import styles from '~/src/styles/markets-symbol-page.module.scss'
import * as timestamps from '~/src/utils/timestamps'

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  return {
    props: {
      symbol: ctx.query.symbol,
    },
  }
}

const StockSymbol: FC<{ symbol: string }> = ({ symbol }) => {
  const { data } = kitcoQuery(
    markets.barchartsQuotes({
      variables: {
        symbols: symbol,
        timestamp: timestamps.current(),
      },
    }),
  )

  return (
    <Layout title={symbol}>
      <div className="grid gap-8 px-4 sm:grid-cols-1  sm:px-8 lg:grid-cols-layout-2">
        <div>
          <IndexDetailTitleBlock data={data} />
          <section className={styles.chartBlock}>
            <Barcharts symbol={symbol} />
          </section>
          {data?.GetBarchartQuotes?.results.length > 0 && (
            <section className={styles.infoBlock}>
              <IndexHighLowTable data={data} />
            </section>
          )}
          <section className={styles.infoBlock}>
            <MarketPageIndicesCell />
          </section>
        </div>
        <div>
          <LatestNewsCell />
        </div>
      </div>
    </Layout>
  )
}

export default StockSymbol
