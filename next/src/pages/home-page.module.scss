@import './../styles/_vars.scss';

.breakingNewsContainer {
  margin: 0 auto 4em auto;
}

.dividerDark {
  height: 2px;
  width: 100%;
  margin-top: 20px;
  background-color: #373737;
}

.tradingView {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 1em;
  margin-top: 4em;
}

.gridTwoColumn {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 2em;
}

.tabletGridOrder {
  display: flex;
  flex-direction: column;
  // gap: 1.25em;
  // display: grid;
  // grid-template-columns: 1fr;
  // grid-template-areas:
  //   'jj'
  //   'ww'
  //   'kk'
  //   'll'
  //   'fff'
  //   'nn'
  //   'aa'
  //   'bb'
  //   'ggg'
  //   'cc'
  //   'dd'
  //   'ee'
  //   'ff'
  //   'hh'
  //   'ii'
  //   'hhh'
  //   'oo'
  //   'qq'
  //   'iii'
  //   'yy'
  //   'zz'
  //   'aaa'
  //   'bbb'
  //   'jjj'
  //   'tt'
  //   'uu'
  //   'vv';

  @media (min-width: 768px) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
      'ss ss'
      'jj jj'
      'aa ww'
      'bb ww'
      'kk kk'
      'll ll'
      'mm mm'
      'nn nn'
      'cc hh'
      'dd hh'
      'ee ii'
      'ff ii'
      'ddd ddd'
      'oo oo'
      'pp pp'
      'qq qq'
      'rr rr'
      'yy aaa'
      'zz bbb'
      'tt tt'
      'eee eee'
      'uu uu'
      'vv vv';
  }

  @media (min-width: 1270px) {
    display: grid;
    grid-template-columns: 180px 730px 300px;
    grid-template-areas: none;
  }

  // Have to switch a little later, as at 1270 there is not room for the center well to be 728px large - TC 8/21/2024
  @media (min-width: 1320px) {
    grid-template-columns: 200px minmax(728px, 1fr) 336px;
  }
}

.miningBannerContainer {
  overflow-x: hidden;
  max-width: 100%;

  @media (min-width: 1270px) {
    display: block;
  }
}
