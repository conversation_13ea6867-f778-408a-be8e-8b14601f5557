import Document, { Head, Html, Main, NextScript } from 'next/document'
import Script from 'next/script'
import AppIcons from '~/src/components/Layout/AppIcons'

export default function MyDocument({ router }) {
  // Google Tag Manager ID
  const googleTagManagerID = 'GTM-PRH98Z'

  // Google Ad Manager (GAM) init script
  const gamInitScript =
    'var googletag=googletag||{};googletag.cmd=googletag.cmd||[]'

  // Prebid init script
  const prebidInitScript = 'var pbjs=pbjs||{};pbjs.que=pbjs.que||[]'

  /**
   * Set the data-theme attribute on the <html> element based on the current route.
   */
  const dataTheme = () => {
    if (router.pathname === '/advertising') {
      return 'advertising'
    }

    if (router.pathname === '/services/cpm-group-signals') {
      return 'services'
    }

    return ''
  }

  return (
    <Html data-theme={dataTheme()} lang="en">
      <Head>
        <link
          rel="dns-prefetch"
          href="https://securepubads.g.doubleclick.net"
        />
        <link rel="preconnect" href="https://securepubads.g.doubleclick.net" />
        <link
          rel="preload"
          href="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
          as="script"
        />
        <script
          src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
          async
        />
        <script src="/prebid.kc.js" async />
        <script>{gamInitScript}</script>
        <script>{prebidInitScript}</script>

        <link rel="dns-prefetch" href="https://c.amazon-adsystem.com" />
        <link rel="preconnect" href="https://c.amazon-adsystem.com" />
        <script src="https://c.amazon-adsystem.com/aax2/apstag.js" async />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              !function(a9,a,p,s,t,A,g){
                if(a[a9])return;
                function q(c,r){a[a9]._Q.push([c,r])}
                a[a9]={
                  init:function(){q("i",arguments)},
                  fetchBids:function(){q("f",arguments)},
                  setDisplayBids:function(){},
                  targetingKeys:function(){return[]},
                  _Q:[]
                };
                A=p.createElement(s);
                A.async=!0;
                A.src=t;
                g=p.getElementsByTagName(s)[0];
                g.parentNode.insertBefore(A,g);
              }("apstag",window,document,"script","//c.amazon-adsystem.com/aax2/apstag.js");
            `,
          }}
        />

        <script
          async
          src="https://fundingchoicesmessages.google.com/i/pub-5330923290342169?ers=1"
          nonce="dMYbj03DFoJrGx7o-GIfVQ"
        />
        <script
          nonce="dMYbj03DFoJrGx7o-GIfVQ"
          dangerouslySetInnerHTML={{
            __html: `(function() {function signalGooglefcPresent() {if (!window.frames['googlefcPresent']) {if (document.body) {const iframe = document.createElement('iframe'); iframe.style = 'width: 0; height: 0; border: none; z-index: -1000; left: -1000px; top: -1000px;'; iframe.style.display = 'none'; iframe.name = 'googlefcPresent'; document.body.appendChild(iframe);} else {setTimeout(signalGooglefcPresent, 0);}}}signalGooglefcPresent();})();`,
          }}
        />

        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Lato:wght@300;400;700&family=Mulish:wght@300;500;600;700&display=swap"
          rel="stylesheet"
        />

        {/* Google Tag Manager */}
        <Script id="google-tag-manager" strategy="afterInteractive">
          {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());

              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','${googleTagManagerID}');
            `}
        </Script>

        <AppIcons />
        <link rel="manifest" href="/favicons/site.webmanifest" />
      </Head>
      <body>
        <noscript
          dangerouslySetInnerHTML={{
            __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=${googleTagManagerID}" height="0" width="0" style="display: none; visibility: hidden;" />`,
          }}
        />
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}

/**
 * Get the initial props.
 *
 * @param ctx
 */
MyDocument.getInitialProps = async (ctx) => {
  const initialProps = await Document.getInitialProps(ctx)
  return { ...initialProps, router: ctx }
}
