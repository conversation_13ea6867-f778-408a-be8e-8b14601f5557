import type { NextApiRequest, NextApiResponse } from 'next'
import crypto from 'node:crypto'
import { verifyToken } from '~/src/services/firebase/admin/service'
import { getUsername } from '~/src/services/firebase/service'

/**
 * The handler function for the Discourse SSO authentication
 *
 * @param req : NextApiRequest The request object
 * @param res : NextApiResponse The response object
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  // Only allow POST requests
  if (req.method !== 'POST')
    return res.status(405).json({ error: 'Method Not Allowed' })

  // Check if we have the SSO payload and signature in the request body
  if (!req.body?.sso || !req.body?.sig) {
    return res.status(400).json({ error: 'Missing SSO payload or signature' })
  }

  const authHeader = req.headers.authorization

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Invalid authorization header' })
  }

  // Extract the SSO payload and signature from the request body
  const { sso, sig } = req.body

  // Get the secret key from the environment variables
  const secret = process.env.DISCOURSE_SSO_SECRET

  // Validate the SSO request
  if (!validate(sso, sig, secret)) {
    return res.status(401).json({ error: 'Invalid SSO request' })
  }

  // Extract the firebase token from the Authorization header
  const token = req.headers.authorization?.split(' ')[1]

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Invalid authorization header' })
  }

  // Check if the token is provided
  if (!token) {
    return res.status(401).json({ error: 'Authorization token not provided' })
  }

  try {
    const decodedToken = await verifyToken(token)

    const username = await getUsername(decodedToken.email)

    // Extract the user data from the decoded token
    const user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.displayName,
      username: username,
    }

    // Extract the nonce and return_sso_url parameters from the SSO payload
    const { nonce, return_sso_url } = getDiscourseParams(sso)

    // Construct the payload to send to Discourse
    const payload = {
      nonce: nonce,
      email: user.email,
      //require_activation: 'true',
      external_id: user.uid,
      username: user.username,
      name: user.name,
    }

    // Generate the URL to redirect the user to Discourse
    const discourseUrl = generateUrl(sso, return_sso_url, payload, secret)

    // Return the URL to the client
    return res.status(200).json({ url: discourseUrl })
  } catch (error) {
    console.error('Discourse SSO authentication failed:', error)
    res.status(401).json({ error: 'Failed to authenticate user' })
  }
}

/**
 * Validates the SSO request by comparing the generated hash with the signature provided in the request
 *
 * @param sso : string The SSO payload
 * @param sig : string The signature provided in the request
 * @param secret : string The secret key used to generate the signature
 */
function validate(sso: string, sig: string, secret: string) {
  // Important: The SSO payload must be Base64 encoded before hashing
  const hash = crypto
    .createHmac('sha256', secret)
    .update(sso) // Here we use the original SSO payload (not Base64 encoded)
    .digest('hex')

  // Compare the generated hash with the signature provided in the request
  return hash === sig
}

/**
 * Generates the URL to redirect the user to Discourse
 *
 * @param sso : string The SSO payload
 * @param returnSsoUrl : string The URL to return the user to after authenticating
 * @param payload : Record<string, string> The payload to send to Discourse
 * @param secret : string The secret key used to generate the signature
 */
function generateUrl(
  sso: string,
  returnSsoUrl: string,
  payload: Record<string, string>,
  secret: string,
) {
  // Construct a query string from the payload
  const payloadQueryString = new URLSearchParams(payload).toString()

  // Encode the payload to Base64
  const base64Payload = Buffer.from(payloadQueryString).toString('base64')

  // Sign the Base64 encoded payload
  const sig = crypto
    .createHmac('sha256', secret)
    .update(base64Payload)
    .digest('hex')

  // Build the URL to redirect the user to Discourse
  return `${returnSsoUrl}?sso=${encodeURIComponent(base64Payload)}&sig=${sig}`
}

/**
 * Extracts the nonce and return_sso_url parameters from the SSO payload
 *
 * @param sso : string The SSO payload
 */
function getDiscourseParams(sso: string) {
  // Decode the SSO payload from Base64
  const decodedSso = Buffer.from(sso, 'base64').toString('utf-8')

  // Parse the query parameters from the decoded SSO payload
  const queryParams = new URLSearchParams(decodedSso)

  // Extract all the parameters from SSO payload
  return Object.fromEntries(queryParams)
}
