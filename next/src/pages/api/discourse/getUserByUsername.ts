import type { NextApiRequest, NextApiResponse } from 'next'
import process from 'process'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import { sanitizeUsername } from '~/src/features/auth/sanitize'
import { authHeaders } from '~/src/services/discourse/auth'

/**
 * Fetch a user by email from Discourse using the API
 *
 * @param username
 */
async function fetchUserByUsername(username: string) {
  try {
    // Construct the URL with query parameters
    const url = new URL(
      `${process.env.NEXT_PUBLIC_DISCOURSE_URL}/u/${username}.json`,
    )
    url.searchParams.append('show_emails', 'true')

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        ...authHeaders(),
      },
    })

    if (!response.ok) {
      if (response.status === 404) {
        // User not found, return null without logging the error
        return null
      }
      throw new Error(`HTTP error: ${response.status}`)
    }

    const data = await response.json()
    return data.user ? data.user : null
  } catch (error) {
    console.error('Error fetching user by username:', error)
    return null
  }
}

/**
 * Handler to get a user by email
 *
 * @param req
 * @param res
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    const { username } = req.body

    if (!username) {
      return res.status(400).json({ error: 'Username is required' })
    }

    const { lowercase } = sanitizeUsername(username)

    const user = await fetchUserByUsername(lowercase)

    if (!user) {
      return res.status(200).json({})
    }

    // Prepare the user data to be returned
    const userData: UserData = {
      email: user.email,
      name: user.name,
      username: user.username,
    }

    res.status(200).json(userData)
  } catch (error) {
    console.error('Error fetching user by username:', error)
    res.status(500).json({ message: 'Server error' })
  }
}
