import type { NextApiRequest, NextApiResponse } from 'next'

/**
 * Get the user's IP address.
 *
 * @param req
 * @param res
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const forwarded = req.headers['x-forwarded-for']
  const ip =
    typeof forwarded === 'string'
      ? forwarded.split(/, /)[0]
      : req.socket.remoteAddress
  res.status(200).json({ ip })
}
