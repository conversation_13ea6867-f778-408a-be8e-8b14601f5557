import type { NextApiRequest, NextApiResponse } from 'next'
import { createXMLSitemap } from '~/src/features/sitemaps/generator'
import { StoreSitemap } from '~/src/features/sitemaps/utils'
import { saveFileToGCS } from '~/src/services/google-cloud/storage'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return await saveTestFile(req, res)
}

/**
 * Generate and save a test sitemap.
 *
 * @param req
 * @param res
 */
async function saveTestFile(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'POST') return res.status(401).end()

    const { type } = req.body

    if (!type) {
      await doTest()

      res
        .status(200)
        .send({ message: 'Test sitemap generated and uploaded successfully.' })
    }
    await saveSitemapFile(req, res, type)
  } catch (error) {
    console.error('Failed to generate or upload sitemap:', error)
    res.status(500).send({ error: 'Failed to generate or upload sitemap.' })
  }
}

/**
 * Generate and save a sitemap based on the type parameter.
 *
 * @param req
 * @param res
 * @param type
 */
async function saveSitemapFile(
  req: NextApiRequest,
  res: NextApiResponse,
  type: string,
) {
  try {
    switch (type) {
      case 'news':
        await StoreSitemap('getAllNewsArticles', 'news.xml')
        break
      case 'off-the-wire':
        await StoreSitemap('getAllOffTheWire', 'off-the-wire.xml')
        break
      case 'opinions':
        await StoreSitemap('getAllOpinions', 'opinions.xml')
        break
      case 'video':
        await StoreSitemap('video', 'video.xml')
        break
      default:
        return res.status(400).send({ error: 'Invalid type parameter.' })
    }

    res.status(200).send({
      message: `Sitemap for type '${type}' generated and uploaded successfully.`,
    })
  } catch (error) {
    console.error('Failed to generate or upload sitemap:', error)
    res.status(500).send({ error: 'Failed to generate or upload sitemap.' })
  }
}

const doTest = async () => {
  const sitemapUrls = [
    {
      loc: 'https://example.com/news/article-1',
      lastmod: '2021-09-01',
    },
    {
      loc: 'https://example.com/news/article-2',
      lastmod: '2021-09-02',
    },
    {
      loc: 'https://example.com/news/article-3',
      lastmod: '2021-09-03',
    },
  ]
  const xmlContent = createXMLSitemap(sitemapUrls)

  await saveFileToGCS(xmlContent, 'sitemaps/test.xml', 'application/xml', true)
}
