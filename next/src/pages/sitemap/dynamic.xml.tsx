import type { GetServerSideProps } from 'next'
import { getServerSideSitemapLegacy } from 'next-sitemap'
import { markets } from '~/src/components/NavVer2/MarketsItem/MarketsMenu'
import { sector } from '~/src/components/NavVer2/MiningItem/MiningMenu'
import MiningStocks from '~/src/data/Mining/MiningStocks'
import {
  extractDataFromQuery,
  generateUrlObject,
} from '~/src/features/sitemaps/queries'
import { allCryptos } from '~/src/lib/all-cryptos'
import { baseMetals, preciousMetals } from '~/src/lib/metals'
import { sitemap } from '~/src/lib/sitemap'
import { ssrQueries } from '~/src/utils/ssr-wrappers'

/**
 * Generate URLs for all the different sections.
 */
function getSections() {
  return [
    ...allCryptos.map((item) => generateUrlObject(item.href)),
    ...MiningStocks.map((item) =>
      generateUrlObject(`/markets/mining/${item.sectorSymbol}`),
    ),
    ...baseMetals.map((item) =>
      generateUrlObject(`/price/base-metals/${item.name}`),
    ),
    ...markets.map((item) => generateUrlObject(item.as)),
    ...preciousMetals.map((item) => generateUrlObject(`/charts/${item.name}`)),
    ...sector.map((item) => generateUrlObject(item.href)),
  ]
}

/**
 * Get all URLs for the sitemap.
 *
 * @param ctx
 */
export const getServerSideProps: GetServerSideProps = async (ctx) => {
  // Get all URLs from the CMS
  const { dehydratedState } = await ssrQueries({
    queries: [
      sitemap.getAllAuthors(),
      sitemap.getAllTags(),
      // sitemap.getAllLeadGen(),
      sitemap.getAllCategories(),
    ],
  })

  // Generate URLs for all the different sections
  let allUrls = getSections()

  // Process each query result and append their URLs
  if (dehydratedState?.queries) {
    for (const query of dehydratedState.queries) {
      allUrls = allUrls.concat(
        extractDataFromQuery(query.state.data, query.queryKey[0]),
      )
    }
  }

  // Clean invalid URLs
  allUrls = allUrls
    .filter((url) => url?.loc)
    .filter((url) => url.loc !== `${process.env.NEXT_PUBLIC_URL}/opinion`)

  return getServerSideSitemapLegacy(ctx, allUrls)
}

// Default export to prevent next.js errors
export default function Sitemap() {}
