import { signOut } from '@firebase/auth'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import LayoutLanding from '~/src/components/LayoutLanding/LayoutLanding'
import { auth } from '~/src/services/firebase/config'

/**
 * Page for logging out the user.
 *
 * @constructor
 */
export default function Logout() {
  const router = useRouter()

  useEffect(() => {
    signOut(auth)
      .then(() => {
        // Redirect to the login page after logging out.
        router.push('/login')
      })
      .catch((error) => {
        console.error('Error logging out:', error)
      })
  }, [router])

  return (
    <LayoutLanding title={'Logout'}>
      <h1>Login out...</h1>
    </LayoutLanding>
  )
}
