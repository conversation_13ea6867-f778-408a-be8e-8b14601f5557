import type { GetServerSideProps } from 'next'
import type { ParsedUrlQuery } from 'querystring'
import LoginForm, { FormState } from '~/src/components/Auth/Form/LoginForm'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import LayoutLanding from '~/src/components/LayoutLanding/LayoutLanding'

interface IParams extends ParsedUrlQuery {
  data: string
}

interface UserProps {
  user: UserData
}

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
  if (!params || typeof params.data !== 'string') {
    return { props: { userData: {} } }
  }

  const { data } = params as IParams
  const decodedData = Buffer.from(data, 'base64').toString('ascii')
  const userData: UserProps = JSON.parse(decodedData)

  return {
    props: { user: userData }, // We will pass the decoded data to the page component as props
  }
}

export default function RecoverUser({ user }: UserProps) {
  return (
    <LayoutLanding title="Recover your account">
      <LoginForm
        defaultState={FormState.RECOVER_FORUM_USER}
        userInfo={user}
        logo="/kitco_forum_logo.png"
        onSuccess={() => {}}
      />
    </LayoutLanding>
  )
}
