import clsx from 'clsx'
import type { NextPage } from 'next'
import Head from 'next/head'
import { AdvertisingSlot } from 'react-advertising'
import {
  CryptosTableMax,
  cryptosTableMaxVariables,
} from '~/src/components-cryptos/CryptosTable/CryptosTableMax'
import { LatestNewsSidebar } from '~/src/components-news/LatestNewsSidebar/LatestNewsSidebar'
import { PageHeader } from '~/src/components/ChartsTitle/ChartsTitle.component'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import LayoutJewelers from '~/src/components/LayoutJewelers/LayoutJewelers'
import { cryptos } from '~/src/lib/cryptos-factory.lib'
import { news } from '~/src/lib/news-factory.lib'
import { ssrQueries } from '~/src/utils/ssr-wrappers'

export const getServerSideProps = async (ctx) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: ctx.res,
    queries: [
      news.newsByCategoryGeneric({
        variables: {
          limit: 5,
          offset: 0,
          urlAlias: `/news/category/cryptocurrencies`,
        },
      }),
      cryptos.cryptosTable({
        variables: cryptosTableMaxVariables,
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}

const CryptoPricesLandingPage: NextPage = () => {
  return (
    <LayoutJewelers title="Crypto Market Capitalizations, Crypto Prices, Charts and Crypto News | KITCO CRYPTO">
      <Head>
        <meta
          name="description"
          content="Top cryptocurrency live prices and charts, coin market capitalization, crypto charts, data and crypto news"
        />
      </Head>
      <div
        className={clsx(
          'h-full w-full max-w-[1240px]',
          'relative mx-auto px-5 lg:px-2',
        )}
      >
        <PageHeader.Root>
          <PageHeader.Title>CRYPTO</PageHeader.Title>
        </PageHeader.Root>
        <div className={clsx('relative grid gap-6 lg:grid-cols-12')}>
          <div className="relative max-w-full overflow-x-hidden lg:col-span-8">
            <ErrBoundary>
              <CryptosTableMax />
            </ErrBoundary>
          </div>
          <div className="col-span-1 lg:col-span-4">
            <AdvertisingSlot
              id={'right-rail-1'}
              className="mx-auto mb-10 h-[250px] w-[300px] desktop:mb-4 no-print"
            />
            <LatestNewsSidebar category="cryptocurrencies" />
            <AdvertisingSlot
              id={'right-rail-2'}
              className="sticky top-[100px] mx-auto mt-8 h-[600px] w-[300px] desktop:mb-4 no-print no-print"
            />
          </div>
        </div>
      </div>
      <AdvertisingSlot
        id={'footer'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
            z-20
            w-[320px]
            -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
      />
    </LayoutJewelers>
  )
}

export default CryptoPricesLandingPage
