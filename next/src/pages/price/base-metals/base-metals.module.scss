.tabletGridOrder {
  display: grid;
  gap: 1.25em;
  grid-template-columns: 1fr;
  grid-template-areas:
    'qq'
    // "aa"
    // "bb"
    // "cc"
    // "dd"
    'ee'
    'kk'
    'll';

  @media (min-width: 768px) {
    // display: grid;
    // grid-template-columns: 1fr 1fr;
    // grid-template-areas:
    //   "aa qq"
    //   "bb qq"
    //   "ii ii"
    //   "cc gg"
    //   "dd hh"
    //   "ee ee"
    //   "ll ll"
    //   "mm mm"
    //   "nn nn"
    //   "oo oo"
    //   "pp pp";
    display: block;
  }

  @media (min-width: 1270px) {
    display: grid;
    grid-template-columns: 200px minmax(0, 1fr) 300px;
    grid-template-areas: none;
    // overflow-x: hidden;
  }
}

.miningBannerContainer {
  display: none;

  @media (min-width: 1270px) {
    display: block;
  }
}
