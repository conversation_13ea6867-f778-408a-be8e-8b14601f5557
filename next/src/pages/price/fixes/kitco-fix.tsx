import { type FC, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import MarketIndicesCell from '~/src/components-markets/MarketIndicesCell/MarketIndicesCell'
import {
  ControlBar,
  MainPriceBlock,
  MainPriceBorder,
  MainPriceTitle,
  TitleBar,
  ZoneSwitcher,
} from '~/src/components-metals/KitcoFixPageBlocks/KitcoFixPageBlocks'
import { LondonFixSidebarGoldSilverPlatinumPalladium } from '~/src/components-metals/LondonFixCell/london-fix-sidebar-gold-plat-silver-palladium.component'
import { MetalHistoryCell } from '~/src/components-metals/MetalHistoryCell/MetalHistoryCell'
import ShanghaiGold from '~/src/components-metals/ShanghaiGold/ShanghaiGold'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import Layout from '~/src/components/Layout/Layout'
import { Timezones } from '~/src/utils/dates'

const KitcoFix: FC = () => {
  const [zone, setZone] = useState<Timezones>(Timezones.NY)

  const zoneHandler = (z: Timezones) => setZone(z)

  return (
    <Layout title="Kitco Gold and Precious Metals Fix">
      <main className="grid gap-6 desktop:grid-cols-layout-2">
        <div>
          <TitleBar />
          <ZoneSwitcher zone={zone} zoneHandler={zoneHandler} />
          <MainPriceBorder>
            <MainPriceTitle />
            <MainPriceBlock zone={zone} />
            <ControlBar zone={zone} />
          </MainPriceBorder>
          <MetalHistoryCell />
          <KitcoFixInfo />
        </div>
        <div className="flex hidden flex-col gap-6 tablet:block">
          <AdvertisingSlot
            id={'right-rail-1'}
            className={
              'mx-auto mb-8 flex h-[250px] w-[300px] items-center justify-center no-print'
            }
          />
          <LatestNewsCell />
          <ErrBoundary>
            <LondonFixSidebarGoldSilverPlatinumPalladium />
          </ErrBoundary>
          <ShanghaiGold />
          <MarketIndicesCell />
        </div>
      </main>
    </Layout>
  )
}

export default KitcoFix

function KitcoFixInfo() {
  return (
    <div className="px-4 py-12 tablet:px-0">
      <AdvertisingSlot
        id={'banner-2'}
        className="mx-auto mb-8 h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] no-print"
      />
      <header className="flex items-center justify-between border-t-2 border-ktc-desc-gray pb-6 pt-6">
        <h3 className="text-2xl uppercase">
          About the kitco gold and precious metals fix
        </h3>
        <img
          src="/icons/kitco-circle-logo.svg"
          alt="Kitco Circle Logo"
          className="max-h-10"
        />
      </header>
      <div className="flex flex-col gap-4 text-base">
        <p>
          More than a century ago the first precious metals benchmarks were
          created. The fixing process involved a group of bankers sitting in a
          room haggling over the price of gold and silver. When demand matched
          with the supply the fixing price was set.
        </p>
        <p>
          For a 100 years this was the process to establish the benchmark price
          for gold, silver, platinum and palladium. The room was eventually
          replaced with a telephone auction process. However, the financial
          marketplace has since become more and more digital.
        </p>
        <p>
          The world is now in need of an inexpensive, reliable benchmark for
          gold, silver, platinum and palladium. Welcome to the Kitco Gold and
          Precious Metals Fix.
        </p>
        <p>
          The world’s leader in precious metals information is establishing the
          new benchmark for all precious metals. Through the liquid and reliable
          over-the-counter cash market, Kitco Metals is able to calculate a
          reliable fixing price for gold, silver, platinum and palladium.
        </p>
        <p>
          The fixing price will be released every day at 10 am and made
          available for free to Kitco users.
        </p>
      </div>
    </div>
  )
}
