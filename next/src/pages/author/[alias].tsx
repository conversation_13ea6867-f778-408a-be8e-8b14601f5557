import clsx from 'clsx'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import ListItemFourLine from '~/src/components-news/ArticleListItems/ListItemFourLine'
import { TeaserWide } from '~/src/components-news/ArticleTeasers/TeaserWide'
import AuthorPageTitleBlock from '~/src/components-news/AuthorPageTitleBlock/AuthorPageTitleBlock'
import ContributorsCell from '~/src/components-news/ContributorsCell/ContributorsCell'
import ReportersCell from '~/src/components-news/ReportersCell/ReportersCell'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import { urlSafePath } from '~/src/utils/url-safe-path'
import useScreenSize from '~/src/utils/useScreenSize'
import styles from './author-page.module.scss'

export const getServerSideProps = (async (c) => {
  const authorPath = urlSafePath(c.query.alias as string)

  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      news.reporters(),
      news.authorByUrlAlias({
        variables: { urlAlias: `/author/${authorPath}` },
      }),
      news.nodeListByAuthor({
        variables: {
          urlAlias: `/author/${authorPath}`,
          limit: 20,
          offset: 0,
        },
      }),
    ],
  })

  let authorExists = false

  for (const x of dehydratedState?.queries ?? []) {
    if (x.queryKey[0] === 'authorByUrlAlias') {
      if (x.state?.data?.authorByUrlAlias) {
        authorExists = true
      }
      break
    }
  }

  if (!authorExists) {
    return { notFound: true }
  }

  return {
    props: {
      dehydratedState,
      alias: c.query.alias,
    },
  }
}) satisfies GetServerSideProps

const AuthorPage: NextPage<{ alias: string }> = ({ alias }) => {
  const { data: author } = kitcoQuery(
    news.authorByUrlAlias({
      variables: { urlAlias: `/author/${alias}` },
      options: {
        enabled: true,
      },
    }),
  )

  const { params, incrementParams } = useParams(20)
  const { data } = kitcoQuery(
    news.nodeListByAuthor({
      variables: { ...params, urlAlias: `/author/${alias}` },
      options: {
        enabled: true,
      },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.nodeListByAuthor?.items,
    incrementParams,
    total: data?.nodeListByAuthor?.total,
  })

  const { isMobile } = useScreenSize()

  const dataItems = !items.length ? data?.nodeListByAuthor?.items : items

  return (
    <LayoutNewsLanding title={author?.authorByUrlAlias?.name}>
      {author?.authorByUrlAlias?.body && (
        <Head>
          <meta
            name="description"
            content={author?.authorByUrlAlias?.body?.replace(
              /(<([^>]+)>)/gi,
              '',
            )}
          />
        </Head>
      )}
      <div
        className={clsx(
          'mx-auto px-[20px] md:px-10 lg:px-10 xl:px-0',
          'w-full max-w-full',
          'box-border xl:w-[1240px]',
        )}
      >
        {/* main content */}
        <div className="pt-2.5">
          {!author?.authorByUrlAlias ? (
            <AuthorPageTitleBlock loading={true} author={null} />
          ) : (
            <AuthorPageTitleBlock
              author={author?.authorByUrlAlias}
              loading={false}
            />
          )}
        </div>

        <div className="lg:layout-cols-2 block">
          <section className="mr-0 lg:mr-12">
            <h4 className={styles.sectionTitle}>Latest</h4>
            <ul className="pt-8 md:pt-0">
              {!dataItems ? (
                <ListItemFourLine.Loading howMany={5} />
              ) : (
                dataItems.map((node: any) => (
                  <>
                    {isMobile ? (
                      <TeaserWide
                        node={node}
                        size="md"
                        aspectRatio="16x9"
                        key={node.id}
                        hideCategory={true}
                      />
                    ) : (
                      <ListItemFourLine.DataOnTheRight
                        key={node.id}
                        authorName={node.author?.fullName}
                        authorUrlAlias={node.author?.urlAlias}
                        date={node.updatedAt}
                        summary={node.teaserSnippet}
                        image={
                          node.image?.detail?.sources?.desktop?.srcset ??
                          node?.legacyThumbnailImageUrl
                        }
                        hasLegacyThumbnailImageUrl={
                          !!node?.legacyThumbnailImageUrl
                        }
                        source={node.__typename}
                        title={node.title}
                        url={node.urlAlias}
                        categoryUrlAlias={node?.category?.urlAlias}
                        categoryName={node?.category?.name}
                        size="lg"
                      />
                    )}
                  </>
                ))
              )}
            </ul>
            <div ref={ref}>{loading && <div>Loading...</div>}</div>
          </section>
          <div className={clsx('hidden lg:block')}>
            {/* FIX: on mobile, lets try to keep these from rendering so we dont have to make network requests */}
            {/* {!isDesktop ? null : ( */}
            <>
              <ReportersCell />
              <ContributorsCell />
            </>
            {/* )} */}
          </div>
        </div>
      </div>
    </LayoutNewsLanding>
  )
}

export default AuthorPage
