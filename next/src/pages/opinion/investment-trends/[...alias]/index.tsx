import clsx from 'clsx'
import type { GetServerSideProps, NextPage } from 'next'
import Link from 'next/link'
import Script from 'next/script'
import { type FC, Fragment, type ReactNode, useEffect, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { createRoot } from 'react-dom/client'
import Zoom from 'react-medium-image-zoom'
import { FeaturedMedia } from '~/src/components-news/Article/ArticleFeaturedMedia.component'
import ArticleAudioPlayer from '~/src/components-news/ArticleAudioPlayer/ArticleAudioPlayer'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserCardMobile } from '~/src/components-news/ArticleTeasers/TeaserCardMobile'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import AuthorDetails from '~/src/components-news/AuthorDetails/AuthorDetails'
import { NoRelatedArticlesShowLatestNews } from '~/src/components-news/RelatedNews/NoRelatedArticlesShowLatestNews'
import { useVideoPlayer } from '~/src/components-news/VideoPlayer/useVideoPlayer.util'
import ArticlePageEmpty from '~/src/components/Article/ArticlePageEmpty'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { AuthorImage } from '~/src/components/image-with-fallback/image-with-fallback.component'
import { NewsCategoryTitleDetailPage } from '~/src/components/news-category/news-category.component'
import NewsMeta from '~/src/components/news/meta'
import { SocialsKitco } from '~/src/components/socials/socials-kitco.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import { TagLink } from '~/src/components/tag-link/tag-link.component'
import type {
  ArticleTeaserFragmentFragment,
  Author,
  Sponsored,
  Tag,
} from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { articleDate } from '~/src/utils/article-date.util'
import cs from '~/src/utils/cs'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import StrippedString from '~/src/utils/strippedString'
import useRecordView from '~/src/utils/useRecordView'
import useScreenSize from '~/src/utils/useScreenSize'
import styles from './investment-trends-alias.module.scss'

interface ArticleProps {
  articleData: Sponsored
}

interface AuthorProps {
  authorData: Author
  publishDate: string
  updateDate: string
}

export const getServerSideProps: GetServerSideProps = async (c) => {
  const slugs = c.query.alias as Array<string>
  const fullSlug = `/opinion/investment-trends/${slugs.join('/')}`
  const auHash = c?.query?.auHash?.toString() ?? ''

  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      news.nodeByUrlAlias({
        variables: { urlAlias: fullSlug, auHash },
      }),
      news.sponsoredContent({
        variables: {
          limit: 4,
          offset: 0,
        },
      }),
      news.newsTrending({
        variables: { limit: 10 },
      }),
    ],
  })

  for (const x of dehydratedState.queries) {
    const queryKey = x.queryKey.find((item) => item?.urlAlias || false)
    if (queryKey?.urlAlias === fullSlug && !x.state.data.nodeByUrlAlias) {
      return { notFound: true }
    }
  }

  return {
    props: {
      dehydratedState,
      urlAlias: fullSlug,
      auHash,
    },
  }
}

const listAuthorStr = (data: Sponsored) => {
  const authorData = data?.author
  const unifyAuthors: <AUTHORS>

  let listAuthorString = 'By '
  unifyAuthors?.forEach((x, idx) => {
    listAuthorString += `${x?.name}${
      idx !== unifyAuthors?.length - 1 ? ' and ' : ''
    }`
  })

  return listAuthorString
}

const ArticlePage: NextPage<{ urlAlias: string; auHash?: string }> = ({
  urlAlias,
  auHash,
}) => {
  const { data } = kitcoQuery(
    news.nodeByUrlAlias({
      variables: { urlAlias, auHash },
      options: {
        enabled: true,
      },
    }),
  )

  useRecordView(Boolean(data), data?.nodeByUrlAlias?.id)

  const articleData = data?.nodeByUrlAlias as Sponsored

  if (!articleData) return <ArticlePageEmpty />

  const { isMobile } = useScreenSize()

  const pFirst = StrippedString(
    articleData?.bodyWithEmbeddedMedia.value,
  )?.replace('&nbsp;', ' ')

  return (
    <LayoutNewsLanding title={`${data?.nodeByUrlAlias?.title} | Kitco News`}>
      <NewsMeta
        title={data?.nodeByUrlAlias?.title}
        image={
          articleData?.teaserImage?.detail?.default?.srcset ??
          articleData?.image?.detail?.default?.srcset
        }
        description={pFirst}
        authorTwitter={articleData?.author?.twitterId}
      />
      {isMobile && <ArticleMobile articleData={articleData} />}
      {!isMobile && <ArticleDesktopAndTablet articleData={articleData} />}
    </LayoutNewsLanding>
  )
}

export default ArticlePage

const ArticleDesktopAndTablet: FC<ArticleProps> = ({ articleData }) => {
  return (
    <>
      <div className="mx-auto flex w-full max-w-full px-4 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <div className="flex-[0_0_190px]">
          <NewsCategoryTitleDetailPage category="opinion/investment-trends" />
        </div>
        <div className="flex-[1_1_auto] pl-10">
          <h1 className="mb-6 !font-lato text-[34px] font-bold leading-[39px]">
            {articleData?.title}
          </h1>
        </div>
      </div>
      <div className="mx-auto flex w-full max-w-full px-4 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <LeftContent articleData={articleData} />
        <ArticleContent articleData={articleData} />
      </div>
    </>
  )
}

const LeftContent: FC<ArticleProps> = ({ articleData }) => {
  return (
    <div className="w-[190px]">
      <AuthorBlock
        authorData={articleData?.author}
        publishDate={articleData?.createdAt}
        updateDate={articleData?.updatedAt}
      />
      {!!articleData?.source && (
        <SourceBlock
          name={articleData?.source?.name}
          description={articleData?.source?.description}
          subtitle={articleData?.source?.subtitle}
        />
      )}
      <AudioPlayer articleData={articleData} />
      <Spacer className="h-[30px]" />
      <SocialsKitco
        className="mx-3 justify-between"
        hidePrint={true}
        listAuthorStr={listAuthorStr(articleData)}
      />
      <Spacer className="h-[30px]" />
      <AdvertisingSlot
        id={'left-rail-1'}
        className={'mx-auto min-h-[600px] w-[160px] no-print'}
      />
    </div>
  )
}

const ArticleContent: FC<ArticleProps> = ({ articleData }) => {
  return (
    <div className="flex-[1_1] pl-10">
      <article>
        <div className="flex justify-items-end gap-10">
          <div id="main-article-body" className="">
            <FeaturedMedia articleData={articleData} />
            <BodyBlock body={articleData?.bodyWithEmbeddedMedia} />
            <Spacer className="h-6" />
            <AuthorDetails authorData={articleData?.author} />
            {articleData?.tags?.length ? (
              <>
                <Spacer className="h-6" />
                <Tags data={articleData?.tags} />
              </>
            ) : (
              <></>
            )}
            <Spacer className="h-6" />
            <Disclaimer />
            <Spacer className="h-6" />
            {/* <ArticleComment />
            <Spacer className="h-6" /> */}
            <RelatedArticles currentNodeId={articleData?.id} />
          </div>
          <aside
            id="right-sidebar"
            className="hidden flex-[0_0_300px] lg:block"
          >
            <AdvertisingSlot
              id={'right-rail-1'}
              className={'mx-auto mb-5 min-h-[250px] w-[300px] no-print'}
            />
            <div className="rounded-md border border-ktc-borders p-5">
              <TrendingNowSection />
            </div>
          </aside>
        </div>
      </article>
    </div>
  )
}

const ArticleMobile: FC<ArticleProps> = ({ articleData }) => {
  return (
    <div className="mx-auto flex w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
      <div className="w-full">
        <NewsCategoryTitleDetailPage category="opinion/investment-trends" />
        <article>
          <h1 className="mb-6 !font-lato text-[34px] font-bold leading-[39px]">
            {articleData?.title}
          </h1>
          <div className="grid grid-cols-1 gap-10 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <AuthorBlockMobile
                authorData={articleData?.author}
                publishDate={articleData?.createdAt}
                updateDate={articleData?.updatedAt}
              />
              {!!articleData?.source && (
                <SourceBlock
                  name={articleData?.source?.name}
                  description={articleData?.source?.description}
                  subtitle={articleData?.source?.subtitle}
                />
              )}
              <Spacer className="h-[30px]" />
              <div className="flex justify-between">
                <AudioPlayer articleData={articleData} isMobile={true} />
                <SocialsKitco
                  className={cs([
                    'mx-3',
                    articleData?.audioTts?.assetUuid
                      ? 'w-1/2 justify-between'
                      : 'w-full justify-evenly',
                  ])}
                  hidePrint={true}
                  listAuthorStr={listAuthorStr(articleData)}
                />
              </div>
              <Spacer className="h-[30px]" />
              <FeaturedMedia articleData={articleData} />
              <BodyBlock body={articleData?.bodyWithEmbeddedMedia} />
              <Spacer className="h-6" />
              <AuthorDetails authorData={articleData?.author} />
              {articleData?.tags?.length ? (
                <>
                  <Spacer className="h-6" />
                  <Tags data={articleData?.tags} />
                </>
              ) : (
                <></>
              )}
              <Spacer className="h-6" />
              <LowerShareBlock articleData={articleData} />
              <Spacer className="h-6" />
              <Disclaimer />
              <Spacer className="h-6" />
              {/* <ArticleComment />
              <Spacer className="h-6" /> */}
              <RelatedArticles currentNodeId={articleData?.id} />
            </div>
          </div>
        </article>
      </div>
    </div>
  )
}

const AuthorBlock: FC<AuthorProps> = ({
  authorData,
  publishDate,
  updateDate,
}) => {
  const unifyAuthors: <AUTHORS>
  const isMultiAuthor: boolean = unifyAuthors?.length >= 2

  return (
    <>
      <div className="mt-5">
        <div className="flex w-full items-center -space-x-[0.75em] pb-2.5">
          {unifyAuthors?.map((x, idx) => (
            <AuthorImage
              src={x?.imageUrl}
              className={clsx(
                'h-20 w-20 rounded-full object-cover',
                isMultiAuthor ? 'border-2 border-white' : undefined,
              )}
              style={{
                zIndex: 100 - idx,
              }}
              urlAlias={x.urlAlias}
              key={x.id + idx}
            />
          ))}
        </div>
        <div>
          <h6 className="font-medium tracking-[0.0168em]">
            <span>By&nbsp;</span>
            {unifyAuthors?.map((x, idx) => (
              <Fragment key={idx}>
                <Link
                  href={x.urlAlias}
                  className="text-sm font-bold text-[#373737]"
                  key={x.id + idx}
                >
                  {x?.name}
                </Link>
                {idx !== unifyAuthors?.length - 1 ? (
                  <span>&nbsp;and&nbsp;</span>
                ) : null}
              </Fragment>
            ))}
          </h6>
          <Spacer className="h-[30px]" />
          <div>
            <div className="text-xs font-bold text-ktc-desc-gray">
              Published:
            </div>
            <time className="text-xs text-ktc-gray">
              {articleDate(publishDate)}
            </time>
          </div>
          <div>
            <div className="mt-2 text-xs font-bold text-ktc-desc-gray">
              Updated:
            </div>
            <time className="text-xs text-ktc-gray">
              {articleDate(updateDate)}
            </time>
          </div>
        </div>
      </div>
    </>
  )
}

const SourceBlock: FC<{
  name: string
  description: string
  subtitle: string
}> = ({ name, description, subtitle }) => {
  if (!name && !description && !subtitle) return null
  return (
    <>
      <Spacer className="h-[30px]" />
      <div className="flex flex-col items-start border border-gray-300 bg-[#f8f8f8] p-2.5 text-base leading-5">
        <h5>
          <div className="font-bold">{name}</div>
          <div className="font-normal">{subtitle}</div>
        </h5>
        <div
          className="pt-2 text-xs font-normal text-ktc-desc-gray "
          dangerouslySetInnerHTML={{ __html: description }}
        />
      </div>
    </>
  )
}

const AuthorBlockMobile: FC<AuthorProps> = ({
  authorData,
  publishDate,
  updateDate,
}) => {
  const unifyAuthors: <AUTHORS>
  const isMultiAuthor: boolean = unifyAuthors?.length >= 2

  return (
    <div className="flex justify-between">
      <div className="flex w-full -space-x-[0.75em]">
        {unifyAuthors?.map((x, idx) => (
          <AuthorImage
            src={x?.imageUrl}
            className={clsx(
              'h-10 w-10 rounded-full object-cover',
              isMultiAuthor ? 'border-2 border-white' : undefined,
            )}
            style={{
              zIndex: 100 - idx,
            }}
            urlAlias={x.urlAlias}
            key={x.id + idx}
          />
        ))}
        <div className="pl-6">
          <h6 className="text-[16px] font-normal leading-[23px] tracking-[0.0168em]">
            <span>By&nbsp;</span>
            {unifyAuthors?.map((x, idx) => (
              <Fragment key={idx}>
                <Link
                  href={x.urlAlias}
                  className="font-bold text-[#373737]"
                  key={x.id + idx}
                >
                  {x?.name}
                </Link>
                {idx !== unifyAuthors?.length - 1 ? (
                  <span>&nbsp;and&nbsp;</span>
                ) : null}
              </Fragment>
            ))}
          </h6>
          <div>
            <span className="font-normal text-ktc-date-gray">
              Published&nbsp;
            </span>
            <time className="font-normal text-ktc-date-gray">
              {articleDate(publishDate)}
            </time>
          </div>
          <div>
            <span className="font-normal text-ktc-date-gray">
              Updated&nbsp;
            </span>
            <time className="font-normal text-ktc-date-gray">
              {articleDate(updateDate)}
            </time>
          </div>
        </div>
      </div>
    </div>
  )
}

const LowerShareBlock: FC<{ articleData: Sponsored }> = ({ articleData }) => {
  return (
    <div className="flex w-full flex-col items-center gap-3">
      <h3 className="text-[16px] font-bold underline">Share</h3>
      <SocialsKitco
        className="gap-4"
        listAuthorStr={listAuthorStr(articleData)}
      />
    </div>
  )
}

const Tags: FC<{ data: Tag[] }> = ({ data }) => {
  return (
    <div className="rounded-xl border border-ktc-borders p-5">
      <h3 className="font-mulish pb-2 leading-5">
        <span>Tags:</span>
      </h3>
      <div className="flex flex-wrap gap-2">
        {data?.map((t, idx) => (
          <TagLink key={idx} href={t.urlAlias} name={t.name} />
        ))}
      </div>
    </div>
  )
}

const Disclaimer = () => {
  return (
    <div className="mb-10 text-[10px] text-xs leading-[14px] text-ktc-gray">
      <span className="font-bold text-ktc-desc-gray">Disclaimer:&nbsp;</span>
      The views expressed in this article are those of the author and may not
      reflect those of Kitco Metals Inc. The author has made every effort to
      ensure accuracy of information provided; however, neither Kitco Metals
      Inc. nor the author can guarantee such accuracy. This article is strictly
      for informational purposes only. It is not a solicitation to make any
      exchange in commodities, securities or other financial instruments. Kitco
      Metals Inc. and the author of this article do not accept culpability for
      losses and/ or damages arising from the use of this publication.
    </div>
  )
}

function BodyBlock({
  body,
}: {
  body: Sponsored['bodyWithEmbeddedMedia']
}): ReactNode {
  const [scriptData, setScriptData] = useState<HTMLScriptElement[]>([])
  const { isTablet, isDesktop } = useScreenSize()

  useVideoPlayer({
    multiple: body.embeddedMedia?.map((x) => ({
      assetUuid: x?.assetUuid,
      startTime: x?.startTime,
      endTime: x?.endTime,
      snippetUuid: x?.snippetUuid,
      thumbnailUuid: x?.thumbnailUuid,
      assetType: 'video',
      manuallyCreateParentNodes: true,
    })),
  })

  useEffect(() => {
    const parent = document.querySelectorAll('#articleBody p')

    const elementHasImg = Array.from(parent).filter((item) => {
      const img = item.querySelectorAll('img')

      if (img.length === 0) return false

      return item
    })

    for (const item of elementHasImg) {
      item.outerHTML = item.outerHTML.replaceAll('\n', '')
    }
  }, [body])

  //  Sometimes users add script tags in the WYSIWYG editor, for example Twitter Embeds.
  //  ReactHtmlParser will not parse script tags, so we must do that separately.
  //  Traditionally, this was done creating and injecting <script> tags, however this
  //  caused an issue where the script would run before the html was available, even when
  //  respecting the original scripts async and defer properties.  By using nextJS's <Script />
  //  component, we are able to respect React LifeCycles which solves this issue.
  useEffect(() => {
    if (typeof window === 'undefined') return
    //create an element and add the bodyHTML so that we can parse out script tags
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = body?.value
    const scripts = tempDiv.querySelectorAll(
      'script',
    ) as unknown as HTMLScriptElement[]

    if (scripts) {
      setScriptData([...scripts])
    }
  }, [body])

  useEffect(() => {
    if (typeof window === 'undefined' || (!isTablet && !isDesktop)) return

    // // select all images in the article body with attribute ckeditor-kitco-image
    const images = document.querySelectorAll('img[ckeditor-kitco-image]') // img[ckeditor-kitco-image]
    for (const image of images) {
      const uuid = image.getAttribute('data-uuid')
      const className = `zoom-element-${uuid}`
      image.outerHTML = `<div class='${className}'>`
      const root = createRoot(document.getElementsByClassName(className)[0])
      root.render(
        <Zoom key={uuid} classDialog="custom-zoom" zoomMargin={50}>
          <img
            id={image.id}
            src={`https://storage.googleapis.com/kitco-image-prod/${uuid}`}
            alt={'teaser'}
            className={clsx('rounded-lg', 'preview-image mb-2.5 block w-full')}
          />
        </Zoom>,
      )
    }
  }, [body, isTablet, isDesktop])

  return (
    <>
      <div
        className={cs([
          'relative text-base',
          styles.articleBodyStyles,
          styles.articleWrapper,
        ])}
        id="articleBody"
        dangerouslySetInnerHTML={{ __html: body?.value }}
      />
      {scriptData?.map((element, index) => {
        //  iterate over each script tag which was found, and create a nextJs <Script/> component instead
        //  note that "lazyOnLoad' strategy works well for Twitter embeds, but if this causes issues with
        //  other use cases, perhaps we should use "afterInteractive" (the default) if the original script
        //  tag has the "defer" property set
        return (
          <Script
            key={`script-${index}`}
            id={element?.id}
            src={element?.src}
            strategy={'lazyOnload'}
          />
        )
      })}
    </>
  )
}

const TrendingNowSection: FC = () => {
  const { data } = kitcoQuery(
    news.newsTrending({
      variables: { limit: 10 },
    }),
  )
  return (
    <div className="flex flex-col">
      <h3 className={'border-b border-ktc-borders pb-2.5 text-[20px]'}>
        <span>Trending News</span>
      </h3>
      <div className="flex flex-grow flex-col justify-between">
        {data?.nodeListTrending
          ?.slice(0, 5)
          .map((x: ArticleTeaserFragmentFragment) => {
            return (
              <div className="mt-5 flex" key={x.id}>
                <TeaserTextOnly
                  key={x?.id}
                  node={x}
                  hideSummary={true}
                  size={'sm'}
                />
              </div>
            )
          })}
      </div>
    </div>
  )
}

const AudioPlayer: FC<{
  articleData: ArticleProps['articleData']
  isMobile?: boolean
}> = ({ articleData, isMobile = false }) => {
  if (!articleData?.audioTts?.assetUuid) return

  if (isMobile) {
    return (
      <div className="w-1/2">
        <ArticleAudioPlayer
          assetSnippetUuid={articleData?.audioTts?.assetUuid}
        />
      </div>
    )
  }

  return (
    <>
      <Spacer className="h-[30px]" />
      <ArticleAudioPlayer assetSnippetUuid={articleData?.audioTts?.assetUuid} />
    </>
  )
}

const RelatedArticles: FC<{ currentNodeId: number }> = ({ currentNodeId }) => {
  const { data } = kitcoQuery(
    news.sponsoredContent({
      variables: {
        limit: 4,
        offset: 0,
      },
    }),
  )

  const filterCurrentFromData = () => {
    return data?.nodeList?.items
      ?.filter((x: ArticleTeaserFragmentFragment) => x.id !== currentNodeId)
      .slice(0, 3)
  }

  const { isMobile } = useScreenSize()

  const articles = filterCurrentFromData()
  const ShowTeaserCard = () => {
    return (
      <>
        {articles?.map((article: ArticleTeaserFragmentFragment) => {
          return !isMobile ? (
            <TeaserCard key={article.id} node={article} size="sm" />
          ) : (
            <TeaserCardMobile key={article.id} node={article} size="sm" />
          )
        })}
      </>
    )
  }

  const classTeaserCard = () => {
    if (isMobile) return 'grid grid-row-3 gap-10'

    return 'grid grid-cols-3 gap-10'
  }

  return (
    <aside>
      <h2 className="font-mulish mb-5 border-b border-ktc-borders pb-2.5 text-2xl">
        Related Articles
      </h2>
      {!articles?.length ? (
        <NoRelatedArticlesShowLatestNews
          currentNodeId={currentNodeId}
          classTeaserCard={classTeaserCard()}
        />
      ) : (
        <div className={classTeaserCard()}>
          <ShowTeaserCard />
        </div>
      )}
    </aside>
  )
}
