@import '../../../../styles/article.scss';

.articleBodyStyles {
  & p {
    margin: 1em 0;
  }

  & blockquote {
    & p {
      margin: 0;
    }
  }

  & p:has(iframe) {
    width: 100%;
    padding-top: 56.25%;
    position: relative;
  }

  & p iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

.articleBulletNews {
  margin-top: -10px;

  font-size: 18px;
  line-height: 110%;

  &::before {
    content: '';
    border-color: transparent #111;
    border-style: solid;
    border-width: 0.35em 0 0.35em 0.45em;
    display: block;
    width: 5px;
    height: 10px;
    left: -1em;
    top: 0.9em;
    position: relative;
  }
}

.articleWrapper {
  @include articleWrapper;
}
