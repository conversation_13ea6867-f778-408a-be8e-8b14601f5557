import clsx from 'clsx'
import LayoutNoTopAdvertisement from '~/src/components/LayoutNoTopAdvertisement/LayoutNoTopAdvertisement'
import styles from './terms.module.scss'

export default function TermsOfUse() {
  return (
    <LayoutNoTopAdvertisement title="Terms of use">
      <div className={clsx(styles.WordSection1)}>
        <Break />
        <Break />
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '6.0pt',
            textAlign: 'center',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '18.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            Kitco Metals Inc. Terms of Use
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'center',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '18.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            Disclaimer and terms of use for Kitco Media &amp; Information
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{ marginBottom: '.0001pt', lineHeight: '16.0pt' }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
            }}
          >
            January 3, 2024
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            1. General
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginTop: '0pt',
            marginRight: '0cm',
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The following Website Terms of Use shall govern your use of all
            websites operated by Kitco Metals Inc. (“Kitco”), including, without
            limitation,{' '}
          </span>
          <span lang="EN-US">
            <a href={process.env.NEXT_PUBLIC_URL}>
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                }}
              >
                www.kitco.com
              </span>
            </a>
          </span>
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            , gold-forum.com, kitcosilver.com, kitcometals.com, corp.kitco.com,
            online.kitco.com, applications.kitco.com and corp.kitco.com
            (referred to collectively herein as the “Websites”).&nbsp;&nbsp;By
            accessing and using the Websites, you accept and agree, without
            limitation, to be bound by the Website Terms of Use.&nbsp;&nbsp;If
            you do not accept these Website Terms of Use, you may not use the
            Website or any of the products or services provided or made
            available therein.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
            }}
          >
            &nbsp;
          </span>
        </p>

        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Website Terms of Use apply when accessing the Website by any
            means, including via computer, mobile device or other
            technology.&nbsp;&nbsp;In addition, the Website Terms of Use apply
            when accessing, using or downloading any information, products,
            software, applications or services available on the Websites
            (collectively referred to herein as the “Services”).
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{ marginBottom: '0pt', lineHeight: '16.0pt' }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Subject to and in compliance with the terms and conditions set forth
            herein, Kitco may provide its users with access via the Websites to:
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Free content including articles, blogs and forums, emails, podcasts,
            webinars and conference calls written by our contributors and
            columnists, (the &quot;Free Service&quot;), and free content written
            or provided by third parties which may include, without limitation,
            text, data, graphics, photographs, videos, or downloadable code or
            programs (&quot;Third-Party Content&quot;); and
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Premium content including blogs and forums written by our
            contributors and columnists, Third-Party Content, emails, podcasts,
            webinars and conference calls written by our contributors and
            columnists, Third-Party Content and User-Generated Content for which
            You must pay a subscription fee (or in some cases a one-time fee) to
            access (the &quot;Premium Service&quot;).
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            A chatbot service designed to assist users by providing automated
            responses to inquiries about Kitco’s products and services. The
            chatbot service is intended to enhance user experience and provide
            prompt, automated assistance. (the &quot;Chatbot Service&quot;).
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoNormal, styles.ml38)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Premium Service, the Free Service and the Chatbot Service are
            collectively referred to herein as the &quot;Services&quot;.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Website Terms of Use must be read together with any other terms,
            conditions, policies, legal notices and disclaimers on the Websites,
            including, without limitation, the&nbsp;
          </span>
          <span lang="EN-US">
            <a href="https://online.kitco.com/help/terms_and_conditions.html">
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                }}
              >
                Online Store: Terms and Conditions
              </span>
            </a>
          </span>
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            , which govern transactions between Kitco and its customers on
            Kitco’s Online Store.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You also understand and agree that the Services may include certain
            communications from Kitco, such as service announcements and
            administrative messages that You may not be able to opt out of
            receiving.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <SectionTitle>2. Risk</SectionTitle>
        <SectionSubTitle>
          2.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>No Advice</u>
        </SectionSubTitle>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The information provided in the Websites and in any of the Services
            is for informational purposes only and is not intended as any form
            of advice, whether legal, accounting, investment, financial or tax
            advice.&nbsp;&nbsp;Therefore, it cannot be relied upon as such.
            Should you require such advice, contact a licensed professional.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You understand that no content published as part of the Services
            constitutes a recommendation that any particular action, investment,
            security, portfolio of securities, transaction or investment
            strategy is suitable for any specific person. You understand that
            the views expressed in the Services are the authors&apos; own
            opinions, that trading in investments involves risk and volatility
            and that past results are not necessarily indicative of future
            performance. The Services may contain opinions which may differ from
            or contradict those in another portion of the Services.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You understand and agree that, from time to time, one or more
            Contributors or their affiliates may have a position in precious
            metals written about.&nbsp; In addition, certain of Kitco’s
            affiliates and employees may, from time to time, have long and short
            positions in, or buy or sell precious metals, precious metals
            related securities, or derivatives thereof, of companies mentioned
            in respective Services and may take positions inconsistent with the
            views expressed.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco makes no representations regarding Third-Party or User
            Generated Content, nor is Kitco liable for Third-Party or User
            Generated Content.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You understand that any data is supplied by sources believed to be
            reliable, that any calculations made using such data are not
            guaranteed by these sources, the information providers, or any other
            person or entity, and may not be complete.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            From time to time, reference may be made to prior articles and
            opinions published on the Websites. These references may be
            selective, may reference only a portion of an article or
            recommendation, and may not to be current. As markets change
            continuously, previously published information and data may not be
            current and should not be relied upon.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <SectionSubTitle>
          2.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Market Risk</u>
        </SectionSubTitle>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Investments in precious metals and financial markets involve risk to
            investment principal and can be volatile. Purchasing precious metals
            often involves a degree of risk that makes them unsuitable for
            certain individuals. You should carefully consider the suitability
            of precious metals as a personal financial choice before taking any
            decisions that may affect your situation.&nbsp;&nbsp;Precious metal
            products and accounts are not insured by the Canadian Deposit
            Insurance Corporation, Canadian Investor Protection Fund or other
            similar program and may lose value. You should always conduct your
            own research and due diligence and obtain professional advice before
            making any investment decision.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            3. Exclusion of Warranties
          </span>
        </p>
        <SectionSubTitle>
          3.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Information on Websites</u>
        </SectionSubTitle>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The information, material and content provided on the Websites or in
            any of the Services (referred to collectively herein as the
            “Content”) is provided on an &quot;as is&quot; basis and your use of
            the Websites and the Services is entirely at your own
            risk.&nbsp;&nbsp;Kitco makes no warranties of any kind with respect
            to the Content provided on the Websites and by the Services, whether
            express or implied, including, but not limited to, warranties as to
            usefulness, accuracy, completeness, reliability, fitness for a
            particular purpose, and non-infringement.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <SectionSubTitle>
          3.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Availability</u>
        </SectionSubTitle>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco does not represent or warrant that the Websites or any of the
            Services available or obtained therein will be provided on an
            uninterrupted basis, and that there will be no delays, difficulties
            in use, errors, security breaches, omissions or loss of transmitted
            information.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco makes no representations or warranties that the Websites or
            Services are appropriate or available for use in all jurisdictions
            outside Canada.&nbsp;&nbsp;Please be aware of the applicable laws
            and regulations of your country.
          </span>
        </p>
        <Break />
        <SectionSubTitle>
          3.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Chatbot Service</u>
        </SectionSubTitle>
        <Text>
          By interacting with Kitco&apos;s Chatbot Service, you hereby agree to
          abide by the terms and conditions stipulated in this document. The
          Service is provided exclusively for informational purposes and is not
          a substitute for professional advice. It is important to note that the
          chatbot’s responses are generated via automated processes, and Kitco
          cannot guarantee they are accurate, complete, or current.
          Consequently, Kitco expressly disclaims all warranties regarding the
          chatbot’s responses, including but not limited to any implied
          warranties of merchantability or fitness for a particular purpose. You
          acknowledge that reliance on any information provided by the Chatbot
          Service should be complemented with independent verification. Kitco
          will not be held liable for any inaccuracies, errors, or
          misinformation in the chatbot&apos;s responses, nor for any direct,
          indirect, incidental, or consequential damages resulting from your use
          of the Service. By continuing to use the Chatbot Service, you signify
          your acceptance of these terms, including any disclaimers or
          limitations of liability therein.
        </Text>

        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <SectionTitle>4. Limitation of Liability</SectionTitle>
        <SectionSubTitle>
          4.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Confidentiality &amp; Security</u>
        </SectionSubTitle>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco adheres to the highest security measures to ensure your data
            is protected against theft, loss, and corruption, and against the
            misuse and alteration of any of your data stored on our
            servers.&nbsp;&nbsp;However, when you access your account via a
            public or unsecured computer terminal or if you chose to share your
            account username and password (“Electronic Identification
            Information”), Kitco cannot guarantee the security of your data.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco respects your privacy&nbsp;(see &nbsp;
          </span>
          <a href="https://online.kitco.com/help/privacy_policy.html">
            <span
              style={{
                fontSize: '12.0pt',
                fontFamily: '"Mulish",serif',
                color: '#95B3D7',
                textDecoration: 'underline',
              }}
            >
              Kitco&apos;s privacy policy
            </span>
          </a>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            ).&nbsp;&nbsp;However, unprotected communications over the Internet,
            such as via email or cellular phones, are not confidential or
            secure, may be intercepted, lost, or altered.&nbsp;&nbsp;We highly
            recommend that you not include private and sensitive information in
            any unprotected communications with Kitco, including, but not
            limited to, account numbers, balances, passwords, Electronic
            Identification Information, etc.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco specifically disclaims any liability with respect to any email
            communications, text messaging, or other like unprotected
            communications, whether initiated by you or Kitco.&nbsp;&nbsp;Kitco
            will not be liable or responsible for any damages suffered in
            connection with such modes of communication.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <SectionSubTitle>
          4.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>No Liability</u>
        </SectionSubTitle>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco shall not be liable under any circumstances whatsoever for any
            loss or damage, including any direct, indirect, special, incidental,
            consequential or exemplary damages or for any indirect or punitive
            losses or damages (including lost profits or lost savings), arising
            out of or in connection with the Websites, or the Services; with
            your use of or inability to use or access the Websites or the
            Services; or with links to other third party sites, whether or not
            caused by the fault or neglect of Kitco and whether or not Kitco had
            knowledge that such losses or damages might be incurred.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>

        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            4.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Force Majeure</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco shall not be liable for any failure to perform its obligations
            hereunder due to fire, computer viruses, network failure, computer
            hardware failure, explosion, flood, lightning, act of terrorism,
            war, rebellion, riot, sabotage, orders or requests of any government
            or any other authority, legislative changes, strikes, lockouts or
            other labor disputes, or events or circumstances beyond its
            reasonable control, but Kitco shall use commercially reasonable
            endeavors to minimize dangers or losses to you as a consequence of
            such events.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            5. Registration and Privacy
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            In order to gain access to portions of the Websites and the
            Services, You must become a subscriber by providing your email
            address and choosing a unique user name or, member name and
            password. Kitco may refuse to grant You a user name or member name
            that is threatening, abusive, offensive, harassing, derisive,
            defamatory, vulgar, obscene, libelous, invasive of another&apos;s
            privacy, impersonates someone else, is or may be protected by
            trademark or proprietary rights law, or is hatefully, racially,
            ethnically or otherwise objectionable, or inappropriate, as
            determined by us in our sole discretion. In consideration of Your
            use of the Website and the Services, You represent that You are of
            legal age to form a binding contract and are not a person barred
            from accessing the Website or the Services under the laws of the
            Province of Quebec or other applicable jurisdiction. You also agree
            to: (a) provide true, accurate, current and complete information
            about yourself as prompted by the Services&apos; registration form
            and (b) maintain and promptly update Your registration data to keep
            it true, accurate, current and complete.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Our policy with respect to the collection and use of Your personal
            information is set forth in our&nbsp;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            <a href="https://online.kitco.com/help/privacy_policy.html">
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                }}
              >
                Privacy Policy
              </span>
            </a>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            .
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            As a member, You also have certain other obligations relating to
            Your account:
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You may not transfer to or resell Your use of or access to the
            Services to any third party;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You are responsible for all activities that occur under Your
            account;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            A maximum of five simultaneous log-ins are allowed per account. This
            simultaneous login limit applies to the total number of logins at
            any given time regardless of device type (i.e., computers, tablets,
            mobile devices, etc.);
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You are responsible for maintaining the confidentiality of Your user
            name, member name and password; and
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You agree to notify us&nbsp;if You become aware of any possible
            unauthorized use(s) of Your account or any possible breach of
            security, including loss, theft, or unauthorized disclosure of Your
            user name, member name or password. Contact details for our Customer
            Service center may be found on our&nbsp;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            <a href="https://corp.kitco.com/contact-us.html">
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                }}
              >
                Contact Us
              </span>
            </a>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;page.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            6. Subscription, Renewal and Cancellation
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            6.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>The subscription process</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco offers subscriptions to the Services. Kitco reserves the right
            to vary the Services that it provides to Subscribers at any time.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco agrees to process your Subscription promptly but does not
            guarantee that your Subscription will be activated by any specified
            time. By submitting your payment and other subscription details, you
            are making an offer to us to buy a Subscription. Your offer will
            only be accepted by us and a contract formed when we have
            successfully verified your payment and contact details, at which
            point we will provide you with access to the Subscription. You will
            receive written confirmation when your Subscription offer has been
            accepted (beginning the fulfilment of a Subscription does not
            signify acceptance).
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco reserves the right to cancel any order at any time by
            contacting You and refunding You in full. If your credit card is
            debited and we ultimately refuse your order, Kitco will refund you
            any amount debited.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginLeft: '45pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Site is currently enabled only to accept orders in English.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            6.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Payment and pricing</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The price to be paid for the Premium Service will be made clear to
            you on the Premium Service order pages, or otherwise during the
            order process, and may vary from time to time. It is however
            possible, despite our efforts, that the Premium Service could be
            incorrectly priced. You agree to pay the correct rates at the time
            you purchase the Premium Service. We will inform you in advance of
            any increase in the price of your Subscription and offer you an
            opportunity to cancel it if you do not wish to pay the new price.
            You will be responsible for paying any sales taxes or customs duties
            that apply.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            When you purchase the Premium Service, you must provide us with
            complete and accurate payment information. By submitting payment
            details you confirm that you are entitled to use those payment
            details for this purpose. If we do not receive payment
            authorization, we may immediately terminate or suspend your access
            to the Premium Service. If your access to the Premium Service is
            terminated by Kitco, you will be entitled to receive a refund of any
            amounts which remain unused at the time of termination unless such
            access is terminated because you are in breach of these Terms (as
            determined solely by Kitco). You will continue to be responsible for
            any fees or other charges you have incurred prior to such
            termination. You are also responsible for the payment of fees or
            charges that may be charged by your bank or credit card provider. If
            you are entitled to a refund under these Terms, refunds can only be
            made to the credit card that was used for the original purchase,
            unless it has expired in which case we will contact you.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Upon the commencement of Your initial subscription (which occurs at
            the expiration of Your free trial, or
            if&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; You are not receiving a free
            trial, upon Your registration for a subscription) Your credit card
            will be billed the applicable subscription fee and Your paid
            subscription will commence.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Contact details for our Customer Service center may be found on
            our&nbsp;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            <a href="https://corp.kitco.com/contact-us.html">
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                }}
              >
                Contact Us
              </span>
            </a>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;page. All promotional offers are limited to one redemption per
            customer unless expressly stated.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            6.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Cancellation</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            YOU SHOULD READ THESE TERMS CLOSELY BEFORE YOU SUBSCRIBE FOR A
            PREMIUM SERVICE.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Unless specifically stated, you have the right to cancel your
            Subscription at any time. If you exercise your right to cancel,
            unless the terms of any Subscription offer or promotion state
            otherwise, we will reimburse all payments received from you, less a
            pro-rated amount for each day of Subscription used before you
            cancelled. During the free-trial portion of Your subscription, if
            any, You may cancel at any time and not be billed.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You can cancel your Subscription&nbsp;online&nbsp;or call our
            Customer Service center.&nbsp;&nbsp;Contact details&nbsp;for our
            Customer Service center may be found on our&nbsp;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            <a href="https://corp.kitco.com/contact-us.html">
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                }}
              >
                Contact Us
              </span>
            </a>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;page.&nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            6.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Renewal</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            YOU SHOULD READ THESE TERMS CLOSELY BEFORE YOU SUBSCRIBE FOR A
            PREMIUM SERVICE.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Unless specifically stated when you order your Subscription, you
            agree that your Subscription will continue for the period referenced
            at the time of order, at the end of the initial subscription period
            (and of each renewal period thereafter), renew for the same
            subscription period (the “Term”) at the renewal rate agreed at the
            point of order. You may cancel your Subscription at any time as set
            out above.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            If You do not notify us as described above, the then-applicable
            monthly, periodic or annual fee for Your subscription will be billed
            automatically to the credit card account You designated during the
            registration process (or subsequently changed).
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            In connection with recurring billing for subscription renewals, You
            authorize Kitco to bill Your credit card account number, regardless
            of whether the other information associated with Your subscription
            has changed, including but not limited to the expiration date of the
            physical card with which You subscribed. We may receive updated
            information about Your account from the financial institution
            issuing Your credit card. You agree to pay all fees and charges
            incurred in connection with Your user name, member name and password
            (including any applicable taxes) at the rates in effect when the
            charges were incurred. You agree to pay all amounts due upon our
            demand.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            7. Your Obligations and Liability
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            7.1.&nbsp;&nbsp;&nbsp;&nbsp;<u>Compliance</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You are responsible for complying with all laws of the jurisdiction
            from which you access the Websites or any of the Services, and you
            shall at all times be solely responsible for obtaining any
            authorizations required by any authoritative body in such
            jurisdiction.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            7.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Use</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You agree to use the Websites and the Services only for purposes
            that are permitted by the Website Terms of Use, as well as any other
            terms, conditions, policies, legal notices and disclaimers to which
            the Services may be Subject.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You are solely responsible for the User Generated Content You post.
            You must comply with any rules posted by the Websites and the
            Services.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Websites and the Services may include article submission and
            comments capability, bulletin boards, discussion groups and other
            public areas or features that allow feedback to the Websites and the
            Services, and interaction between users. While Kitco does not
            control the information/materials posted by users (the &quot;User
            Generated Content&quot;), it reserves the right (which it may
            exercise at its sole discretion without notice) to delete, move or
            edit the User Generated Content and to terminate your access to and
            use of the Websites and the Services.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            In addition You agree not to:
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post, link to or otherwise publish any User Generated Content
            containing material that is obscene, racist, homophobic or sexist or
            that contains any form of hate speech;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post, link to or otherwise publish any User Generated Content that
            infringes copyright;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post, link to or otherwise publish any User Generated Content that
            is illegal, libelous, defamatory or may prejudice ongoing legal
            proceedings or breach a court injunction or other order;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post, link to or otherwise publish any User Generated Content that
            is abusive, threatening or make any form of personal attack on
            another user or an employee of Kitco;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post User Generated Content in any language other than English;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post the same User Generated Content, or a very similar User
            Generated Content, repeatedly;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post or otherwise publish any User Generated Content unrelated to
            the Websites and the Services;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post, link to or otherwise publish any User Generated Content
            containing any form of advertising or promotion for goods and
            services or any chain User Generated Content or &quot;spam&quot;;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post, link to or otherwise publish any User Generated Content with
            recommendations to buy or refrain from buying a particular security
            or which contain confidential information of another party or which
            otherwise have the purpose of affecting the price or value of any
            security;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Disguise the origin of any User Generated Content;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Impersonate any person or entity (including Kitco employees or
            contributors or columnists) or misrepresent any affiliation with any
            person or entity;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Post or transmit any User Generated Content that contains software
            viruses, files or code designed to interrupt, destroy or limit the
            functionality of the Site or any computer software or equipment, or
            any other harmful component;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Collect or store other users&apos; personal data; and/or
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Restrict or inhibit any other visitor from using the Websites or the
            Services, including, without limitation, by means of
            &quot;hacking&quot; or defacing any portion of any of our websites;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Use the Websites or the Services for any unlawful purpose;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Modify, adapt, sublicense, translate, sell, reverse engineer,
            decompile or disassemble any portion of the Websites or the Services
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &quot;Frame&quot; or &quot;mirror&quot; any content available
            through the Websites or the Services&nbsp; without our prior written
            authorization;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Use any robot, spider, site search/retrieval application, or other
            manual or automatic device or process to download, retrieve, index,
            &quot;data mine&quot;, or in any way reproduce or circumvent the
            navigational structure or presentation of the content available
            through the Websites or the Services;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Harvest or collect information about users of the Websites or the
            Services without their express consent.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You also agree that at all times You will:
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Comply with all applicable laws, rules and regulations in connection
            with Your use of the Websites and the Services and the content made
            available therein;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Be responsible for all statements made and acts or omissions that
            occur on Your user account while Your password is being used;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Waive any and all rights against Kitco and hold Kitco harmless in
            connection with any claims relating to any action taken by Kitco as
            part of its investigation of a suspected violation or result of its
            conclusion that a violation of the term of use has occurred,
            including but not limited to the removal of posts or a suspension or
            termination of Your membership account; or
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml65, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Maintain and promptly update Your registration data to keep it true,
            accurate, current and complete.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>

        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginLeft: '45pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco has no control over individuals posting User Generated Content
            on any of the Websites or the Services. Kitco cannot guarantee the
            accuracy, integrity or quality of any User Generated Content. Some
            users may breach these terms and post User Generated Content that is
            misleading, untrue or offensive. You must bear all risk associated
            with your use of the Websites and the Services and should not rely
            on the Websites and the Services when you make (or refrain from
            making) any specific investment or other decision.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            It is not possible for Kitco to fully and effectively monitor
            infringement of third-party rights in User Generated Content. If you
            believe that any content infringes your legal rights, you should
            notify Kitco immediately by contacting our customer service center
            by using the &quot;Report Abuse&quot; function provided. Repeated
            misuse of the “Report Abuse&quot; function will result in your
            access to the Websites and Services being terminated.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            By submitting User Generated Content to the Websites and the
            Services you are granting Kitco a perpetual, irrevocable, royalty
            free non-exclusive license to reproduce, modify, translate, make
            available, distribute and sub-license the User Generated Content in
            whole or in part, and in any form. This may include personal
            information such as your user or pen name and your expressions of
            opinion. You waive any moral rights that you may have in regard to
            the User Generated Content that you submit.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            7.3.&nbsp;&nbsp;&nbsp;&nbsp;
            <u>Protection of Electronic Identification Information</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            It is your obligation to ensure that your Electronic Identification
            Information is kept secret.&nbsp;&nbsp;You hereby agree to keep your
            Electronic Identification Information and all components thereof
            secret and safe to prevent unauthorized use.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            7.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Customer Liability</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco will be under no obligation to confirm the actual identity or
            authority of any user of the Electronic Identification Information
            or any component thereof.&nbsp;You must contact Kitco immediately if
            a transaction or balance recorded in an account is incorrect or if
            you suspect unauthorized use of your Electronic Identification
            Information. Kitco will not be held liable should you fail to
            disclose any unauthorized use of Electronic Identification
            Information and your Kitco accounts.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You will not be responsible for any unauthorized use of Electronic
            Identification Information occurring after notifying Kitco of the
            suspected unauthorized use.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            7.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Indemnification</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You agree to defend, indemnify and hold harmless Kitco, its
            affiliates and related companies and each of their respective
            officers, directors, employees, consultants and agents, from and
            against any and all claims, liabilities, expenses, actions or
            demands, including without limitation reasonable legal and
            accounting fees and expenses, arising from or related to: (a) your
            breach of any of these Website Terms of Use or&nbsp;any other terms,
            conditions, policies, legal notices and disclaimers on the Websites;
            (b) your access or use of the Websites or Services; or (c) your use
            or reliance on, or publication, communication, transmission or
            distribution of the Content or Services.&nbsp;&nbsp;Kitco reserves
            the right, at its own expense, to assume the exclusive defense and
            control of any matter otherwise subject to indemnification by
            you.&nbsp;&nbsp;You agree to co-operate as fully as reasonably
            required in the defense of any claim.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            8. Intellectual Property
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginLeft: '20pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            All of the intellectual property rights including without limitation
            trademarks, service marks, trade names, copyright and other rights
            used or embodied in the Websites or in any of the Services are and
            will remain the sole property of Kitco (or its suppliers where
            applicable).
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            All Content supplied by Kitco, constitutes part of Kitco&apos;s
            confidential and proprietary information.&nbsp;&nbsp;In accessing
            the Websites or any of the Services, it is strictly prohibited to
            copy, reproduce, republish, store, retransmit, alter, modify,
            distribute, make public use thereof, create derivative works from,
            reverse engineer, disassemble or try to locate source code, of the
            Content, and any software or applications used by Kitco in
            connection with the Websites or any of the Services, except as
            explicitly otherwise authorized by Kitco.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Because we host User-Generated Content as a part of portions of the
            Websites or the Services and therefore redistribute User-Generated
            Content You give us, we need to obtain certain rights in those
            materials. By posting, sending or transmitting to us User-Generated
            Content, You grant us and our designees a worldwide, non-exclusive,
            sub-licensable (through multiple tiers), assignable, royalty-free,
            perpetual, irrevocable right to use, reproduce, distribute (through
            multiple tiers), create derivative works of, publicly perform,
            publicly display, digitally perform, make, have made, sell, offer
            for sale and import such User-Generated Content in any media now
            known or hereafter developed, for any purpose whatsoever, commercial
            or otherwise, without compensation to the provider of the
            User-Generated Content. None of the User-Generated Content disclosed
            in any part of the Websites or the Services shall be subject to any
            obligation, whether of confidentiality, attribution, or otherwise,
            on our part and we shall not be liable for any use or disclosure of
            any User-Generated Content.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            If You choose to submit content for publication through the
            Services, such as guest commentary or guest opinions, such content
            shall be exclusive to Kitco, deemed to be property of Kitco, and by
            submitting such content You irrevocably assign any and all rights to
            such content to Kitco.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &quot;Kitco.com&quot;, &quot;forum.kitco.com&quot;,
            &quot;corp.kitco.com,
            &quot;kitcosilver.com&quot;,&quot;kitcometals.com&quot;,
            &quot;online.kitco.com&quot;, &quot;applications.kitco.com&quot;,
            &quot;Kitco Global Index&quot;, &quot;KGX&quot;, &quot;Kitco Silver
            Fix&quot;, the &quot;Kitco Logo&quot;, &quot;Kitco News&quot; ,
            &quot;Scrapit&quot;, &quot;Metalynx&quot;, and certain other marks
            used on Kitco Portfolio of Websites are trademarks and/or service
            marks of Kitco. All other trademarks, service marks, and logos used
            on Kitco Portfolio of Websites are the trademarks, service marks, or
            logos of their respective owners.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>

        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            9. Links to and from Third Party Websites
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Websites or the Services may include links to other third party
            Websites and resources (collectively, “Linked Sites”), enabling you
            to leave the Websites or the Services in order to access them, or
            may incorporate materials or information from other Linked
            Sites.&nbsp;&nbsp;Unless otherwise expressly provided, Kitco
            provides these links, materials and information as a convenience and
            not as an endorsement or approval of Linked Site, its information,
            opinions, advice, services or products.&nbsp;&nbsp;&nbsp;Kitco is
            not responsible for the availability of the Linked Sites or the
            content or activities of such sites. If You decide to access Linked
            Sites, You do so at Your own risk. In addition, Your use of Linked
            Sites is subject to any applicable policies and terms and conditions
            of use, including but not limited to, the Linked Site&apos;s privacy
            policy.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You may not create links between the Websites or the Services and
            another site without Kitco’s prior written
            consent.&nbsp;&nbsp;Unless otherwise expressly agreed, such links
            must not imply that Kitco is associated in any way whatsoever with
            another website or that Kitco endorses or approves of its
            content.&nbsp;You acknowledge and agree that Kitco may request at
            any time, at its sole discretion, that a link be removed.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco is not responsible or liable for any third party website link
            to or from the Websites or the Services, any website link to or from
            those third party websites, the content of those websites, or their
            products or services.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco uses YouTube API services to connect the contents of Kitco’s
            YouTube Channel(s) with our sites and apps. YouTube’s Terms of
            Service can be found at&nbsp;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            <a href="https://www.youtube.com/t/terms">
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                }}
              >
                Terms of Service
              </span>
            </a>
          </span>
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            . By using those API Clients, users are agreeing to be bound by the
            YouTube Terms of Service.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Viral Content Distribution
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco may grant you—but only through express written permission—the
            limited, revocable permission to engage in certain expressly
            described personal uses of Materials as may from time to time be
            made available on the Websites for such purposes (&quot;Viral
            Distribution&quot;).
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Express written permission for Viral Distribution may include these
            personal uses: (a) sending Materials to friends or acquaintances at
            no charge; (b) posting and displaying a copy of Materials on a
            personal web site; or (c) posting and displaying a copy of Materials
            on any online bulletin board, message board, newsgroup, website or
            chat room (&quot;Third-Party Site&quot;) that permits users to post
            content, so long as the posting is allowed pursuant to the
            Third-Party Site terms and conditions, and provided that the
            Third-Party Site does not charge for access to those materials or
            associate those materials with products, services, or advertising.
            If expressly permitted and made available on the Websites, you may
            engage in Viral Distribution pursuant to these Terms, but you may
            not make any use of or license, distribute, reproduce, or otherwise
            exploit any part of the Materials without our express written
            permission.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Additionally, any limited, revocable permission to post such links
            on a Third-Party Site requires that You comply with the following
            guidelines:
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You may display only an excerpt of the content not to exceed 75
            words that must be followed by a link to the full content of the
            Website or the Service. You are not permitted to reproduce the
            entire text of content as it appears on the Website or the Service.
            You are prohibited from displaying excerpts from the Premium
            Service.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The byline must consist of the name or title of the content and the
            name of the Website or the Service. (e.g. &quot;as appearing on
            Kitco&quot;)
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You may not not suggest or imply that Kitco is sponsoring or
            endorsing any Third-Party Site or its products, unless Kitco has
            given its prior written consent.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You may not misrepresent any state of facts, including the
            relationship of the Third-Party Site with Kitco or any of its
            affiliates.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You may not present false information about Kitco products or
            services.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You may not use any logo or mark of Kitco or any of its affiliates
            without express written permission from Kitco.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml38, styles.textIndent)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '10.0pt',
              fontFamily: 'Symbol',
              color: '#7F7F7F',
            }}
          >
            <span>
              ·
              <span
                style={{
                  fontSize: '7pt',
                  fontFamily: 'Times New Roman',
                }}
              >
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </span>
            </span>
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You must not display any excerpt of Kitco content linked together
            with content on such Third-Party Site that may be construed as
            distasteful, offensive or controversial.
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          />
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Notwithstanding anything contained herein, we reserve the right to
            deny You permission to link to the website for any reason in our
            sole and absolute discretion.
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            10. Downloads, Software and Applications
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco may make some Services available for download from the
            Websites or from third party websites (the “Downloaded
            Services”).&nbsp;&nbsp;Any and all Downloaded Services are the
            copyrighted work of, and owned by, Kitco and&nbsp;its affiliates and
            related companies.&nbsp;
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco grants you a personal, limited, revocable, non-exclusive,
            non-transferable license to use the Downloaded Services for your
            personal, non-commercial use only, provided that you not modify the
            Downloaded Services, that you maintain all copyright and other
            proprietary notices, and that Kitco, or any other third parties,
            retain full and complete title to the Downloaded Services and all
            intellectual property rights therein.
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You acknowledge and agree that the Downloaded Services are protected
            by copyright laws, and that, without limiting the generality of the
            foregoing, copying, re-selling, disassembling, redistributing or
            reproducing the Downloaded Services is strictly prohibited.&nbsp;
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            You acknowledge and agree that it is your responsibility to review,
            assess and evaluate the Downloaded Services and any other related
            terms and conditions to which the Downloaded Services may be
            subject, and which you may be provided with during the download
            process, and that all risk associated with the downloading and use
            of the Downloaded Services rests with
            you.&nbsp;&nbsp;Kitco,&nbsp;its affiliates and related
            companies,&nbsp;will not be responsible or liable for any damages,
            difficulties or consequences encountered as a result of or during
            the download process or the use of or reliance on the Downloaded
            Services.&nbsp;
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            11. Modifications
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco reserves the right, at its sole discretion, to add to, remove
            or change these Terms of Use without notice at any
            time.&nbsp;&nbsp;Except as otherwise expressly provided, changes
            will be effective immediately upon posting on the
            Websites.&nbsp;&nbsp;Review these Website Terms of Use
            regularly.&nbsp;&nbsp;Your continued use of the Websites or any of
            the Services after any such changes are posted will mean you accept
            the changes.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml20)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco also reserves the right to unilaterally amend or withdraw any
            information, products or services provided or described on the
            Websites and the Services without notice at any time.
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoListParagraph)}
          style={{
            marginTop: '6.0pt',
            marginRight: '0cm',
            marginBottom: '12.0pt',
            marginLeft: '0cm',
            lineHeight: 'normal',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '16.0pt',
              fontFamily: '"Lato",sans-serif',
              color: '#373737',
            }}
          >
            12. Miscellaneous
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            12.1.&nbsp;&nbsp;&nbsp;&nbsp;
            <u>Advertising, Third Party Content and other Web Sites</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Websites may contain advertising or other third party content.
            Advertisers and other content providers are responsible for ensuring
            that such material complies with international and national law.
            Kitco is not responsible for any third party content or error, or
            for any omission or inaccuracy in any advertising material. The Site
            and/or Digital Applications may also contain links to other web
            sites. Kitco is not responsible for the availability of these
            websites or their content.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            12.2.&nbsp;&nbsp;&nbsp;&nbsp;<u>Changes to Terms and Conditions</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            Kitco reserves the right to amend these Terms of Use for all
            Services, any applicable fees and charges, or any Services, rights
            or obligations provided for herein at its sole discretion, at any
            time, without prior notice to You. Acceptance of these Terms and
            Conditions is limited to the acknowledged transaction and is not
            applicable to any future transactions. You will be responsible for
            accepting the Terms and Conditions for every subsequent transaction.
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            12.3.&nbsp;&nbsp;&nbsp;&nbsp;<u>Applicable Law and Jurisdiction</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The Website Terms of Use are construed in accordance with and
            governed by the laws applicable in the Province of Quebec and the
            laws of Canada applicable therein. The Parties hereby irrevocably
            submit to the non-exclusive jurisdiction of the Courts of the
            province of Quebec in respect of all matters or disputes arising
            from the use of the Websites or of any of the Services, except as
            otherwise specifically stated herein.
          </span>
        </p>
        <Break />
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '6.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#373737',
              marginLeft: '17.0pt',
            }}
          >
            12.4.&nbsp;&nbsp;&nbsp;&nbsp;<u>Language</u>
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            The parties hereto have expressly required that this agreement and
            all deeds, documents or notices relating thereto be executed in the
            English language.&nbsp;&nbsp; Les parties aux présentes ont
            expressément convenu que cette entente et tout autre acte, document
            ou avis y afférent soient rédigés en anglais.
          </span>
        </p>
        <p
          className={clsx(styles.MsoNormal)}
          style={{
            marginBottom: '0pt',
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
          }}
        >
          <span
            lang="EN-US"
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            &nbsp;
          </span>
        </p>
        <SectionSubTitle>
          12.5.&nbsp;&nbsp;&nbsp;&nbsp;<u>Privacy</u>
        </SectionSubTitle>
        <p
          className={clsx(styles.MsoNormal, styles.ml45)}
          style={{
            textAlign: 'justify',
            textJustify: 'inter-word',
            lineHeight: '16.0pt',
            marginBottom: '0pt',
          }}
        >
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
              float: 'left',
            }}
          >
            Kitco respects your privacy.&nbsp;&nbsp;Our privacy policy is
            available at:&nbsp;
          </span>
          <span
            style={{
              fontSize: '12.0pt',
              fontFamily: '"Mulish",serif',
              color: '#7F7F7F',
            }}
          >
            <a href="https://online.kitco.com/help/privacy_policy.html">
              <span
                style={{
                  fontSize: '12.0pt',
                  fontFamily: '"Mulish",serif',
                  color: '#95B3D7',
                  textDecoration: 'underline',
                  wordBreak: 'break-word',
                }}
              >
                https://online.kitco.com/help/privacy_policy.html
              </span>
            </a>
          </span>
        </p>
        <Break />
      </div>
    </LayoutNoTopAdvertisement>
  )
}

const SectionTitle = ({ children }) => (
  <p
    className={clsx(styles.MsoListParagraph)}
    style={{
      marginTop: '6.0pt',
      marginRight: '0cm',
      marginBottom: '12.0pt',
      marginLeft: '0cm',
      lineHeight: 'normal',
    }}
  >
    <span
      lang="EN-US"
      style={{
        fontSize: '16.0pt',
        fontFamily: '"Lato",sans-serif',
        color: '#373737',
      }}
    >
      {children}
    </span>
  </p>
)

const SectionSubTitle = ({ children }) => (
  <p
    className={clsx(styles.MsoNormal)}
    style={{
      textAlign: 'justify',
      textJustify: 'inter-word',
      lineHeight: '16.0pt',
      marginBottom: '6.0pt',
    }}
  >
    <span
      lang="EN-US"
      style={{
        fontSize: '12.0pt',
        fontFamily: '"Mulish",serif',
        color: '#373737',
        marginLeft: '17.0pt',
      }}
    >
      {children}
    </span>
  </p>
)

const Text = ({ children }) => (
  <p
    className={clsx(styles.MsoNormal, styles.ml45)}
    style={{
      textAlign: 'justify',
      textJustify: 'inter-word',
      lineHeight: '16.0pt',
      marginBottom: '0pt',
    }}
  >
    <span
      style={{
        fontSize: '12.0pt',
        fontFamily: '"Mulish",serif',
        color: '#7F7F7F',
      }}
    >
      {children}
    </span>
  </p>
)

const Break = () => (
  <p
    className={clsx(styles.MsoNormal)}
    style={{
      marginBottom: '0pt',
      textAlign: 'justify',
      textJustify: 'inter-word',
      lineHeight: '16.0pt',
    }}
  >
    <span
      lang="EN-US"
      style={{
        fontSize: '12.0pt',
        fontFamily: '"Mulish",serif',
        color: '#7F7F7F',
      }}
    >
      &nbsp;
    </span>
  </p>
)
