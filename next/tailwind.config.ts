import type { Config } from 'tailwindcss'
import colors from 'tailwindcss/colors'

/** @type {import('tailwindcss').Config} */
// prettier-ignore
module.exports = {
  content: [
    './components/**/*.{js,ts,jsx,tsx}',
    './containers/**/*.{js,ts,jsx,tsx}',
    './pages/**/*.{js,ts,jsx,tsx}',
    './src/**/*.{js,ts,jsx,tsx}',
    './cells/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    screens: {
      'native-sm': '731px',
      sm: '640px',
      md: '768px',
      tablet: '768px',
      lg: '1024px',
      desktop: '1270px',
      xl: '1280px',
      amqdAds: '1320px',
      '2xl': '1536px',
    },
    extend: {
      animation: {
        'scale-pulse': 'scale-pulse 2s forwards',
      },
      keyframes: {
        'scale-pulse': {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
          '100%': { transform: 'scale(1)' },
        },
      },
      boxShadow: {
        default:
          '0px 0px 3px rgba(20, 20, 20, 0.15), 0px 1px 4px rgba(20, 20, 20, 0.05)',
        noshadow: '0px 0px 0px rgba(0,0,0,0)',
      },
      colors: {
        gray: {
          ...colors.gray,
          600: '#979797',
          900: '#373737',
          summary: colors.gray['500'],
          date: colors.gray['400'],
          bggray: '#F8F8F8',
          black: '#373737',
        },
        'accent-1': '#333',
        'alt-row': '#f5f5f5',
        'gray-date': colors.gray['400'],
        'gray-summary': colors.gray['500'],
        'kitco-black': '#232323',
        'ktc-black': '#373737',
        'ktc-blue': '#0A87D2',
        'ktc-borders': '#E5E5E5',
        'ktc-category': '#0C88D4',
        'ktc-date-gray': '#757575',
        'ktc-desc-gray': '#838383',
        'ktc-gold': '#ECB30E',
        'ktc-gray': '#11111180',
        'ktc-hover': '#5E5E5E',
        'ktc-icon-black': '#111111',
        'ktc-summary-gray': '#4D4D4D',
      },
      gridTemplateColumns: {
        'layout-2': 'minmax(auto, 1fr) 300px',
        'layout-5': '110px 1fr 100px 1fr 1fr',
        'layout-10': '280px minmax(auto, 1fr)',
      },
      width: {
        '6.5/10': '65.5%',
        '3.5/10': '34.5%',
      },
      lineClamp: {
        12: '12',
      },
    },
    fontFamily: {
      lato: 'Lato, sans-serif',
      arial: ['Arial, Helvetica, sans-serif'],
    },
  },
  variants: {},
  plugins: [require('@tailwindcss/typography')],
} satisfies Config
