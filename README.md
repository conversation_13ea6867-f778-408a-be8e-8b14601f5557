# Kitco Frontend

This is the frontend for the Kitco website.

It is built using NextJS, Tailwind and GraphQL and deployed to Google Kubernetes using Helm using CircleCI.

## Important

When deploying note that if you're making changes to varnish you will need to manually delete the varnish pods.

If this is not done the varnish configuration will not be updated.

## Getting Started

### Prerequisites

start the project.
we're just a script from the package.json
all env vars are in both the circle config as well .env.development

```
cd next/
yarn dev

# If you want to use SSL locally
yarn dev:ssl
```

### Local Kubernetes

Start the project (Install/update helm chart)

```
./deploy_helm.sh -a deploy
```

Generate the Helm template for testing before deploying

```
./deploy_helm.sh -a template
```

Pause the project (does not delete it, just pauses deployment)

```
./deploy_helm.sh -a pause
```

Delete project (Deletes the deployment)

```
./deploy_helm.sh -a remove
```

We're using [gql-codegen](https://graphql-code-generator.com/docs/getting-started/index) in order to get generated types
and hooks for more declarative code.
All data fetches to be stored in factory objects like so:
(Please take note of the empty Generic for `args`)

Step 1. create query i.e.

```typescript
// src/lib/news-factory.lib-ts

const news = {
  categoriesTree: (args: <>) => {
    return {
      ...args?.options,
      queryKey: ["categoriesTree", args?.variables],
      queryFn: gql`
				query CategoriesTree {
					categoriesTree {
						id
						name
						urlAlias
					}
				}
			`
    }
  }
}
  `;
```

Step 2.

```bash
yarn generate
```

Step 3.
Add the generated types to the query

```typescript
// src/lib/news-factory.lib-ts
import type { CategoriesTreeQuery, CategoriesTreeQueryVariables } from '~/src/core/generated'

const news = {
  categoriesTree: (args: <CategoriesTreeQuery, CategoriesTreeQueryVariables>) => {
    return {
      ...args?.options,
      queryKey: ["categoriesTree", args?.variables],
      queryFn: gql`
				query CategoriesTree {
					categoriesTree {
						id
						name
						urlAlias
					}
				}
			`
    }
  }
}
  `;
```

Step 4. (pt 1.)
Usage on the client

```typescript
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { news } from "~/src/lib/news-factory.ts";

const { data } = kitcoQuery(
  news.categoriesTree({
    variables: { ...incoming?.vars }
  })
);
```

Step 4. (pt 2.)
Usage on the server

```typescript
import { news } from "~/src/lib/news-factory.ts";
import { kitcoQueryClient } from '~/src/services/database/kitcoQuery'

export const getServerSideProps: GetServerSideProps = async () => {
  const client = kitcoQueryClient()

  const { queryKey, queryFn } = news.categoriesTree()

  await client.prefetch(queryKey, queryFn)

  return {
    props: {
      // why all the stringify shenanigans? Well its because nextjs sucks at handling types that arent easily serialized
      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),
    }
  }
}
```
