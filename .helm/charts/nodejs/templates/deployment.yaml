apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "templ.fullname" . }}
  labels:
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicas }}
{{- end }}
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: {{ template "templ.fullname" . }}
  template:
    metadata:
      labels:
        app: {{ template "templ.fullname" . }}
    spec:
      nodeSelector:
{{ toYaml .Values.nodeSelector | indent 8 }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
      containers:
      - name: {{ .Values.containerName }}
        resources:
{{ toYaml .Values.resources | indent 10 }}
        image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
        env:
        - name: "NODE_ENV"
          value: {{ .Values.nodeEnv | quote }}
        - name: "NODE_PORT"
          value: {{ .Values.port | quote }}
        - name: "NODE_HOST"
          value: {{ .Values.host | quote }}
        {{- if .Values.extraEnvs }}
{{ toYaml .Values.extraEnvs | indent 8 }}
        {{- end }}
        ports:
        - containerPort: {{ .Values.port }}
        workingDir: /usr/src/app
        {{- if .Values.livenessProbe.enabled }}
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f next-server"
          initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
          failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
        {{- end }}
        {{- if .Values.volume.enabled }}
        command:
{{ toYaml .Values.volume.command | indent 10 }}
        args:
{{ toYaml .Values.volume.commandArgs | indent 10 }}
        volumeMounts:
        - mountPath: /usr/src/app
          name: app
        - mountPath: /usr/src/app/node_modules
          name: node-modules
          {{- range .Values.extraVolumeMounts }}
        - mountPath: {{ .mountPath }}
          name: {{ .name }}
          {{- end }}
      volumes:
      - name: app
        hostPath:
          path: {{ .Values.volume.hostPath }}
      - name: node-modules
        emptyDir: {}
        {{- range .Values.extraVolumes }}
      - name: {{ .name }}
        hostPath:
          path: {{ .hostPath }}
        {{- end }}
        {{- end }}
