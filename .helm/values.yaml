nextjs:
  image:
    repository:
    tag:
  extraEnvs:
  ingress:
    enabled:
    hostname:
    tls:
      enabled:
  volume:
    enabled:
    hostPath:
    command:
    commandArgs:

extra:

frontendProdRedirect:
  enabled: false

varnish:
  enabled: true
  backend:
    host: nextjs
    port: 80
  configMapName: "nextjs-varnish"
  image:
    repository: "favish/varnish"
    tag: 2.0.1
  secret: "9E4D6Rgy8"
