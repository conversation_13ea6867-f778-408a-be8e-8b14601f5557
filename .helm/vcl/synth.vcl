sub vcl_synth {

    # handle unauthorized (Basic Auth)
    if (resp.status == 401) {
        set resp.http.WWW-Authenticate = "Basic realm='Kitco Restricted Area'";
        return(deliver);
    }

    # handle not found
    if (resp.status == 829) {
        set resp.status = 302;
        set resp.reason = "Found";
        set resp.http.cache-control = "max-age=86400";
        set resp.http.Location = req.http.X-Proto + "://" + req.http.host + req.url;

        return (deliver);
    }

    # handle not found
    if (resp.status == 404) {
        return (deliver);
    }

    # PURGE request error
    if (resp.status == 405) {
        return (deliver);
    }

    # handle redirects
    if (resp.status == 619) {
        set resp.http.Location = req.http.X-Redirect-URL;
        return (deliver);
    }

    # Forex redirect
    if (resp.status == 620 && resp.reason == "Redirect") {
        set resp.status = 301;
        set resp.http.Location = req.http.X-Proto + "://" + req.http.host + "/price/forex/" + req.http.X-Forex-Symbol;
        return (deliver);
    }

    # old ulr redirects
    if (resp.status == 678 && resp.reason == "Redirect") {
        set resp.status = 308;
        set resp.http.Location = req.http.X-Proto + "://" + req.http.host + req.http.X-Value + req.http.X-Query-String;

        # Strip a trailing ? if it exists
        if (resp.http.Location ~ "\?$") {
            set resp.http.Location = regsub(resp.http.Location, "\?$", "");
        }

        unset req.http.X-Value;

        return (deliver);
    }

    # permanent redirects
    if (resp.status == 681 && resp.reason == "Redirect") {
        set resp.status = 301;
        set resp.http.Location = req.http.X-Proto + "://" + req.http.host + req.http.X-Value + req.http.X-Query-String;

        # Strip a trailing ? if it exists
        if (resp.http.Location ~ "\?$") {
            set resp.http.Location = regsub(resp.http.Location, "\?$", "");
        }

        unset req.http.X-Value;
        return (deliver);
    }

    # found (temporarily) redirects
    if (resp.status == 682 && resp.reason == "Redirect") {
        set resp.status = 302;
        set resp.http.Location = req.http.X-Proto + "://" + req.http.host + req.http.X-Value + req.http.X-Query-String;
        unset req.http.X-Value;

        # Strip a trailing ? if it exists
        if (resp.http.Location ~ "\?$") {
            set resp.http.Location = regsub(resp.http.Location, "\?$", "");
        }

        return (deliver);
    }

    # off site redirects
    if (resp.status == 691 && resp.reason == "Redirect") {
        set resp.status = 301;
        set resp.http.Location = req.http.X-Value;
        unset req.http.X-Value;

        # Strip a trailing ? if it exists
        if (resp.http.Location ~ "\?$") {
            set resp.http.Location = regsub(resp.http.Location, "\?$", "");
        }

        return (deliver);
    }

    # Permanent redirect to WWW
    if (resp.status == 830) {
        set resp.status = 301;
        set resp.reason = "Moved Permanently";
        set resp.http.cache-control = "max-age=86400";
        set resp.http.Location = req.http.X-Proto + "://" + req.http.host + req.url;
        return (deliver);
    }
}
