apiVersion: apps/v1
kind: Deployment
metadata:
    name: nginx-rss-proxy
spec:
    replicas: {{ .Values.rss.replicas | default 1 }}
    selector:
        matchLabels:
            app: nginx-rss-proxy
    template:
        metadata:
            labels:
                app: nginx-rss-proxy
        spec:
            containers:
              - name: nginx
                image: nginx:alpine
                volumeMounts:
                  - name: nginx-config-volume
                    mountPath: /etc/nginx/conf.d
                livenessProbe:
                    httpGet:
                        path: /hc
                        port: 80
                    initialDelaySeconds: 15
                    periodSeconds: 10
                    failureThreshold: 5
                    successThreshold: 1
                    timeoutSeconds: 3
                resources:
                    {{- toYaml .Values.rss.resources | nindent 20 }}
            volumes:
              - name: nginx-config-volume
                configMap:
                    name: nginx-rss-cm
