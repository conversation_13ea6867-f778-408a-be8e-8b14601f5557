apiVersion: v1
kind: ConfigMap
metadata:
    name: nginx-rss-cm
data:
    default.conf: |
        resolver ******* *******;
        server {
            listen 80;
            location ~ ^/(?!news\/rss\/kitconewsfeedextended.xml|rss) {
                return 301 https://www.kitco.com/news;
            }
            location /news/rss/kitconewsfeedextended.xml {
                proxy_pass https://{{ .Values.rss.targetHost }}/rss/kitconewsfeedextended.xml;
                proxy_set_header Host {{ .Values.rss.targetHost }};
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }
            location /rss {
                proxy_pass https://{{ .Values.rss.targetHost }}$request_uri;
                proxy_set_header Host {{ .Values.rss.targetHost }};
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }
            location /hc {
                return 200 "OK";
            }
        }
