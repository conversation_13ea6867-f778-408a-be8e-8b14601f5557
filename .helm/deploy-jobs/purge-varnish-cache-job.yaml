apiVersion: batch/v1
kind: Job
metadata:
  name: purge-varnish-cache
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded,hook-failed
spec:
  completions: 1
  template:
    spec:
      containers:
      - name: purge-cache
        image: appropriate/curl:latest
        command: ["sh", "-c"]
        env:
          - name: VARNISH_SERVERS
            value: "%%VARNISH_SERVERS%%"
        args:
          - |
            echo "purge-varnish-cache job"
            echo $VARNISH_SERVERS
            for server in $VARNISH_SERVERS; do
              curl -X PURGE -H "X-Varnish-Purge: 9E4D6Rgy8" $server
            done
      restartPolicy: Never
  backoffLimit: 4
