nextjs:
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targets:
      cpuUtilizationPercentage: 70
      memoryUtilizationPercentage: 70
  basicAuth:
    enabled: false
  image:
    repository: "us.gcr.io/kitco-224816/kitco-frontend"
  nodeEnv: "production"
  extraEnvs:
  livenessProbe:
    enabled: true
    path: "/api/health"
  resources:
    requests:
      cpu: 1
      memory: 1Gi
    limits:
      cpu: 2
      memory: 4Gi
  ingress:
    enabled: true
    hostname: "kitco.com"
    redirect_www: true
    service:
      name: varnish
      port: 80
    annotations:
      nginx.ingress.kubernetes.io/blacklist-source-range: *********/32,*********/32,*********/32
      # Redirect all HTTP requests to HTTPS
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      # Redirect non-www to www
      nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
    tls:
      enabled: true
      secretName: "nextjs-frontend-tls-secret"

frontendProdRedirect:
  enabled: true

varnish:
  replicas: 3
  image:
    repository: us.gcr.io/kitco-224816/favish/varnish-frontend
    tag: 1.0.1
  backend:
    host: kitco-frontend-prod-nextjs
  resources:
    requests:
      cpu: 300m
      memory: 2Gi
    limits:
      memory: 4Gi
      cpu: 2
  # Extra Varnish Configuration for templating
  extra:
    # Drupal Host
    drupalHost: "cms.prod.kitco.com"

    # Enable/Disable Basic Auth
    enableBasicAuth: false

    # Main Hostname
    hostname: "www.kitco.com"

    # Enable/Disable Redirect to www
    redirect_www: true
    # Domain without www for comparison
    redirect_www_hostname: "kitco.com"

    # Sitemap Bucket for Google Storage
    sitemapBucket: "kitco-cms-prod.storage.googleapis.com"

rss:
  resources:
    requests:
      cpu: 100m
      memory: 100M
    limits:
      cpu: 1
      memory: 1G
  ingressHost: news.kitco.com
  ingressAnnotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    certmanager.k8s.io/cluster-issuer: "letsencrypt-prod"
  targetHost: cms.prod.kitco.com
  tls:
    enabled: true
    host: news.kitco.com
