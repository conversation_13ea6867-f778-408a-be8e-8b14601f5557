{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "organizeImports": {"enabled": false}, "linter": {"enabled": true, "rules": {"style": {"useNodejsImportProtocol": "off"}, "correctness": {"useExhaustiveDependencies": "warn"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80, "ignore": []}, "javascript": {"formatter": {"arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true, "quoteStyle": "single", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "semicolons": "asNeeded", "trailingCommas": "all"}}}