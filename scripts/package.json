{"name": "kitco-frontend-scripts", "type": "module", "packageManager": "yarn@4.5.0", "scripts": {"build": "tsc", "format": "biome format ./src --write & prettier ./src --write --log-level warn", "generate-users": "node src/generateUserLinks.cjs", "lint": "eslint './src/**/*.{js,jsx,ts,tsx}'", "sync-firebase": "tsc && node dist/sync-firebase.js", "type-check": "tsc --pretty", "update-users-email": "node dist/update-users-email.js"}, "dependencies": {"csv-parser": "^3.0.0", "dotenv": "^16.4.5", "firebase-admin": "^12.6.0", "json2csv": "^6.0.0-alpha.2", "object-hash": "^3.0.0"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/eslint": "^9", "@types/eslint__js": "^8.42.3", "@types/node": "^22.7.4", "@types/object-hash": "^3", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "biome": "^0.3.3", "eslint": "^9.11.1", "eslint-plugin-import": "^2.30.0", "prettier": "^3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "typescript": "^5.6.2", "typescript-eslint": "^8.8.0"}}