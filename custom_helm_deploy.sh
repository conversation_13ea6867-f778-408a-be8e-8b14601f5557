#!/usr/bin/env bash

# Default namespace
DEFAULT_NAMESPACE="kitco-frontend-local"

# Function to deploy the Helm chart
deploy_helm() {
  local namespace=$1
  local dir=$2

  helm upgrade --install --namespace="$namespace" \
    --values ./.circleci/environments/local/values.yml \
    --set nextjs.volume.hostPath="${dir}/next" \
    --timeout 3600s \
    --debug \
    --wait \
    "$namespace" \
    .helm

  echo ""
  echo ""
  echo "Deployment complete"
  echo "Kitco Frontend .. http://frontend.local.favish.com/"
}

# Function to generate the Helm template
template_helm() {
  local namespace=$1
  local dir=$2

  helm template --namespace="$namespace" \
    --values ./.circleci/environments/local/values.yml \
    --set nextjs.volume.hostPath="${dir}/next" \
    --timeout 3600s \
    --debug \
    --wait \
    "$namespace" \
    .helm
}

# Export the function and DEFAULT_NAMESPACE for use in other scripts
export DEFAULT_NAMESPACE
export -f deploy_helm
